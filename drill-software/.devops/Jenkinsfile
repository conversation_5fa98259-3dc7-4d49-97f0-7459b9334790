import groovy.json.JsonSlurperClassic

def config = [
    docker: [
        docker_dev_number: "registry.gitlab.com/vistmt/drill-software/develop:${BUILD_NUMBER}",
        docker_dev_latest: "registry.gitlab.com/vistmt/drill-software/develop:latest",
        docker_prod_number: "registry.gitlab.com/vistmt/drill-software/production:${BUILD_NUMBER}",
        docker_prod_latest: "registry.gitlab.com/vistmt/drill-software/production:latest"
    ],
    git: [
        url: "**************:vistmt/drill-software.git",
        branch: "${GIT_BRANCH}"
//     ],
//     sonar: [
//       projectKey: "drill-software",
//       host_url: "http://jenkins-zr.zyfra.com:9000",
//       python_version: "3.7"
    ]
]

pipeline {
    agent any
    parameters{
      gitParameter name: 'GIT_BRANCH',
                   type: 'PT_BRANCH_TAG',
                   branchFilter: 'origin/(.*)',
                   defaultValue: 'develop',
                   selectedValue: 'DEFAULT',
                   sortMode: 'ASCENDING_SMART',
	               description: 'Select your branch.'
    }
    stages {
//    ######### Checking selected branch #########
        stage('check-branch') {
            steps {
                checkout([$class: 'GitSCM',
                          branches: [[name: "${params.GIT_BRANCH}"]],
                          doGenerateSubmoduleConfigurations: false,
                          extensions: [[$class: 'SubmoduleOption',
                            disableSubmodules: false,
                            parentCredentials: false,
                            recursiveSubmodules: true,
                            reference: '',
                            timeout: 4,
                            trackingSubmodules: false]],
                          userRemoteConfigs: [[url: config.git.url,credentialsId: 'Jenkins_gitlab_token',]]
                ])
            }
        }
        stage('dev_image_build') {
            when {
              expression {return params.GIT_BRANCH != 'master'}
            }
            steps {
                ansiColor('xterm') {
                    sh"docker build --pull -f Dockerfile -t ${config.docker.docker_dev_number} -t ${config.docker.docker_dev_latest} ."
                    sh"docker push ${config.docker.docker_dev_number}"
                    sh"docker push ${config.docker.docker_dev_latest}"
                }
            }
        }
        stage('prod_image_build') {
            when {
              expression {return params.GIT_BRANCH == 'master'}
            }
            steps {
                ansiColor('xterm') {
                    sh"docker build --pull -f Dockerfile -t ${config.docker.docker_prod_number} -t ${config.docker.docker_prod_latest} ."
                    sh"docker push ${config.docker.docker_prod_number}"
                    sh"docker push ${config.docker.docker_prod_latest}"
                }
            }
        }
////     ######### Python code quality #########
//        stage('SonarQube Analysis') {
//          steps {
//            withCredentials([string(credentialsId: 'SONARQUBE_TOKEN', variable: 'SONARQUBE_TOKEN')]) {
//              sh "sonar-scanner \
//                 -Dsonar.projectKey=${config.sonar.projectKey} \
//                 -Dsonar.sources=. \
//                 -Dsonar.host.url=${config.sonar.host_url} \
//                 -Dsonar.python.version=${config.sonar.python_version} \
//                 -Dsonar.scm.exclusions.disabled=true \
//                 -Dsonar.login=${SONARQUBE_TOKEN}"
//            }
//          }
//        }
//         stage('clean-ws') {
//             steps {
//                   cleanWs()
//             }
//         }
    }
}

