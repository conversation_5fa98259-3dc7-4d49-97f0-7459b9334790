import groovy.json.JsonSlurperClassic

def config = [
    docker: [
        docker_dev_number: "registry.gitlab.com/vistmt/drill-software/develop:${BUILD_NUMBER}",
        docker_dev_latest: "registry.gitlab.com/vistmt/drill-software/develop:latest",
        docker_prod_number: "registry.gitlab.com/vistmt/drill-software/production:${BUILD_NUMBER}",
        docker_prod_latest: "registry.gitlab.com/vistmt/drill-software/production:latest"
    ]
]

pipeline {
    agent any
    stages {
        stage('dev_image_build') {
            when {
              expression {return env.GIT_BRANCH != 'origin/master'}
            }
            steps {
                ansiColor('xterm') {
                    sh"docker build --pull -f Dockerfile -t ${config.docker.docker_dev_number} -t ${config.docker.docker_dev_latest} ."
                    sh"docker push ${config.docker.docker_dev_number}"
                    sh"docker push ${config.docker.docker_dev_latest}"
                }
            }
        }
        stage('prod_image_build') {
            when {
              expression {return env.GIT_BRANCH == 'origin/master'}
            }
            steps {
                ansiColor('xterm') {
                    sh"docker build --pull -f Dockerfile -t ${config.docker.docker_prod_number} -t ${config.docker.docker_prod_latest} ."
                    sh"docker push ${config.docker.docker_prod_number}"
                    sh"docker push ${config.docker.docker_prod_latest}"
                }
            }
        }
    }
//     ######### Telegram notification #########
    post {
        success {
//            notifyEvents message: "Build <b>$BUILD_URL</b> successful. Sonarqube: <b>${config.sonar.host_url}/dashboard?id=${config.sonar.projectKey}</b>", token: 'UZGLTCgUhutsp-uqWPVTYnxDKrk7d2oQ'
            notifyEvents message: "Build <b>$BUILD_URL</b> successful. Project <b>$GIT_URL</b>. Branch <b>$GIT_BRANCH</b>", token: 'UZGLTCgUhutsp-uqWPVTYnxDKrk7d2oQ'
        }
        unstable {
//            notifyEvents message: "Build <b>$BUILD_URL</b> successful, but unstable. Please check it. Sonarqube: <b>${config.sonar.host_url}/dashboard?id=${config.sonar.projectKey}</b>", token: 'UZGLTCgUhutsp-uqWPVTYnxDKrk7d2oQ'
            notifyEvents message: "Build <b>$BUILD_URL</b> successful, but unstable. Please check it. Project <b>$GIT_URL</b>. Branch <b>$GIT_BRANCH</b>", token: 'UZGLTCgUhutsp-uqWPVTYnxDKrk7d2oQ'
        }
        failure {
//            notifyEvents message: "ALARM!!! Build <b>$BUILD_URL</b> FAILED!!!. Please fix it NOW!!! Project <b>$GIT_URL</b>. Branch <b>$GIT_BRANCH</b>", token: 'UZGLTCgUhutsp-uqWPVTYnxDKrk7d2oQ'
            notifyEvents message: "ALARM!!! Build <b>$BUILD_URL</b> FAILED!!!. Please fix it NOW!!! Project <b>$GIT_URL</b>. Branch <b>$GIT_BRANCH</b>", token: 'UZGLTCgUhutsp-uqWPVTYnxDKrk7d2oQ'
        }
    }
}

