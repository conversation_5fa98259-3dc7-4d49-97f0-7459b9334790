image: docker:stable

variables:
  # When using dind service we need to instruct docker, to talk with the
  # daemon started inside of the service. The daemon is available with
  # a network connection instead of the default /var/run/docker.sock socket.
  #
  # The 'docker' hostname is the alias of the service container as described at
  # https://docs.gitlab.com/ee/ci/docker/using_docker_images.html#accessing-the-services
  #
  # Note that if you're using Kubernetes executor, the variable should be set to
  # tcp://localhost:2375 because of how Kubernetes executor connects services
  # to the job container
  DOCKER_HOST: tcp://docker:2375/
  # When using dind, it's wise to use the overlayfs driver for
  # improved performance.
  DOCKER_DRIVER: overlay2

services:
  - docker:dind

catkin_make:
  stage: build
  image: registry.gitlab.com/vistmt/qarl_mover:tests
  cache:
    key: "$CI_PIPELINE_ID"
    paths:
      - .
    policy: push

  before_script:
      - source /opt/ros/kinetic/setup.bash
      - cd ..
      - rm -rf catkin_ws

      - mkdir -p catkin_ws/src
      - ls
      - cp -r common catkin_ws/src
      
      #- git clone https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com/vistmt/vmt_tools
      #- cp -r vmt_tools catkin_ws/src

      - cd catkin_ws
      - python src/common/common/scripts/catkin_deps_install.py /builds/vistmt/catkin_ws/src

  script:
    - catkin_make

catkin_make tests:
  stage: test
  image: registry.gitlab.com/vistmt/qarl_mover:tests
  cache:
    key: "$CI_PIPELINE_ID"
    paths:
      - .
    policy: pull
  script:
    - cd ..
    - ls
    - cd catkin_ws
    - source devel/setup.bash
    - catkin_make run_tests --force-cmake
    - catkin_test_results

#deploy:
#  stage: deploy
#  before_script:
#    - docker images
#  script:
#    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
#    - docker build -t registry.gitlab.com/vistmt/qarl_mover:deploy -f Dockerfile:deploy .
#    - docker push registry.gitlab.com/vistmt/qarl_mover:deploy
#  environment:
#    name: QARL
#  only:
#    - master
