FROM ubuntu:16.04

RUN apt-get update -y \
    && apt-get install -y lsb-release \
    && apt-get install -y git \
    && echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" \
        > /etc/apt/sources.list.d/ros-latest.list \
    && apt-key adv \
        --keyserver hkp://ha.pool.sks-keyservers.net:80 \
        --recv-key 421C365BD9FF1F717815A3895523BAEEB01FA116 \
    && apt-get update -y \
    && apt-get install -y ros-kinetic-desktop \
    && rosdep init \
    && rosdep update \
    && apt-get install -y \
        build-essential \
        python-rosinstall \
        python-rosinstall-generator \
        python-wstool \
    && echo "source /opt/ros/kinetic/setup.bash" >> ~/.bashrc

RUN apt-get update -y \
    && apt-get install -y python-pip

WORKDIR /catkin_ws/src/RoboD.Tru3

COPY ./requirements.txt .

RUN pip install -r requirements.txt

RUN /bin/bash -c "source /opt/ros/kinetic/setup.bash"

CMD ["/bin/bash"]
