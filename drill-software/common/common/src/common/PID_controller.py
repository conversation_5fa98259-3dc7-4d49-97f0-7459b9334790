import numpy as np
import math

class PIDController(object):
    def __init__(self,proportional_factor=0,integral_factor=0,derivative_factor=0):
        super(PIDController,self).__init__()

        self.prev_error = 0
        self.prev_error_initialized = False
        self.prev_integral = 0

        self.proportional_factor = proportional_factor
        self.integral_factor = integral_factor
        self.derivative_factor = derivative_factor

    def calc(self, error):
        proportional = self.proportional_factor * error
        integral = self.prev_integral + self.integral_factor * error
        derivative = self.derivative_factor * (error - self.prev_error) if self.prev_error_initialized else 0.0

        self.prev_error = error
        self.prev_error_initialized = True
        self.prev_integral = integral

        return proportional + integral + derivative

    def reset(self):
        self.prev_error = 0.0
        self.prev_integral = 0.0


class PID:
    def __init__(self, get_time, i_saturation, p=0, i=0, d=0, ff=0, min=-np.inf, max=np.inf, out_min=-1,
                 out_max=1, d_term_tc=0.0001, out_tc=0.0001, is_angular=False, force_zero=False, ff_rev=None, one_side_i=False):
        self.get_time = get_time

        self.proportional_factor = p
        self.integral_factor = i
        self.derivative_factor = d

        self.previous_time = None
        self.previous_error = None
        self.integral = 0

        self.i_saturation = i_saturation
        self.ff = ff

        self.feedback = None
        self.is_angular = is_angular
        self.min = min
        self.max = max
        self.out_min = out_min
        self.out_max = out_max
        self.d_old = 0
        self.out_old = 0
        self.d_term_tc = d_term_tc
        self.out_tc = out_tc
        self.force_zero = force_zero
        self.ff_rev = ff_rev
        self.one_side_i = one_side_i


    def _get_time(self):
        return self.get_time

    def update(self, sp, feedback):
        if self.previous_time is None:
            self.previous_time = self.get_time()
            return 0
        cur_time = self.get_time()
        time_delta = cur_time - self.previous_time

        if self.is_angular:
            error = norm_angle_minusPI_plusPI(sp - feedback)
        else:
            error = sp - feedback

        if abs(sp) < 0.005 and self.force_zero:
            self.integral = 0
            self.d_old = 0
            self.out_old = 0
            out_smoothed = 0
            self.previous_error = None
        else:
            proportional = error * self.proportional_factor

            self.integral += error * self.integral_factor * time_delta
            d = ((error - self.previous_error) * self.derivative_factor / time_delta) if self.previous_error is not None else 0.0
            derivative = self.d_old + (1-math.exp(-time_delta/self.d_term_tc)) * (d - self.d_old)
            self.d_old = derivative
            # Integral limitation
            if abs(self.integral) > self.i_saturation:
                self.integral = self.i_saturation * abs(self.integral) / self.integral

            if np.sign(self.integral) != np.sign(sp) and self.one_side_i:
                self.integral = 0

            #print "P: ", proportional, " I: ", self.integral, " D: ", derivative
            if sp < 0 and self.ff_rev is not None:
                out = proportional + self.integral + derivative + sp * self.ff_rev
            else:
                out = proportional + self.integral + derivative + sp * self.ff
            #print "out: ", out

            out_smoothed = self.out_old + (1-math.exp(-float(time_delta) / self.out_tc)) * (out - self.out_old)

        #print "Out smoothed: ", out_smoothed
        if sp >= self.max:
            out_smoothed = self.out_max
        elif sp <= self.min:
            out_smoothed = self.out_min

        if out_smoothed > self.out_max:
            out_smoothed = self.out_max
        elif out_smoothed < self.out_min:
            out_smoothed = self.out_min

        self.previous_error = error
        self.previous_time = cur_time

        self.out_old = out_smoothed

        return out_smoothed
