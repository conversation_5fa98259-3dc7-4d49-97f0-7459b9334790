import json
from scipy import signal
import os
import argparse
from matplotlib import pyplot as plt


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('-f', '--filename', help="file name")
    parser.add_argument('-nf', '--new_filename', help="new file name")
    #parser.add_argument('-r', '--repeat', action='store_true', help="repeat scenario")
    args, _ = parser.parse_known_args()

    with open(args.filename, "r") as f:
        trajectory = json.load(f)

        heights = [
            p['local_pose']['z']
            for p in trajectory['points']
        ]

    plt.plot(range(0, len(heights)), heights)
    #plt.show()

    #exit()
    b, a = signal.ellip(4, 0.01, 50, 0.1)
    filtered = signal.filtfilt(b, a, heights)


    delta = abs(filtered[0] - heights[0])
    filtered = [f + delta for f in filtered]
    plt.plot(range(0, len(filtered)), filtered)
    plt.show()

    for p, z in zip(trajectory['points'], filtered):
        p['local_pose']['z'] = z
        p['road_roughness'] = 0.016

    with open(args.new_filename, 'w') as f:
        f.write(json.dumps(trajectory))