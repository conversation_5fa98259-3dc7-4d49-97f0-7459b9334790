#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import rosparam
from abc import ABCMeta
import six #for abc compailibility py3 and py2.7
from collections import namedtuple
import traceback
from common_msgs.msg import InternalReport, SystemStatus, Event
from threading import RLock
import rosgraph
from traceback import format_exc
from common_msgs.srv import ParametersReloader


Status = namedtuple('Status', ('code', 'message'))

def log_decorator(log_func):
    def wrapper(self, *args, **kwargs):
        if not args:
            raise TypeError('missing 1 required positional argument: \'msg\'')

        msg = '%s: ' % self._name

        if len(args) > 1:
            msg += args[0] % args[1:]
        else:
            msg += str(args[0])

        period = kwargs.get('period', None)
        event = kwargs.get('event', '')
        log_func(self, msg, period, event)

    return wrapper

@six.add_metaclass(ABCMeta)
class BaseNode(object):
    def __init__(self, name, *args, **kwargs):
        super(BaseNode, self).__init__()
        # Имя ноды
        self._name = name
        # Статус ноды
        self.status = None

        self.logdebug_dict = {}
        self.loginfo_dict = {}
        self.logwarn_dict = {}
        self.logerr_dict = {}
        self.logfatal_dict = {}

        self.log_period = 1.5

        rospy.init_node(self._name)
        rospy.on_shutdown(self.shutdown)
        # Паблишер отчетов о состоянии ноды
        self._report_pub = rospy.Publisher(rospy.get_param('Global/topics/internal_report',
                                                           default='internal_report'),
                                           InternalReport, queue_size=100)
        self._event_pub = rospy.Publisher(rospy.get_param('Global/topics/event',
                                                           default='event'),
                                           Event, queue_size=100)
        # Подгрузка статусов из настроек
        self._report_types = rospy.get_param('Global/Reports')
        # Отчет статусом начала инициализации
        self.publish_alert(self._report_types['Info']['Initializing'], "Initializing", loglevel=1)

        try:
            # Подгрузка параметров системы
            if rospy.has_param(self._name):
                self.node_params = rosparam.get_param(self._name)
            else:
                raise Exception("Missed node parameters in settings")
            if rospy.has_param('Global'):
                self.global_params = rosparam.get_param('Global/')
            if rospy.has_param('Vehicle'):
                self.vehicle_params = rosparam.get_param('Vehicle/')


            # Подписка на статус системы
            rospy.Subscriber(rospy.get_param('Global/topics/system_status',
                                             default='system_status'),
                             SystemStatus, self.system_status_callback)

            self.parameters_reloader = rospy.Service(self._name + "ReloadParams",
                                           ParametersReloader,
                                           self._process_parameters_reloading)
            # Установка частоты
            self._ros = rospy.Rate(self.node_params['rate'])
            if 'log_period' in list(self.node_params.keys()):
                self.log_period = self.node_params['log_period']
            # Статус системы
            self.system_status_lock = RLock()
            self.sys_status = SystemStatus()
            # Инициализация
            self.initialize(*args, **kwargs)

        except Exception as e:
            # Паблиш ошибки инициализации
            self.log(format_exc(), loglevel=3)
            self.publish_alert(
                self._report_types['Not_working']['Initialization_exception'],
                "Initializing ERROR " + format_exc(),
                loglevel=4
            )
            raise e

    def initialize(self, *args, **kwargs):
        pass

    ###

    ### on start
    def starting(self):
        self.before_start()
        self.on_start()
        self.after_start()

    def on_start(self):
        pass

    def before_start(self):
        #
        self.publish_alert(self._report_types['Info']['Starting'], "Starting", loglevel=1)

    def after_start(self):
        #
        self.publish_alert(self._report_types['Info']['Started'], "Started", loglevel=1)

    ###

    ### on stop
    def shutdown(self):
        self.before_stop()
        self.on_stop()
        self.after_stop()

    def on_stop(self):
        pass

    def before_stop(self):
        #
        self.publish_alert(self._report_types['Info']['Shutting down'] if self._report_types is not None else 202,
                           "Shutting down", loglevel=1)

    def after_stop(self):
        #
        self.publish_alert(self._report_types['Info']['Shut_down'] if self._report_types is not None else 203,
                           "Shut down", loglevel=1)
        rospy.signal_shutdown('close')

    ###

    ### work
    def work(self):
        # Старт ноды
        self.starting()
        # Основной цикл
        while not rospy.is_shutdown():
            # Начальный статус - норм
            self.set_status(self._report_types['Info']['Working'], "Working")

            try:
                # Итерация
                self.do_work()
            except rospy.exceptions.ROSInterruptException as ex:
                self.log("Node: %s: interrupting" % self._name)
                self.log(ex, 3)
            except Exception as ex:
                self.log(format_exc(), loglevel=3)
                self.set_status(self._report_types['Not_working']['Runtime_exception'], traceback.format_exc())
            finally:
                if self.status.code < 300:
                    loglevel = 0
                else:
                    loglevel = 3
                self.publish_alert(self.status.code, self.status.message, loglevel)

            try:
                self._ros.sleep()
            except rospy.exceptions.ROSInterruptException as ex:
                self.log("Node: %s: interrupting" % self._name)

    def do_work(self):
        pass

    ###

    ### form reports
    def set_status(self, type, message=""):
        self.status = Status(type, message)

    def prepare_report(self, type, message=""):
        msg = InternalReport()
        msg.header.stamp = rospy.Time.now()
        msg.source = self._name
        msg.type = type
        msg.message = message
        return msg

    def publish_alert(self, type, message="", loglevel=0, event=''):
        self.status = Status(type, message)
        self.publish_alert_once(type, message, loglevel, event=event)

    def publish_alert_once(self, type, message="", loglevel=0, event=''):
        msg = self.prepare_report(type, message)
        if loglevel != 0:
            self.log(message, loglevel, event)
        self._report_pub.publish(msg)

    ###

    def check_msg_ttl(self, message):
        """Function for message delay checking.
        Only works for messages with "Header header" field.

        Takes message as an argument and checks the time in header.
        Returns True if the time exceeds current by no more than Global/MSG_TTL seconds
        or if message.header.frame_id == 'debug', otherwise it returns False.
        """
        if message is None:
            return False
        elif message.header.frame_id == 'debug':  # For manual message sending
            return True
        elif (rospy.get_time() - message.header.stamp.to_sec() <=
                rospy.get_param('Global/MSG_TTL', default=0.5)):
            return True
        else:
            self.publish_alert(self._report_types['Errors']['Incoming_data_delay'],
                    '%s message time expired' % type(message).__name__, loglevel=3)
            return False

    # Удобная Функция логирования
    def log(self, message, loglevel=1, event=''):
        if loglevel == 0:
            self.logdebug(str(message), period=self.log_period, event=event)
        elif loglevel == 1:
            self.loginfo(str(message), period=self.log_period, event=event)
        if loglevel == 2:
            self.logwarn(str(message), period=self.log_period, event=event)
        elif loglevel == 3:
            self.logerr(str(message), period=self.log_period, event=event)
        elif loglevel == 4:
            self.logfatal(str(message), period=self.log_period, event=event)
        else:
            pass

    def publish_event(self, code, msg='', level=1):
        event = Event(code=code, msg=msg, source=self._name, level=level)
        event.header.stamp = rospy.get_rostime()
        self._event_pub.publish(event)

    @log_decorator
    def logdebug(self, msg, period, event):
        if period:
            if msg not in self.logdebug_dict or rospy.get_time() - self.logdebug_dict[msg] > period:
                self.logdebug_dict[msg] = rospy.get_time()
                self.publish_event(event, msg, level=0)
                rospy.logdebug(msg)
        else:
            self.publish_event(event, msg)
            rospy.logdebug(msg)

    @log_decorator
    def loginfo(self, msg, period, event):
        if period:
            if msg not in self.loginfo_dict or rospy.get_time() - self.loginfo_dict[msg] > period:
                self.loginfo_dict[msg] = rospy.get_time()
                self.publish_event(event, msg, level=2)
                rospy.loginfo(msg)
        else:
            self.publish_event(event, msg)
            rospy.loginfo(msg)

    @log_decorator
    def logwarn(self, msg, period, event):
        if period:
            if msg not in self.logwarn_dict or rospy.get_time() - self.logwarn_dict[msg] > period:
                self.logwarn_dict[msg] = rospy.get_time()
                self.publish_event(event, msg, level=4)
                rospy.logwarn(msg)
        else:
            self.publish_event(event, msg)
            rospy.logwarn(msg)

    @log_decorator
    def logerr(self, msg, period, event):
        if period:
            if msg not in self.logerr_dict or rospy.get_time() - self.logerr_dict[msg] > period:
                self.logerr_dict[msg] = rospy.get_time()
                self.publish_event(event, msg, level=8)
                rospy.logerr(msg)
        else:
            self.publish_event(event, msg)
            rospy.logerr(msg)

    @log_decorator
    def logfatal(self, msg, period, event):
        if period:
            if msg not in self.logfatal_dict or rospy.get_time() - self.logfatal_dict[msg] > period:
                self.logfatal_dict[msg] = rospy.get_time()
                self.publish_event(event, msg, level=8)
                rospy.logfatal(msg)
        else:
            self.publish_event(event, msg)
            rospy.logfatal(msg)

    def system_status_callback(self, message):
        with self.system_status_lock:
            self.sys_status = message

    def _process_parameters_reloading(self, request):
        """
        Parameters reload request handler.
        @param request<common.ParametersReloader>
        """
        if request.global_parameters:
            self.load_global_parameters()
        if request.vehicle_parameters:
            self.load_vehicle_parameters()
        if request.node_parameters:
            self.load_node_parameters()

        reinit_params = getattr(self, "initialize_params", None)
        if callable(reinit_params):
            self.initialize_params()
        else:
            print((self._name, "has no reinit params"))

        return True

    def load_global_parameters(self):
        if rospy.has_param('Global/'):
            self.global_params = rosparam.get_param('Global/')

    def load_vehicle_parameters(self):
        if rospy.has_param('Vehicle/'):
            self.vehicle_params = rosparam.get_param('Vehicle/')

    def load_node_parameters(self):
        if rospy.has_param(self._name):
            self.node_params = rosparam.get_param(self._name)
