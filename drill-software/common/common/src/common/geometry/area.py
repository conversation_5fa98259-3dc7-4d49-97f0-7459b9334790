from common.geometry.pose import MoverPose
from common_msgs.msg import PlannedRoute, RoutePoint
from std_msgs.msg import Header
from geometry_msgs.msg import Pose2D
import rospy
import json


class MoverArea(object):
    def __init__(self, data='', *args, **kwargs):
        super(MoverArea, self).__init__(*args, **kwargs)
        self.timestamp = None
        self.seq = None
        self.frame_id = ""
        self.direction = None
        self.border_points = []
        self.obstacles = []
        self.in_point = MoverPose()
        self.out_point = MoverPose()
        self.target_point = MoverPose()
        if not data == "":
            self.from_json_in_msg(data)

    def from_msg(self, message):
        self.timestamp = message.header.stamp
        rospy.logerr('SEQ')
        rospy.logerr(message.header.seq)
        self.seq = message.header.seq
        self.frame_id = message.header.frame_id

        self.direction = message.type
        self.border_points = message.border_points
        self.obstacles = message.obstacles

        self.in_point = MoverPose(
            x=message.in_point.x,
            y=message.in_point.y,
            theta=message.in_point.theta
        )
        self.out_point = MoverPose(
            x=message.out_point.x,
            y=message.out_point.y,
            theta=message.out_point.theta
        )
        self.target_point = MoverPose(
            x=message.target_point.x,
            y=message.target_point.y,
            theta=message.target_point.theta
        )
        # if self.out_point.x < self.in_point.x:
        #     self.out_point.theta *= -1

    def from_json_in_msg(self, data):
        read_json = json.loads(data)
        self.border_points = read_json['border_points']
        self.obstacles = read_json['obstacles']
        self.direction = read_json['direction']
        self.start_point = MoverPose(
            x=read_json['start_point']['x'],
            y=read_json['start_point']['y'],
            theta=read_json['start_point']['theta'],
        )

        self.target_point = MoverPose(
            x=read_json['target_point']['x'],
            y=read_json['target_point']['y'],
            theta=read_json['target_point']['theta'],
        )

    def from_file(self, url):
        read_file = open(url)
        read_json = json.loads(read_file.read())

        # rospy.logerr(read_json)
        self.direction = read_json['direction']
        # rospy.logerr(self.direction)
        self.border_points = read_json['border_points'],
        self.obstacles = read_json['obstacles'],

        self.start_point = MoverPose(
            x=read_json['start_point']['x'],
            y=read_json['start_point']['y'],
            theta=read_json['start_point']['theta'],
        )

        self.target_point = MoverPose(
            x=read_json['target_point']['x'],
            y=read_json['target_point']['y'],
            theta=read_json['target_point']['theta'],
        )
        # if self.out_point.x < self.in_point.x:
        #     self.out_point.theta *= -1
        read_file.close()

    def to_msg(self):
        message = PlannedRoute(
            header=Header(
                stamp=rospy.Time.now(),
                frame_id=self.frame_id
            )
        )
        message.width = self.width
        message.length = self.length

        message.position = Pose2D(
            x=self.pose.x,
            y=self.pose.y,
            theta=self.pose.theta,
        )

        message.start_point = Pose2D(
            x=self.start_point.x,
            y=self.start_point.y,
            theta=self.start_point.theta,
        )

        message.target_point = Pose2D(
            x=self.target_point.x,
            y=self.target_point.y,
            theta=self.target_point.theta,
        )
        return message
