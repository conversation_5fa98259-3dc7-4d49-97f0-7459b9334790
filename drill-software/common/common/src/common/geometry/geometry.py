#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import division

import math
import numpy
import rospy
from common.geometry import MoverPoint

# from sympy import Point, Segment

if rospy.has_param('Vehicle/geometry/wheel_base'):
    WHEEL_BASE = rospy.get_param('Vehicle/geometry/wheel_base')
else:
    WHEEL_BASE = 1.0

if rospy.has_param('Vehicle/geometry/min_turn_radius'):
    MIN_TURN_RADIUS = rospy.get_param('Vehicle/geometry/min_turn_radius')
else:
    MIN_TURN_RADIUS = 1.0

if rospy.has_param('Global/EPS'):
    EPS = rospy.get_param('Global/EPS')
else:
    EPS = 0.001


# Local MoverPoint geometry


def equal(first, second):
    return second - EPS <= first <= second + EPS


def rotate(point, angle):
    rotated_point = MoverPoint(x=point.x * math.cos(angle) - point.y * math.sin(angle),
                               y=point.x * math.sin(angle) + point.y * math.cos(angle))
    return rotated_point


def any_center_rotate(point, center, angle):
    new_point = MoverPoint(
        x=point.x - center.x,
        y=point.y - center.y
    )
    return MoverPoint(
        x=center.x + rotate(new_point, angle).x,
        y=center.y + rotate(new_point, angle).y,
    )


def line_points(p_from, p_to, x_step):
    return add_middle_points(p_from, p_to, x_step)


def circle_points(p_from, center, sector, max_step):
    start = MoverPoint(
        x=p_from.x - center.x,
        y=p_from.y - center.y
    )
    polar_start = to_polar(start)
    count = math.ceil(math.fabs(sector / max_step))
    step = sector / count

    circle = list()
    circle.append(polar_start)
    for i in range(1, int(count), 1):
        circle.append({
            'R': polar_start['R'],
            'Phi': circle[i - 1]['Phi'] - step
        }
        )
    result = []
    for point in circle:
        result.append(MoverPoint(
            x=point['R'] * math.cos(point['Phi']) + center.x,
            y=point['R'] * math.sin(point['Phi']) + center.y,
        )
        )
    return result


def to_polar(p):
    R = length(p)
    if R == 0:
        return {'R': 0, 'Phi': 0}

    cos = p.x / R
    phi = math.acos(cos)
    if p.y < 0:
        phi *= -1

    polar = {'R': R, 'Phi': phi}
    return polar


def len_between(fr, to):
    return math.hypot(fr.x - to.x, fr.y - to.y)


def length(p):
    return math.hypot(p.x, p.y)


def len_to_segment(fr, segment_begin, segment_end):
    scalar_s_b = \
        numpy.vdot(
            [fr.x - segment_begin.x, fr.y - segment_begin.y],
            [segment_end.x - segment_begin.x, segment_end.y - segment_begin.y]
        )
    if scalar_s_b <= 0.0:
        return len_between(fr, segment_begin)

    scalar_s_e = \
        numpy.vdot(
            [fr.x - segment_end.x, fr.y - segment_end.y],
            [segment_begin.x - segment_end.x, segment_begin.y - segment_end.y]
        )
    if scalar_s_e <= 0.0:
        return len_between(fr, segment_end)

    if segment_end != segment_begin:
        # Расстояние от точки до сегмента может быть выражено через площадь треугольника:
        # h = 2*S/b, где h - высота треугольника (требуемое значение); S - площадь; b - длина
        # основания треугольника
        # Площадь треугольника может быть выражена по формуле: (p*(p-a)*(p-b)*(p-c))**0.5, где p -
        # полупериметр; a, b, c - длины сторон
        first_edge_length = len_between(fr, segment_begin)
        second_edge_length = len_between(fr, segment_end)
        third_edge_length = len_between(segment_begin, segment_end)
        half_perimeter = sum([first_edge_length, second_edge_length, third_edge_length]) / 2.0

        triangle_area = \
            (half_perimeter \
             * (half_perimeter - first_edge_length) \
             * (half_perimeter - second_edge_length) \
             * (half_perimeter - third_edge_length)) ** 0.5
        triangle_height = 2.0 * triangle_area / third_edge_length

        return triangle_height
    else:
        return len_between(fr, segment_begin)


def add_middle_points(start, end, point_distance):
    u"""
    Разбивает отрезок от start до end с шагом, не превышающим point_distance.
    Возвращает список точек, разбивающих отрезок, причем первая точка списка совпадает со start,
    последняя точка находится внутри сегмета и не совпадает с end
    """
    distance = len_between(start, end)

    steps_count = int(math.ceil(distance / point_distance))
    # На случай, если distance == 0, чтобы вернулись стартовая и конечная точки
    steps_count = max(steps_count, 1)

    middle_points = []
    step = distance / steps_count
    zdist = abs(start.z - end.z)
    zstep = zdist / steps_count
    vector = MoverPoint(x=(end.x - start.x) / distance,
                        y=(end.y - start.y) / distance,
                        z=((end.z - start.z) / zdist) if zdist else 0)
    for i in xrange(steps_count):
        middle_points.append(MoverPoint(x=vector.x * step * i + start.x,
                                        y=vector.y * step * i + start.y,
                                        z=vector.z * zstep * i + start.z,
                                        target_speed=start.target_speed,
                                        road_roughness=start.road_roughness))

    return middle_points


def divide_segment(start, end, point_distance):
    u"""
    Разбивает отрезок от start до end с шагом, не превышающим point_distance.
    Возвращает список точек, разбивающих отрезок, причем первая точка списка совпадает со start,
    последняя точка совпадает с end
    """
    divided_segment = add_middle_points(start, end, point_distance)
    divided_segment.append(
        MoverPoint(x=end.x, y=end.y, target_speed=end.target_speed, road_roughness=end.road_roughness)
    )
    return divided_segment


def get_rear_wheel_position(position, yaw):
    rear_wheel_local = MoverPoint(x=-WHEEL_BASE, y=0)
    rear_wheel_shift = rotate(rear_wheel_local, yaw)
    rear_wheel = MoverPoint(x=position.x + rear_wheel_shift.x,
                            y=position.y + rear_wheel_shift.y)
    return rear_wheel


def get_yaw(qat):
    qx2 = math.pow(qat.x, 2)
    qy2 = math.pow(qat.y, 2)
    qz2 = math.pow(qat.z, 2)
    yaw = math.atan2(2 * (qat.z * qat.w + qat.x * qat.y),
                     1 - 2 * (qy2 + qz2))
    return yaw


def get_turn_direction(a, c, obstacle_point):
    x = obstacle_point.x
    y = obstacle_point.y

    result = (x - a.x) * (c.y - a.y) - (y - a.y) * (c.x - a.x)
    return numpy.sign(result)


# MoverPoint geometry
def route_length(route, start=0, stop=None):
    r_len = 0
    if stop is None or stop > len(route.points):
        stop = len(route.points)
    if stop <= 1 + start:
        return 0
    for i in range(1 + start, stop, 1):
        r_len += len_between(route.points[i], route.points[i - 1])
    return r_len


def get_curvature(route, start, analyse_length=11):
    points_count = -1
    analyze_distance = 0
    for j in range(1, len(route.points) - start, 1):

        distance = len_between(route.points[start + j - 1], route.points[start + j])
        if analyze_distance + distance > analyse_length:
            break
        else:
            analyze_distance += distance
            points_count = j
    if points_count < 2:
        return 1.0

    A = route.points[0]
    B = route.points[int(points_count / 2)]
    C = route.points[points_count]

    a = len_between(C, B)
    b = len_between(A, C)
    c = len_between(A, B)

    p = (a + b + c) / 2
    if p * (p - a) * (p - b) * (p - c) <= 0:
        return 9999

    return (a * b * c) / (4 * math.sqrt(p * (p - a) * (p - b) * (p - c)))


def check_distances(points, max_point_distance=2.0):
    divided_points = []

    if len(points) > 1:
        for i in xrange(len(points) - 1):
            prev_point, next_point = points[i:i + 2]

            len_between_points = len_between(prev_point, next_point)
            if len_between_points > max_point_distance:
                # Записываются точки сегмента: от начальной (включительно) до конечной
                # (не включительно)
                middle_points = divide_segment(prev_point, next_point, max_point_distance)
                divided_points.extend(middle_points[:-1])
            else:
                divided_points.append(prev_point)
        else:
            divided_points.append(points[-1])
    else:
        divided_points = points

    return divided_points


def point_to_global_system(point, pose):
    rotated_point = rotate(point, pose.theta)
    result_point = MoverPoint(x=pose.x + rotated_point.x,
                              y=pose.y + rotated_point.y)
    return result_point


def traect_to_global_system(points, pose):
    new_traectory = []
    for point in points:
        new_traectory.append(point_to_global_system(point, pose))

    return new_traectory


def norm_angle_minusPI_plusPI(angle):
    # if -math.pi >= angle or angle >= math.pi:
    # f, i = math.modf(angle/math.pi)
    # angle = math.pi*f
    while angle <= math.pi:
        angle += 2 * math.pi
    while angle > math.pi:
        angle -= 2 * math.pi
    return angle


def norm_angle_0_2PI(angle):
    while angle < 0:
        angle += 2 * math.pi
    while angle >= 2 * math.pi:
        angle -= 2 * math.pi

    return angle


def check_intersection_of_segments(seg1_begin, seg1_end, seg2_begin, seg2_end):
    A1 = seg1_begin.y - seg1_end.y
    B1 = seg1_end.x - seg1_begin.x
    C1 = seg1_begin.x * seg1_end.y - seg1_end.x * seg1_begin.y
    A2 = seg2_begin.y - seg2_end.y
    B2 = seg2_end.x - seg2_begin.x
    C2 = seg2_begin.x * seg2_end.y - seg2_end.x * seg2_begin.y

    if B1 * A2 - B2 * A1 and A1:
        y = (C2 * A1 - C1 * A2) / (B1 * A2 - B2 * A1)
        x = (-C1 - B1 * y) / A1

        if min(seg1_begin.x, seg1_end.x) <= x <= max(seg1_begin.x, seg1_end.x):
            return True
        else:
            return False
    elif B1 * A2 - B2 * A1 and A2:
        y = (C2 * A1 - C1 * A2) / (B1 * A2 - B2 * A1)
        x = (-C2 - B2 * y) / A2

        if min(seg1_begin.x, seg1_end.x) <= x <= max(seg1_begin.x, seg1_end.x):
            return True
        else:
            return False
    else:
        return False


"""def sympy_intersection(seg1_begin, seg1_end, seg2_begin, seg2_end):
    s1, s2 = Segment((seg1_begin.x, seg1_begin.y), (seg1_end.x, seg1_end.y)), \
             Segment((seg2_begin.x, seg2_begin.y), (seg2_end.x, seg2_end.y))
    intersection = s1.intersection(s2)
    return len(intersection) != 0"""
