#!/usr/bin/env python
# -*- coding: utf-8 -*-


class MoverPoint(object):
    def __init__(self, x=0, y=0, z=0, target_speed=0, road_roughness=0):
        super(MoverPoint, self).__init__()
        self.x = x
        self.y = y
        self.z = z
        self.target_speed = target_speed
        self.road_roughness = road_roughness
        self.action_seq = None

    def __eq__(self, other):
        return self.x == other.x and self.y == other.y

    def __add__(self, other):
        return MoverPoint(x=self.x + other.x, y=self.y + other.y, z=self.z + other.z)

    def __iadd__(self, other):
        self.x += other.x
        self.y += other.y
        self.z += other.z
        return self
