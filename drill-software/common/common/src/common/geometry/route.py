from common.geometry.point import MoverPoint
from common.geometry.geometry import len_between
from common_msgs.msg import PlannedRoute
from common_msgs.msg import RoutePoint
from std_msgs.msg import Header
from geometry_msgs.msg import Point
import rospy
import json
import copy


class MoverRoute(object):
    def __init__(self, data='', direction='forward', *args, **kwargs):
        super(MoverRoute, self).__init__(*args, **kwargs)
        self.timestamp = rospy.Time.now()
        self.seq = 0
        self.frame_id = ""
        self.need_stoppage = False
        self.direction = direction
        self.points = []
        self.extra_data = None
        if not data == "":
            self.from_json_in_msg(data)

    def from_msg(self, message):
        self.timestamp = message.header.stamp
        self.seq = message.header.seq
        self.frame_id = message.header.frame_id
        if message.extra_data != "":
            self.extra_data = json.loads(message.extra_data)
        else:
            self.extra_data = None
        self.direction = message.direction
        self.points = []
        for point in message.points:
            self.points.append(MoverPoint(
                x=point.local_pose.x,
                y=point.local_pose.y,
                z=point.local_pose.z,
                target_speed=point.target_speed,
                road_roughness=point.road_roughness
            ))

    def from_json_in_msg(self, data):
        read_json = json.loads(data)
        self.direction = read_json['mo_direction']
        self.points = []
        for point in read_json['points']:
            new_point = MoverPoint(
                x=point['local_pose']['x'],
                y=point['local_pose']['y'],
                target_speed=point['target_speed'],
                road_roughness=point['road_roughness']
            )
            self.points.append(new_point)

    def from_file(self, url):
        read_file = open(url)
        read_json = json.loads(read_file.read())
        self.direction = read_json['mo_direction']
        self.points = []
        #rospy.logerr(url)
        for point in read_json['points']:
            new_point = MoverPoint(
                x=point['local_pose']['x'],
                y=point['local_pose']['y'],
                z=point['local_pose']['z'],
                target_speed=point['target_speed'],
                road_roughness=point['road_roughness']
            )
            #rospy.logerr(point['local_pose']['z'])
            self.points.append(new_point)
        read_file.close()

    def to_json(self):
        out_json = {"direction": self.direction, "points": []}
        for point in self.points:
            out_json['points'].append(
                {"local_pose": {"x": point.x, "y": point.y, "z": point.z}, "target_speed": point.target_speed,
                 "road_roughness": point.road_roughness})
        return json.dumps(out_json)

    def to_msg(self):
        message = PlannedRoute(
            header=Header(
                stamp=rospy.Time.now(),
                frame_id=self.frame_id
            ),
            direction=self.direction
        )
        if self.extra_data is not None:
            message.extra_data = json.dumps(self.extra_data)
        for point in self.points:
            message.points.append(RoutePoint(
                local_pose=Point(
                    x=point.x,
                    y=point.y,
                    z=point.z
                ),
                target_speed=point.target_speed,
                road_roughness=point.road_roughness

            ))
        return message

    def path_len(self):
        r_len = 0
        points = copy.deepcopy(self.points)
        if len(points) < 2:
            return 0
        else:
            for i in range(1, len(points) - 1, 1):
                r_len += len_between(points[i], points[i - 1])
            return r_len

    def __len__(self):
        return len(self.points)
