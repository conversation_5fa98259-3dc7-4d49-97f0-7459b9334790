#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import os
from pypcd import pypcd
import numpy as np
from astropy.convolution import Gaussian2D<PERSON><PERSON>l, interpolate_replace_nans
import json
from sensor_msgs.msg import PointCloud2, PointField

MIN_VALUE = -99999
MAX_VALUE = 99999

class HeightMap():
    '''Class calculates height map and save it in json file
    Height map contains mean value of z coord of map points falling into a cell
    '''
    def __init__(self):
        # load parameters
        self.map_csize = rospy.get_param('/create_height_map/map_csize')
        self.hmap_csize = rospy.get_param('/create_height_map/height_map_csize')
        self.kernel_size = rospy.get_param('/create_height_map/kernel_size')
        self.factor = rospy.get_param('/create_height_map/factor')
        self.path = rospy.get_param('/create_height_map/path_to_map')

        height_map_pub = rospy.Publisher('/height_map_debug', PointCloud2, queue_size=10)

        # form path for json file
        current_path, _ = os.path.split(os.path.realpath(__file__))
        self.path_to_json, _ = os.path.split(current_path)
        self.path_to_json, _ = os.path.split(self.path_to_json)
        self.path_to_json += '/maps/height_map.json'

        self.empty_map_value = 0.0
        # process map and create 2d height map and 3d map
        height_map, map_3d = self.create_height_map(self.path)
        self.write_to_json(map_3d)
        self.pub_point_cloud = self.publish_map(map_3d)

        while not rospy.is_shutdown():
            height_map_pub.publish(self.pub_point_cloud)
            rospy.loginfo('Publishing height map point cloud')
            rospy.sleep(1.0)

    def publish_map(self, map_3d):
        '''Forms Point Cloud for publishing'''
        point_cloud = PointCloud2()
        point_cloud.header.frame_id = 'map'
        point_cloud.height = 1
        point_cloud.width = len(map_3d)
        point_cloud.fields = [
            PointField('x', 0, PointField.FLOAT32, 1),
            PointField('y', 4, PointField.FLOAT32, 1),
            PointField('z', 8, PointField.FLOAT32, 1)]
        point_cloud.is_bigendian = False
        point_cloud.point_step = 12
        point_cloud.row_step = 12 * len(map_3d)
        point_cloud.is_dense = int(np.isfinite(map_3d).all())
        point_cloud.data = np.asarray(map_3d, np.float32).tostring()
        return point_cloud

    def create_height_map(self, path_to_map):
        '''Calcultes height map and 3d map'''
        map = pypcd.PointCloud.from_path(path_to_map)
        rospy.loginfo('Size of loaded map %d, %d' % (map.width, map.height))
        # calculate map borders
        min_x = min(map.pc_data['x'])
        max_x = max(map.pc_data['x'])
        min_y = min(map.pc_data['y'])
        max_y = max(map.pc_data['y'])
        self.map_xmin, map_xmax = self.map_min_max_values(min_x, max_x)
        self.map_ymin, map_ymax = self.map_min_max_values(min_y, max_y)
        rospy.loginfo('Map min_x: %d, max_x: %d' % (self.map_xmin, map_xmax))
        rospy.loginfo('Map min_y: %d, max_y: %d' % (self.map_ymin, map_ymax))

        # enhance min and max values of map for height map interpolation
        self.map_xmin_enhanced = self.map_xmin - self.hmap_csize * self.kernel_size * self.factor
        map_xmax_enhanced = map_xmax + self.hmap_csize * self.kernel_size * self.factor
        self.map_ymin_enhanced = self.map_ymin - self.hmap_csize * self.kernel_size * self.factor
        map_ymax_enhanced = map_ymax + self.hmap_csize * self.kernel_size * self.factor
        rospy.loginfo('Enhanced map min_x: %d, max_x: %d' % (self.map_xmin_enhanced, map_xmax_enhanced))
        rospy.loginfo('Enhanced map min_y: %d, max_y: %d' % (self.map_ymin_enhanced, map_ymax_enhanced))
        self.hmap_cells_x = int((map_xmax_enhanced - self.map_xmin_enhanced) / self.hmap_csize)
        self.hmap_cells_y = int((map_ymax_enhanced - self.map_ymin_enhanced) / self.hmap_csize)
        height_map = np.full((self.hmap_cells_x, self.hmap_cells_y), self.empty_map_value)

        rospy.loginfo('Height map dim before interpolation: %s' % (np.shape(height_map),))
        # initialize heightmap
        count_points_map = np.full((self.hmap_cells_x, self.hmap_cells_y), self.empty_map_value)
        # filling in the height map
        for i in range(0, map.width):
            # find the corresponding cell and push the point into it
            pt_x = map.pc_data['x'][i]
            pt_y = map.pc_data['y'][i]
            # find cell
            cx, cy = self.find_cell_heightmap(pt_x, pt_y)
            height_map[cx, cy] += map.pc_data['z'][i]
            count_points_map[cx, cy] += 1.0
        height_map = height_map / count_points_map

        count = 0
        for i in range(0, self.hmap_cells_x):
            for j in range(0, self.hmap_cells_y):
                if np.isnan(height_map[i, j]):
                    count += 1
        rospy.loginfo('Number of filled / total cells %d / %d' % (self.hmap_cells_x * self.hmap_cells_y - count,
                      self.hmap_cells_x * self.hmap_cells_y))
        rospy.loginfo('Number of nan %d' % count)

        kernel = Gaussian2DKernel(10)
        height_map[height_map == np.inf] = np.nan
        rospy.loginfo('Interpolating height map...')
        height_map = interpolate_replace_nans(height_map, kernel)
        rospy.loginfo('Interpolation has finished.')

        # delete extra rows and columns in the height map
        height_map = np.delete(height_map, slice(0, self.kernel_size * self.factor), axis=0)
        height_map = np.delete(height_map, slice(len(height_map) - self.kernel_size * self.factor, len(height_map)),
                               axis=0)
        height_map = np.delete(height_map, slice(0, self.kernel_size * self.factor), axis=1)
        height_map = np.delete(height_map, slice(len(height_map[0]) - self.kernel_size * self.factor, len(height_map[0])),
                               axis=1)

        rospy.loginfo('Height map dim after interpolation: %s' % (np.shape(height_map),))
        map_3d = []
        (x, y) = np.shape(height_map)
        for i in range(0, x):
            for j in range(0, y):
                x_coord, y_coord = self.map_coordinates(i, j)
                map_3d.append([x_coord, y_coord, height_map[i, j]])

        return height_map, map_3d

    def write_to_json(self, map_3d):
        '''Write 3d map to json file'''
        max_x = max_y = MIN_VALUE
        min_x = min_y = MAX_VALUE

        map_3d_json = {}
        map_3d_json['points'] = []
        for i in map_3d:
            if np.isnan(i[2]):
                pass
            else:
                map_3d_json['points'].append(i)
                # find min and max values of x and y of the map
                if i[0] < min_x:
                    min_x = i[0]
                if i[0] > max_x:
                    max_x = i[0]
                if i[1] < min_y:
                    min_y = i[1]
                if i[1] > max_y:
                    max_y = i[1]

        map_3d_json['x_min'] = min_x
        map_3d_json['x_max'] = max_x
        map_3d_json['y_min'] = min_y
        map_3d_json['y_max'] = max_y
        map_3d_json['cell_size'] = self.hmap_csize
        # write to file
        with open(self.path_to_json, 'w') as outfile:
            json.dump(map_3d_json, outfile)
        rospy.loginfo('Summary of json file:')
        rospy.loginfo("Number of points in 3d map: %d" % len(map_3d))
        rospy.loginfo('Min_x: %d, max_x: %d' % (min_x, max_x))
        rospy.loginfo('Min_y: %d, max_y: %d' % (min_y, max_y))
        rospy.loginfo('Cell size: %.2f' % self.hmap_csize)
        rospy.loginfo('Saved 3d map to %s' % self.path_to_json)

    def map_coordinates(self, cell_x, cell_y):
        '''Calculates x and y map coordinates of a center of a height map cell'''
        x_coord = self.map_xmin + (cell_x + 0.5) * self.hmap_csize
        y_coord = self.map_ymin + (cell_y + 0.5) * self.hmap_csize
        return x_coord, y_coord

    def map_min_max_values(self, min_value, max_value):
        '''Calcultes rounded min and max values of initial map'''
        map_min = np.floor(min_value / self.map_csize) * self.map_csize
        map_max = np.ceil(max_value / self.map_csize) * self.map_csize
        return map_min, map_max

    def find_cell_heightmap(self, pt_x, pt_y):
        '''Calculates cell indices of height map for a point from initial map'''
        cx = int(np.floor((pt_x - self.map_xmin_enhanced) / self.hmap_csize))
        cy = int(np.floor((pt_y - self.map_ymin_enhanced) / self.hmap_csize))
        return cx, cy

def main():
    rospy.init_node('create_height_map')
    try:
        HeightMap()
    except rospy.ROSInterruptException:
        pass

if __name__ == '__main__':
    main()
