#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import division
import math
import numpy as np
import time
from scipy import optimize
#import matplotlib.pyplot as plt

class AccelModel(object):
    def __init__(self, m_power, b_power, k, mass, time_const_a, time_const_b, N = 0.0, B = 0.0,
                 a2 = 0.0, a1 = 1.0, a0 = 0.0, b2 = 0.0, b1 = 1.0, b0 = 0.0, tpl_k = 0.0, tpl_p = 1.0, tpl_d = 1.0,
                 k2_br = 1.0, k1_br = 1.0, k0_br = 1.0, slope = None, roughness=None):
        self.a2 = a2
        self.a1 = a1
        self.a0 = a0
        self.b2 = b2
        self.b1 = b1
        self.b0 = b0
        self.mp = m_power
        self.bp = b_power
        self.Ta = time_const_a
        self.Tb = time_const_b
        self.k = k
        self.m = mass
        self.N_old = N
        self.B_old = B
        self.tpl_k = tpl_k
        self.tpl_p = tpl_p
        self.tpl_d = tpl_d
        self.k2_br = k2_br
        self.k1_br = k1_br
        self.k0_br = k0_br
        self.slope = slope
        self.roughness=roughness

        if self.slope is None:
            self.slope = self.zero

        if self.roughness is None:
            self.roughness = self.roughness_stub

        self.last_ts = 0
        self.last_u = 0
        self.dir_change_ts = 0

    def zero(self, x):
        return 0

    def roughness_stub(self, x):
        return self.k

    def nonlin_a(self, x):
        return self.a2*x**2 + self.a1*x + self.a0

    def nonlin_b(self, x):
        return self.b2*x**2 + self.b1*x + self.b0

    def motor(self, u, dt, N_old = None):
        if not 0 <= u <= 1:
            u = 0
        if N_old is None:
            N_old = self.N_old
        N = N_old + (1-math.exp(-dt/self.Ta))*(self.nonlin_a(u) * self.mp - N_old)
        #print N
        self.N_old = N
        return N

    def brake(self, u, dt, B_old = None):
        if not -1 <= u <= 0:
            u = 0
        if B_old is None:
            B_old = self.B_old
        B = B_old + (1-math.exp(-dt/self.Tb))*(self.nonlin_b(abs(u)) * -self.bp - B_old)
        self.B_old = B
        return B

    def calc_v_step(self, v0, dt, u, a = 0, k=None):
        if k is None:
            k = self.k
        motor = self.motor(u, dt)  # we're rly need to call both functions!!!
        brake = self.brake(u, dt)  # i told you! do not remove!!!
        #print brake
        #print motor

        if self.last_ts - self.dir_change_ts < 0.4:
            F = 0
        else:
            if u >= 0:
                if abs(v0) < 1.5:
                    v0 = 1.5*np.sign(v0)
                #print v0, v0/self.tpl_d
                N = motor - (self.tpl_k * (math.pow(abs(v0/self.tpl_d), self.tpl_p)) if motor > 1500 else 0)
                F = N/abs(v0)
            else:
                F = (self.k2_br * abs(v0)**2 + self.k1_br * abs(v0) + self.k0_br)*brake*np.sign(v0)
        #print N

        #print "N " ,N
        #print "pwr dv ", (N/(self.m * v0))*dt
        #print F
        return (F/self.m - 9.81 * (math.cos(a)*np.sign(v0)*k +math.sin(a)))*dt

    def calc_dx(self, v0, v, dt):
        return v0*dt+(v-v0)*dt/2

    def reinit(self, ts, u, dt, sw_ts, N, B):
        self.last_ts = ts
        self.last_u = u
        self.N_old = self.motor(u, dt, N_old=N)
        self.B_old = self.brake(u, dt, B_old=B)
        self.dir_change_ts = sw_ts
        return self.B_old, self.N_old

    def reinit2(self, ts, u, sw_ts, N, B):
        self.last_ts = ts
        self.last_u = u
        self.N_old = N
        self.B_old = B
        self.dir_change_ts = sw_ts


    def calc_v(self, x0, v0, u, slope=None, xt=None, tt = None, k = None, N_old = None, B_old = None, backward=False):
        if xt is None and tt is None:
            return None
        abort = False
        if v0 == 0:
            v0 = 0.001

        v = v0
        x = x0
        dt = 0.05
        t = 0

        if abs(u) < 0.01:
            u = 0

        if N_old is not None and B_old is not None:
            self.B_old = B_old
            self.N_old = N_old

        if np.sign(u) != np.sign(self.last_u):
            self.dir_change_ts = self.last_ts

        vlist = []
        tlist = []
        while not abort:
            if slope is None:
                slope_t = self.slope(x)
                #slope=self.slope(x)
            else:
                slope_t = slope

            if k is None:
                k_t = self.roughness(x)
                k = self.roughness(x)
            else:
                k_t = k

            v_old = v
            #print "V: ", v, "at T= ", t
            dv = self.calc_v_step(v_old, dt, u, slope_t, k_t)
            v += dv
            vlist.append(v)
            if not backward:
                x += self.calc_dx(v_old, v, dt)
            else:
                x -= self.calc_dx(v_old, v, dt)
            t += dt
            tlist.append(t)
            self.last_ts += dt
            if tt is not None:
                if t >= tt:
                    abort = True
            if xt is not None:
                if not backward and x >= xt:
                    abort = True
                elif backward and x <= xt:
                    abort = True
        return x, v, t, vlist, tlist


def tf(arg):
    tpl_k = arg[0]
    tpl_p =  arg[1]
    tpl_d = arg[2]
    p30 = arg[3]
    p50 = arg[4]
    p70 = arg[5]
    p100 = arg[6]
    #print arg
    dat = [{"thr": p30, 't': 7, "v": 1},
           {"thr": p30, 't': 15, "v": 1.1},

           {"thr": p50, 't': 4, "v": 1},
           {"thr": p50, 't': 6, "v": 2},
           {"thr": p50, 't': 10, "v": 3},
           {"thr": p50, 't': 26, "v": 3.5},

           {"thr": p100, 't': 8, "v": 3.5},
           {"thr": p100, 't': 10, "v": 5},
           {"thr": p100, 't': 200, "v": 15},

           {"thr": p70, 't': 3, "v": 1},
           {"thr": p70, 't': 5, "v": 2},
           {"thr": p70, 't': 7, "v": 3},
           {"thr": p70, 't': 10, "v": 4.5},
           ]
    err = 0
    for sample in dat:
        am = AccelModel(m_power=sample['thr'], b_power=0, k=0.016, mass=106000, time_const_a=1.0, time_const_b=0.5,
                        tpl_k=tpl_k, tpl_p=tpl_p, tpl_d=tpl_d)
        res = am.calc_v(x0=0, v0=0.01, u=1.0, slope=math.radians(0), tt=sample['t'])
        #print res
        err+= (sample['v'] - res[1])**2
    print math.sqrt(err)
    return math.sqrt(err)


def tf_dec(arg):
    #tpl_k = arg[0]
    #tpl_p =  arg[1]
    #tpl_d = arg[2]
    p30 = arg[0]
    p50 = arg[1]
    p70 = arg[2]
    p100 = arg[3]
    k2_br = arg[4]
    k1_br = arg[5]
    k0_br = arg[6]

    #print arg
    dat = [
           {"thr": p100, 't': 2.5, "v": 2, "v0": 3},
           {"thr": p100, 't': 3.5, "v": 1, "v0": 3},
           {"thr": p100, 't': 4, "v": 0.5, "v0": 3},
           {"thr": p100, 't': 6, "v": 0.3, "v0": 3},
           {"thr": p100, 't': 8, "v": 0.0, "v0": 3},

           {"thr": p70, 't': 3, "v": 2, "v0": 3},
           {"thr": p70, 't': 5, "v": 0.8, "v0": 3},
           {"thr": p70, 't': 9, "v": 0, "v0": 3},
           {"thr": p70, 't': 7, "v": 0.25, "v0": 3},

           {"thr": p50, 't': 3, "v": 2.5, "v0": 3},
           {"thr": p50, 't': 5, "v": 1.8, "v0": 3},
           {"thr": p50, 't': 8, "v": 1.0, "v0": 3},
           {"thr": p50, 't': 11, "v": 0.3, "v0": 3},
           {"thr": p50, 't': 13, "v": 0.0, "v0": 3},

           {"thr": p30, 't': 3, "v": 2.5, "v0": 3},
           {"thr": p30, 't': 5, "v": 2, "v0": 3},
           {"thr": p30, 't': 10, "v": 0.8, "v0": 3},
           {"thr": p30, 't': 12, "v": 0.4, "v0": 3},
           {"thr": p30, 't': 15, "v": 0.0, "v0": 3},

           ]
    err = 0
    for sample in dat:
        am = AccelModel(m_power=0, b_power=sample['thr'], k=0.016, mass=106000, time_const_a=0.8, time_const_b=1.2,
                        k2_br=k2_br, k1_br=k1_br, k0_br=k0_br)
        res = am.calc_v(x0=0, v0=sample['v0'], u=-1.0, tt=sample['t'], slope=math.radians(0))
        #print res
        err+= (sample['v'] - res[1])**2
        print abs(sample['v'] - res[1])
    print math.sqrt(err)
    return err

if __name__ == '__main__':

    def slope(x):
        return math.radians(5)


    psc = PredictiveSpeedController(slope)
    t = time.time()
    u = psc.make_control(x0=0, v0=1.5, delta_x=50, v_target=1.5, current_time=time.time())
    print u
    u = psc.make_control(x0=0, v0=1.5, delta_x=50, v_target=1.5, current_time=time.time()+2)
    print u
    print "simulation time: ", (time.time() - t)

    """
    ulist = []
    errlist = []

    for u_ in xrange(200):
        u = (u_ / 100.0)-1.0
        ulist.append(u)
        am = AccelModel(m_power=265650, b_power=1.66536079e+04, k=0.016, mass=106000, time_const_a=1.0,
                        time_const_b=1.2,
                        tpl_k=1.07424597e+04, tpl_p=9.64991685e-03, tpl_d=5.24880327,
                        a2=0.546, a1=0.448, a0=0.003,
                        k2_br=4.69123019e-01, k1_br=7.64177208e-01, k0_br=4.75551350e-01,
                        b2=1.08404, b1=-0.0736998, b0=0.00208957,
                        slope=slope)
        res = am.calc_v(x0=0, v0=1.5, u=u, xt=50, tt=(50/np.mean([1.5,1.5]))*1.5 + 10)
        #print res
        if abs(res[0] - 50) > 0.5:
            errlist.append(float('Inf'))
        else:
            errlist.append((res[1]-1.5)**2)
        #errlist.append(res[1])

    plt.plot(ulist, errlist)
    plt.show()

    
    am = AccelModel(m_power=265650, b_power=1.66536079e+04, k=0.016, mass=106000, time_const_a=1.0, time_const_b=1.2,
                    tpl_k=1.07424597e+04, tpl_p =9.64991685e-03, tpl_d=5.24880327,
                    a2=0.546, a1=0.448, a0=0.003,
                    k2_br=4.69123019e-01, k1_br=7.64177208e-01, k0_br=4.75551350e-01,
                    b2=1.08404, b1=-0.0736998, b0=0.00208957,
                    slope=slope)
    t = time.time()
    res = am.calc_v(x0=0, v0=1.5, u=0.76, xt=50)
    print "simulation time: ", (time.time()-t)
    print res[1]
    print (res[1]-1.5)**2
    plt.plot(res[4],res[3])
    plt.show()
   
    
    
    print optimize.minimize(fun=tf_dec, x0=np.array([10000, 10000, 10000, 10000, 5.54113452e-01, 5.54113452e-01, 3.54113452e-01]), method='Nelder-Mead',
                            options={'disp': False, 'maxiter': 7000, 'maxfev': 9000, 'xatol': 0.01,  'fatol': 0.01})
    


    print optimize.minimize(fun=tf, x0=np.array([2000, 2, 2, 50000, 100000, 120000, 150000]), method='Nelder-Mead',
                            options={'disp': True})
    """
