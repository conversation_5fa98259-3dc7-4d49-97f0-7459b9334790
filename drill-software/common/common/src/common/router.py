import copy

class Router(object):
    """
    init with descriptions of areas, crosses and places. Read docs in confluence or example in the
    end of file for more info
    """
    def __init__(self, graph, areas, crosses, places):
        self.graph = graph
        self.areas = areas
        self.crosses = crosses
        self.places = places

    def calc_node_route(self, start, finish):
        """
        :param start: id of start node
        :param finish: id of target node
        :return: route dict
        """
        if start == finish:
            return {'path': [finish], 'len': 0, 'roads': []}

        search_threads = [{'path': [start], 'len': 0, 'roads': []}]

        min_len = float('inf')
        while len(search_threads) > 1 or search_threads[0]['path'][-1] != finish:
            add_search_threads = []
            kill_search_threads = []
            for i in xrange(len(search_threads)):
                head = search_threads[i]['path'][-1]
                if head == finish:
                    if search_threads[i]['len'] > min_len:
                        kill_search_threads.append(i)
                    continue
                if head not in self.graph.keys() or not len(self.graph[head]):
                    cross_found = False
                    connected_nodes = []
                    for k in self.crosses.keys():
                        if head in self.crosses[k]['enters']:
                            for exit in self.crosses[k]['exits']:
                                connected_nodes.append({'target': exit, 'roads': ['CROSS:'+k+':'+head+':'+exit], 'len': 0, 'closed': self.crosses[k]['closed']})
                            cross_found = True
                            break
                    if not cross_found:
                        kill_search_threads.append(i)
                        #print "killed thread %d because of dead end"%i
                        continue
                else:
                    connected_nodes = self.graph[head]

                accepted_transitions = 0
                updated_thread = None
                connected_nodes = [cn for cn in connected_nodes if not cn['closed']]
                for j in xrange(len(connected_nodes)):
                    new_len = search_threads[i]['len']+connected_nodes[j]['len']
                    if new_len > min_len:
                        continue
                    else:
                        accepted_transitions += 1
                        if connected_nodes[j]['target'] == finish and new_len < min_len:
                            min_len = new_len
                        if not j:
                            updated_thread = {'path': search_threads[i]['path']+[connected_nodes[j]['target']],
                                                       'len': new_len, 'roads': search_threads[i]['roads'] + [connected_nodes[j]['roads']]}
                        else:
                            add_search_threads.append({'path': search_threads[i]['path']+[connected_nodes[j]['target']],
                                                       'len': new_len, 'roads': search_threads[i]['roads'] + [connected_nodes[j]['roads']]})
                if accepted_transitions == 0:
                    kill_search_threads.append(i)
                    #print "killed thread %d because of length or dead end"%i
                elif updated_thread is not None:
                    search_threads[i] = copy.deepcopy(updated_thread)
                else:
                    kill_search_threads.append(i)
                    #print "killed thread %d because of dead end" % i

            for j in sorted(kill_search_threads, reverse=True):
                del search_threads[j]

            search_threads.extend(add_search_threads)
            if not len(search_threads):
                return {'path': [], 'len': float('inf'), 'roads': []}
        return search_threads[0]

    def get_node_owner(self, node):
        """
        find object name containing given node
        :param node: node id
        :return: object name
        """
        for a in self.areas.keys():
            if node in self.areas[a]['enters'] or node in self.areas[a]['exits']:
                return a
        for p in self.places.keys():
            if node in self.places[p]:
                return p
        return None

    def calc_node2obj_route(self, node, finish_name):
        """
        find route between node and object
        :param node: node id
        :param finish_name: target object name
        :return: route dict
        """
        #print "looking up for route from node %s  to %s" % (node, finish_name)
        if finish_name not in self.areas.keys() and finish_name not in self.places.keys():
            raise AttributeError("unknown finish place %s" % finish_name)

        finish_nodes = []
        if finish_name in self.areas.keys():
            finish_nodes.extend(self.areas[finish_name]['enters'])
        elif finish_name in self.places.keys():
            finish_nodes.extend(self.places[finish_name])

        min_route = {'path': [], 'len': float('inf'), 'roads': []}

        for finish in finish_nodes:
            r = self.calc_node_route(node, finish)
            if r is not None:
                if r['len'] < min_route['len']:
                    min_route = copy.deepcopy(r)
        #print "result: %s"%str(min_route)
        return min_route

    def calc_obj_route(self, start_name, finish_name):
        """
        find route between two objects
        :param start_name: start object name
        :param finish_name: target object name
        :return: route dict
        """
        #print "looking up for route from %s to %s"%(start_name, finish_name)
        if start_name not in self.areas.keys() and start_name not in self.places.keys():
            raise AttributeError("unknown start place %s" % start_name)
        if finish_name not in self.areas.keys() and finish_name not in self.places.keys():
            raise AttributeError("unknown finish place %s" % finish_name)

        start_nodes = []
        if start_name in self.areas.keys():
            start_nodes.extend(self.areas[start_name]['exits'])
        elif start_name in self.places.keys():
            start_nodes.extend(self.places[start_name])

        finish_nodes = []
        if finish_name in self.areas.keys():
            finish_nodes.extend(self.areas[finish_name]['enters'])
        elif finish_name in self.places.keys():
            finish_nodes.extend(self.places[finish_name])


        min_route = {'path': [], 'len': float('inf'), 'roads': []}

        for start in start_nodes:
            for finish in finish_nodes:
                r = self.calc_node_route(start, finish)
                if r is not None:
                    if r['len'] < min_route['len']:
                        min_route = copy.deepcopy(r)
        #print "result: %s" % str(min_route)
        return min_route


if __name__ == '__main__':
    graph = {
        '2': [{'target': '13', 'roads': ['a.json'], 'len': 145, 'closed': False}, {'target': '15', 'roads': ['a.json'], 'len': 45, 'closed': False} ],
        '3': [{'target': '2', 'roads': ['a.json'], 'len': 50, 'closed': False}],
        '4': [{'target': '6', 'roads': ['a.json'], 'len': 50, 'closed': False}],
        '7': [{'target': '3', 'roads': ['a.json'], 'len': 50, 'closed': False}],
        '8': [{'target': '11', 'roads': ['a.json'], 'len': 50, 'closed': False}],
        '10': [{'target': '14', 'roads': ['a.json'], 'len': 50, 'closed': False}],
        '12': [{'target': '9', 'roads': ['a.json, b.json'], 'len': 50, 'closed': False}],
        '13': [{'target': '5', 'roads': ['a.json'], 'len': 50, 'closed': False}],
        '14': [{'target': '1', 'roads': ['a.json'], 'len': 50, 'closed': False}],
        '15': [{'target': '1', 'roads': ['a.json'], 'len': 30, 'closed': False}]
    }

    areas = {'load': {'enters': ['2', '3'], 'exits': ['2', '4']}, 'unload': {'enters': ['11'], 'exits': ['12']}}
    crosses = {'cr': {'enters': ['6', '5', '9'], 'exits': ['7', '8', '10'], 'closed': False}}
    places = {'R': ['13', '14'], 'P': ['15']}

    router = Router(graph, areas, crosses, places)
    print router.calc_node_route('12', '1')
    print router.calc_node_route('7', '5')
    print router.calc_obj_route('load', 'unload')
    print router.calc_node2obj_route('11', "unload")
    print router.get_node_owner("11")
