
import argparse
import json

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('-f', '--filename', help="file name")
    parser.add_argument('-nf', '--new_filename', help="new file name")
    #parser.add_argument('-r', '--repeat', action='store_true', help="repeat scenario")
    args, _ = parser.parse_known_args()

    with open(args.filename, "r") as f:
        trajectory = json.load(f)

    for point in trajectory['points']:
        point['target_speed'] = 2.5
        point['road_roughness'] = 0.025

    with open(args.new_filename, 'w') as f:
        f.write(json.dumps(trajectory))