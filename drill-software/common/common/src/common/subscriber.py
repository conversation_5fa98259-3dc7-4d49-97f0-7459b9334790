import rospy
import threading

# For Python 2/3 compatibility
try:
    import queue
    queue.time = rospy.get_time
except ImportError:
    import Queue as queue
    queue._time = rospy.get_time


class Subscriber(rospy.Subscriber):
    def __init__(self, name, data_class,
                 callback=None, callback_args=None,
                 queue_size=None, buff_size=65536, tcp_nodelay=False,
                 max_delay=-1, delay_callback=None, delay_callback_args=None):
        
        if delay_callback is not None and max_delay > 0:
            super(Subscriber, self).__init__(name, data_class,
                                         self._callback, callback_args,
                                         queue_size, buff_size, tcp_nodelay)

            self._message_received = False
            self._working = True

            self._name = name
            self._passed_callback = callback
            self._max_delay = max_delay
            self._delay_callback = delay_callback
            self._delay_callback_args = delay_callback_args

            self._queue = queue.Queue(maxsize=1)
            self._check_thread = threading.Thread(target=self._timeout_check)
            self._check_thread.start()

        else:
            super(Subscriber, self).__init__(name, data_class,
                                         callback, callback_args,
                                         queue_size, buff_size, tcp_nodelay)

    def unregister(self):
        self._working = False
        super(Subscriber, self).unregister()

    def _callback(self, *args, **kwargs):
        self._message_received = True

        try:
            self._queue.put_nowait(True)
        except:
            pass

        self._passed_callback(*args, **kwargs)

    def _timeout_check(self):
        while not rospy.is_shutdown() and self._working:
            try:
                self._queue.get(timeout=self._max_delay)
                self._queue.task_done()
            except queue.Empty:
                if self._message_received and self._working:
                    if self._delay_callback_args is not None:
                        self._delay_callback(self._name, self._delay_callback_args)
                    else:
                        self._delay_callback(self._name)

