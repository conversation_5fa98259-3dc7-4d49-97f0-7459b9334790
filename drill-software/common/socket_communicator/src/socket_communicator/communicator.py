#!/usr/bin/env python2
# -*- coding: utf-8 -*-

u"""
This module provides interface to create socket connection between server and
truck
"""
import time
import threading
import json
import struct
import socket
import sys
import msgpack
import zlib
# import lz4.frame


try:
    import socketserver
except ImportError:
    import SocketServer as socketserver


DEFAULT_TIMEOUT = 5 #seconds
IS_PY3 = sys.version_info[0] == 3


def serialize(data):
    u"""
    function to serialize data for transmission
    @param data <dict>
    @return str (python2) or bytes (python3)
    """

    s_data = msgpack.packb(data)
    compressed_data = zlib.compress(s_data)
    # compressed_data = lz4.frame.compress(s_data)

    header = struct.pack("hI", 0, len(compressed_data))

    # json_data = json.dumps(data)    # only to compare
    # print("JSON size: {} bytes".format(len(json_data)))
    # print("MessagePack size: {} bytes".format(len(s_data)))
    # print("Compressed MessagePack size: {} bytes".format(len(compressed_data)))

    return header + compressed_data


def convert_bytes_in_dict(obj):
    """
    Recursively convert all bytes objects in a nested dictionary or list to strings.
    (It's needed because msgpack.unpackb returns bytes instead of strings (even with raw=False))

    :param obj: The input object (a nested dictionary, list, or bytes object)
    :return: A new object with the same structure as the input object, but with all bytes objects replaced by strings
    """
    if isinstance(obj, dict):
        return {convert_bytes_in_dict(key): convert_bytes_in_dict(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_bytes_in_dict(element) for element in obj]
    elif isinstance(obj, bytes):
        return obj.decode('utf-8')
    else:
        return obj


def deserialize(data):
    u"""
    function to deserialize data for transmission
    @param data <bytes>
    @return dict
    """
    # header, l = struct.unpack("hI", data[:8])
    decompressed_data = zlib.decompress(data)
    # decompressed_data = lz4.frame.decompress(data)
    deserialized_data = msgpack.unpackb(decompressed_data)

    return convert_bytes_in_dict(deserialized_data)


def recv(sock, stop, timeout=DEFAULT_TIMEOUT):
    u"""
    function to receive some data from socket

    @param sock <socket.socket> object to receive data
    @param stop <function> function to notify that work is ower and we need
                           to stop receiving data
    @param timeout <int> timeout

    @return str or None
    """
    received = 0
    d = sock.recv(8)

    if len(d) > 0:
        header, size = struct.unpack("hI", d)
        b = bytearray()
        t = time.time()

        while size - received > 0 and not stop():
            if time.time() - t > timeout:
                raise socket.timeout()

            d = sock.recv(size - received)
            b.extend(d)
            received += len(d)

        return bytes(b)


def create_client(ip, port, timeout=DEFAULT_TIMEOUT):
    u"""
    function to create client socket instance
    @param ip <str>
    @param port <int>
    @param timeout <int>

    @return socket.socket
    """
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(timeout)
    sock.connect((ip, port))

    return sock


def create_server(node, ip, port, stop, timeout=DEFAULT_TIMEOUT, proto='tcp'):
    u"""
    function to create server socket instance
    @param node <object> some upper-level object who use this module
    @param ip <str>
    @param port <int>
    @param stop <function> function to notify that work is ower and we need
                           to stop receiving data
    @param timeout <int>

    @return communicator.Server
    """
    Server.allow_reuse_address = True

    if proto == 'tcp':
        srv = Server((ip, port), RequestHandler)
    elif proto == 'udp':
        srv = ServerUDP((ip, port), RequestHandlerUDP)
    else:
        return

    srv.node = node
    srv.stop = stop
    srv.timeout = timeout
    t = threading.Thread(target=srv.serve_forever)
    t.daemon = True
    t.start()

    return srv


class RequestHandler(socketserver.BaseRequestHandler):
    u"""
    Class to process requests. One request means one truck.
    """

    def handle(self):
        u"""
        Requset process func.
        call recv function, get data and send them to node
        """

        t = time.time()

        while time.time() - t < self.server.timeout:
            try:
                data = deserialize(recv(self.request,
                                        self.server.stop,
                                        self.server.timeout))
            except TypeError:
                data = None

            if data:
                t = time.time()
                self.server.process_message((self.client_address, self.request),
                                            data)

            time.sleep(0.000001)


class RequestHandlerUDP(socketserver.BaseRequestHandler):

    def handle(self):
        message, client = self.request
        deserialized_message = deserialize(message)
        self.server.process_message(client, deserialized_message)


class Server(socketserver.ThreadingMixIn, socketserver.TCPServer):
    u"""
    Class to provide server.
    For more info see https://docs.python.org/2/library/socketserver.html
    """

    def process_message(self, client, message):
        u"""
        function to throw message from client up to node
        @param client <socket.socket>
        @param message <dict>
        """
        self.node.process_message(client, message)


class ServerUDP(socketserver.ThreadingUDPServer):

    def process_message(self, client, message):
        self.node.process_message(client, message)


if __name__ == "__main__":

    srv = ServerUDP(('0.0.0.0', 51234), RequestHandlerUDP)
    srv.serve_forever()

    #TEST_OBJECT = 1004001
    #TEST_MESSAGE = 'Test text message'

    #class Node(object):
    #    def process_message(self, client, message):
    #        print("{} {}".format(client, message))

    #node = Node()
    #srv = create_server(node, 'localhost', 9999)
    #cli = create_client('localhost', 9999)
    #cli.sendall(serialize(dict(truck_id=TEST_OBJECT, msg=TEST_MESSAGE)))
    #time.sleep(1)
    #cli.close()
    #srv.shutdown()
    #srv.server_close()
