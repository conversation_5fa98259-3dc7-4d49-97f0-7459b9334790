FAILURE = "failure"


class AbstractState(object):
    u"""
    Class that describe state's interface for State Machine.

    Attributes:
        name (str): state name.

        node: AbstractNodeStateMachine instance.

        prev_state_saving_flag (bool): if True set to `last_state` in state machine
            while change state from it. If False than don't set it state to `last_state`.

    Examples:
        class SimpleState(AbstractState):
            def __init__(self, node):
                super(SimpleState, self).__init__(node, 'simple_state')
            
            def do_work(self):
                pass
    """
    def __init__(self, node, name, prev_state_saving_flag=True, ignore_permission=False,
                 ignore_missing_inputs=False, supress_failure=False, ignore_check=False, ignore_robomode=False):
        if name is None:
            raise AttributeError('Name should be string, NOT {}'.format(name))
        self._name = name
        self.node = node
        self.ignore_permission = ignore_permission
        self.ignore_missing_inputs = ignore_missing_inputs
        self.ignore_robomode = ignore_robomode
        self.supress_failure = supress_failure
        self.ignore_check = ignore_check
        self.prev_state_saving_flag = prev_state_saving_flag
        self.last_transition_kwargs = {}

    def get_name(self):
        u"""
        Return state name.

        Return:
            str.
        """
        return self._name

    def do_work(self):
        u"""
        Main state method. Calles it when move permition flag is True.
        """
        raise NotImplementedError()

    def on_transition_to(self):
        u"""
        Called when machine state changes it's state to current.
        """
        pass

    def on_transition_from(self):
        u"""
        Called when machine state changes it's state to other state from current.
        """
        pass


class FailureState(AbstractState):
    def __init__(self, node):
        super(FailureState, self).__init__(node, FAILURE, prev_state_saving_flag=False)

    def do_work(self):
        self.node.stop_control()
