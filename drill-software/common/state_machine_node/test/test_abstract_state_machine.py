#!/usr/bin/env python
PKG='state_machine_node'

import sys
import rospy
import rosparam
import time
import unittest
from mock import MagicMock

from state_machine_node.subscriber import SubscriberJam
from common_msgs.msg import State


import random
from state_machine_node import AbstractNodeStateMachine, AbstractState, FAILURE
from common_msgs.msg import MovePermission
from drill_msgs.msg import ManualState


FIRST_STATE = 'first_state'
SECOND_STATE = 'second_state'


class FirstState(AbstractState):
    def __init__(self, state_machine):
        super(FirstState, self).__init__(state_machine, FIRST_STATE)

    def on_transmition(self):
        pass

    def do_work(self):
        self.node.set_state(SECOND_STATE)

    def do_prohibit_state(self):
        pass

    def on_state_changed(self):
        pass 
        
        
class SecondState(AbstractState):
    def  __init__(self, state_machine):
        super(SecondState, self).__init__(state_machine, SECOND_STATE)
        self.counter = 0

    def on_transmition(self):
        self.counter = 0

    def do_work(self):
        self.node.set_state(FIRST_STATE)

    def do_prohibit_state(self):
        pass

    def on_state_changed(self):
        pass 


class SimpleStateMachine(AbstractNodeStateMachine):
    def drill_initialize(self, *args, **kwargs):
        first_state = FirstState(self)
        second_state = SecondState(self)

        self.set_current_state(first_state)
        self.add_states(second_state)


class TestAbstractNodeStateMachine(unittest.TestCase):
    def test_do_work(self):
        d = SimpleStateMachine("TestNode")
        d.do_work()
        self.assertIsInstance(d.state_machine.get_current_state(), SecondState)
        d.do_work()
        self.assertIsInstance(d.state_machine.get_current_state(), FirstState)

    def test__move_permission_callback(self):
        d = SimpleStateMachine("TestNode")
        permission = MovePermission()
        permission.source = 'test_class'
        permission.permission = False

        d._AbstractNodeStateMachine__move_permission_callback(permission)
        self.assertFalse(d.move_permission.is_work_permitted())

        permission = MovePermission()
        permission.source = 'test_class'
        permission.permission = True

        d._AbstractNodeStateMachine__move_permission_callback(permission)
        self.assertTrue(d.move_permission.is_work_permitted())

    def test__manual_state_callback(self):
        d = SimpleStateMachine("TestNode")

        message = ManualState()
        message.node_name = d._name
        message.mode = SECOND_STATE
        d._AbstractNodeStateMachine__manual_state_callback(message)
        self.assertIsInstance(d.state_machine.get_current_state(), SecondState)

        message = ManualState()
        message.node_name = d._name
        message.mode = 'prev'
        d._AbstractNodeStateMachine__manual_state_callback(message)
        self.assertIsInstance(d.state_machine.get_current_state(), SecondState)

        d.switch_to_failure()
        self.assertNotIsInstance(d.state_machine.get_current_state(), SecondState)

        message = ManualState()
        message.node_name = d._name
        message.mode = 'prev'
        d._AbstractNodeStateMachine__manual_state_callback(message)
        self.assertIsInstance(d.state_machine.get_current_state(), SecondState)

    def test_set_params_as_attr(self):
        d = SimpleStateMachine("TestNode")
        param = rosparam.get_param('Global/TestNode')
        self.assertEqual(d.test_param, param['test_param'])


if __name__ == '__main__':
    import rosunit
    rosunit.unitrun(PKG, 'abstract_state_node', TestAbstractNodeStateMachine)
