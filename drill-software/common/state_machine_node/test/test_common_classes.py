#!/usr/bin/env python
PKG='state_machine_node'

import rospy
import sys
import time
import unittest

from state_machine_node.state_machine import MovePermissionProcessor, StateMap

from common_msgs.msg import MovePermission


class SomeState(object):
    def get_name(self):
        return "SomeName"


class TestMovePermissionProcessor(unittest.TestCase):
    def test_is_work_permitted_one_source(self):
        permission = MovePermissionProcessor()

        message = MovePermission()
        message.permission = False
        message.source = 'source'

        permission.add_message(message)

        self.assertFalse(permission.is_work_permitted())

        message = MovePermission()
        message.permission = True
        message.source = 'source'

        permission.add_message(message)

        self.assertTrue(permission.is_work_permitted())

    def test_is_work_permitted_many_source(self):
        permission = MovePermissionProcessor()

        message = MovePermission()
        message.permission = False
        message.source = 'source'

        permission.add_message(message)

        message = MovePermission()
        message.permission = False
        message.source = 'source2'

        permission.add_message(message)

        self.assertFalse(permission.is_work_permitted())

        message = MovePermission()
        message.permission = True
        message.source = 'source'

        permission.add_message(message)

        self.assertFalse(permission.is_work_permitted())

        message = MovePermission()
        message.permission = True
        message.source = 'source2'

        permission.add_message(message)
        self.assertTrue(permission.is_work_permitted())


class TestStateMap(unittest.TestCase):
    def test_append(self):
        state_map = StateMap()
        state_map.append(SomeState())

        with self.assertRaises(AttributeError) as context:
            state_map.append(SomeState())

    def test_get(self):
        state = SomeState()
        state_map = StateMap()
        state_map.append(state)

        self.assertEqual(state_map.get('SomeName'), state)
        self.assertIsNone(state_map.get('NoneState'))


if __name__ == '__main__':
    import rosunit
    rosunit.unitrun(PKG, 'move_permission_processor', TestMovePermissionProcessor)
    rosunit.unitrun(PKG, 'state_map', TestStateMap)