#!/usr/bin/env python
# -*- coding: utf-8 -*-

PKG='state_machine_node'

import rospy
import sys
import time
import unittest

from state_machine_node.subscriber import SubscriberJam
from state_machine_node.subscriber import NO_DATA_WARNING, TIMEOUT_WARNING
from common_msgs.msg import State


## A sample python unit test
class TestSubscriberJam(unittest.TestCase):

    def test_SubscriberJam_add_subscriber(self):
        s = SubscriberJam()
        s.add_subscriber('topic_name_2', State, 'state')
        try:
            self.assertEqual(s.state, None)
        except Exception as e:
            self.fail()

    def test_SubscriberJam_add_subscriber_double_add(self):
        s = SubscriberJam()
        s.add_subscriber('topic_name_2', State, 'state')

        with self.assertRaises(AttributeError) as context:
            s.add_subscriber('topic_name_2', State, 'state')

    def test_getattr_getitem_equals(self):
        s = SubscriberJam()
        s.add_subscriber('/topic_name_2', State, 'state')
        s._SubscriberJam__inner_dict['state'].callback(object())

        self.assertEqual(s['state'], s.state)


    def test_SubscriberJam_process_no_timeout(self):

        def callback(*args, **kwargs):
            self.fail()

        s = SubscriberJam()
        s.add_subscriber('/topic_name_2', State, 'state', 1.0, 1.0, timeout_event_handler=callback)
        s._SubscriberJam__inner_dict['state'].callback(object())
        self.assertNotEqual(None, s.state)

    def test_SubscriberJam_timeout_not_shoving_up_data(self):
        
        def callback(timeout_type):
            self.assertEqual(timeout_type, NO_DATA_WARNING)
            raise RuntimeError()

        s = SubscriberJam()
        s.add_subscriber('topic_name_2', State, 'state', 0.0000001, 0.0000001,
            timeout_event_handler=callback)
        with self.assertRaises(RuntimeError) as context:
            s.process()

    def test_SubscriberJam_timeout_data_obsolescence(self):
        
        def callback(text, timeout_type):
            self.assertEqual(timeout_type, TIMEOUT_WARNING)
            raise RuntimeError()

        s = SubscriberJam()
        s.add_subscriber('topic_name_2', State, 'state', 5, 0.0000001,
            timeout_event_handler=callback, text='HELLO')
        s.process()
        s._SubscriberJam__inner_dict['state'].callback(object())
        time.sleep(0.01)
        with self.assertRaises(RuntimeError) as context:
            s.process()



if __name__ == '__main__':
    import rosunit
    rospy.init_node('test_node')
    rosunit.unitrun(PKG, 'subscriber_jam', TestSubscriberJam)
