u"""
This script provides functions that convert [lon, lat] coords to [x, y] and vice versa.
Before use public functions from this script you need run launch file with yaml params.

Usage:
>> ## run launch file or attach params with yaml file.
>> import wgs84_converter.ros_converter as ros_converter
>> ros_converter.convert_to_local(91, 53)
0, 0
>> ros_converter.convert_to_lonlat(0, 0)
91, 53

"""

import rospy

from .wgs84_converter_2 import Converter, Point


class _RosConverter(Converter):
    u"""
    This convertor use known global params to initialize Parent class Convertor.
    """
    def __init__(self):
        node_params = rospy.get_param('Global')
        ent_params = node_params['enterprise']
        location = ent_params['location']
        base_point = (location['base_point']['lon'], location['base_point']['lat'])
        ref_point_1 = Point(
            (location['ref_point_1']['local_x'], location['ref_point_1']['local_y']),
            (location['ref_point_1']['lat'], location['ref_point_1']['lon']),
        )
        ref_point_2 = Point(
            (location['ref_point_2']['local_x'], location['ref_point_2']['local_y']),
            (location['ref_point_2']['lat'], location['ref_point_2']['lon']),
        )
        ref_point_3 = Point(
            (location['ref_point_3']['local_x'], location['ref_point_3']['local_y']),
            (location['ref_point_3']['lat'], location['ref_point_3']['lon']),
        )
        zone = location['utm_zone']

        super(_RosConverter, self).__init__(
            base_point,
            ref_point_1,
            ref_point_2,
            ref_point_3,
            zone
        )


class _SingletonRosConverter(_RosConverter):
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(_SingletonRosConverter, cls).__new__(cls)
        return cls._instance


def convert_to_local(lon, lat):
    u"""
    Convert lon and lat to x and y.

    Args:
        lon, lat (float).

    Return:
        x - float, y - float.
    """

    __ros_convertor = _SingletonRosConverter()

    return __ros_convertor.covertToLocal(lon, lat)


def convert_to_lonlat(x, y):
    u"""
    Convert x and y to lon and lat.

    Args:
        x, y (float).

    Return:
        lon - float, lat - float.
    """

    __ros_convertor = _SingletonRosConverter()

    return __ros_convertor.covertToWGS84(x, y)
