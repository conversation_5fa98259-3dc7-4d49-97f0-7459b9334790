#!/usr/bin/env python
# -*- coding: utf-8 -*-


# YET ANOTHER COORDS CONVERTER

from pyproj import Proj
import math
from collections import namedtuple
import numpy as np
from itertools import chain
import rospy


Point = namedtuple("Point", ["local", "wgs"])
OFFSET = 500


class ConverterOld(object):

    def __init__(self, base_wgs, point1, point2, point3,  zone):
        self._convert = Proj(proj='utm', zone=zone, ellps='WGS84')

        base_x, base_y = self._convert(*base_wgs)

        point1 = Point((0, 0), base_wgs)
        point2 = Point((OFFSET, 0), self._convert(base_x + OFFSET, base_y, inverse=True))
        point3 = Point((0, OFFSET), self._convert(base_x, base_y + OFFSET, inverse=True))

        subst = lambda v: v[0] - v[1]
        self._local_wgs_base = base_x, base_y

        self._p1 = Point(
            point1.local,
            map(subst, zip(self._convert(*point1.wgs), self._local_wgs_base))
        )
        self._p2 = Point(
            point2.local,
            map(subst, zip(self._convert(*point2.wgs), self._local_wgs_base))
        )
        self._p3 = Point(
            point3.local,
            map(subst, zip(self._convert(*point3.wgs), self._local_wgs_base))
        )

        self.local_A = np.array([
            [(self._p2.wgs[0] - self._p1.wgs[0]), (self._p3.wgs[0] - self._p1.wgs[0])],
            [(self._p2.wgs[1] - self._p1.wgs[1]), (self._p3.wgs[1] - self._p1.wgs[1])]

        ])

        self.local_B = np.array([
            [self._p2.local[0] - self._p1.local[0], self._p3.local[0] - self._p1.local[0]],
            [self._p2.local[1] - self._p1.local[1], self._p3.local[1] - self._p1.local[1]]
        ])

        self.local_A_inv = np.linalg.inv(self.local_A)
        self.local_B_inv = np.linalg.inv(self.local_B)

        self.wgs_A = np.array([
            [self._p2.local[0] - self._p1.local[0], self._p3.local[0] - self._p1.local[0]],
            [self._p2.local[1] - self._p1.local[1], self._p3.local[1] - self._p1.local[1]]
        ])
        self.wgs_B = np.array([
            [(self._p2.wgs[0] - self._p1.wgs[0]), (self._p3.wgs[0] - self._p1.wgs[0])],
            [(self._p2.wgs[1] - self._p1.wgs[1]), (self._p3.wgs[1] - self._p1.wgs[1])]
        ])

        # Обратные матрицы
        self.wgs_A_inv = np.linalg.inv(self.wgs_A)
        self.wgs_B_inv = np.linalg.inv(self.wgs_B)

    def to_wgs(self, x, y):

        # Матрицы перехода
        V = np.array([[x], [y]])
        _x, _y = chain(*np.matmul(self.wgs_A_inv, V).tolist())

        N = np.array([[_x], [_y]])
        nx, ny = chain(*np.matmul(self.wgs_B, N).tolist())

        nx = nx + self._local_wgs_base[0] + self._p1.wgs[0]
        ny = ny + self._local_wgs_base[1] + self._p1.wgs[1]

        return self._convert(nx, ny, inverse=True)

    def to_local(self, lat, lon):
        x, y = self._convert(lon, lat)
        x -= self._local_wgs_base[0]
        y -= self._local_wgs_base[1]

        V = np.array([[x], [y]])
        _x, _y = chain(*np.matmul(self.local_A_inv, V).tolist())

        N = np.array([[_x], [_y]])
        nx, ny = chain(*np.matmul(self.local_B, N).tolist())


        nx = nx - self._p1.local[0]
        ny = ny - self._p1.local[1]

        return nx, ny

    def covertToLocal(self, lat, lon):
        return self.to_local(lat, lon)

    def covertToWGS84(self, x, y):
        return self.to_wgs(x, y)


class Converter(object):

    u"""
    Простой преобразователь координат, без необходимости задавать референсные
    точки.
    """

    def __init__(self, base_wgs, point1, point2, point3,  zone):
        # Todo добавить проверку на Point-instance

        self._convert = Proj(proj='utm', zone=zone, ellps='WGS84')
        self._base_x, self._base_y = self._convert(*base_wgs)

    def to_wgs(self, x, y):
        _x = self._base_x + x
        _y = self._base_y + y
        return self._convert(_x, _y, inverse=True)

    def to_local(self, lon, lat):
        x, y = self._convert(lon, lat)
        x -= self._base_x
        y -= self._base_y
        return x, y

    def covertToLocal(self, lon, lat):
        return self.to_local(lon, lat)

    def covertToWGS84(self, x, y):
        return self.to_wgs(x, y)


# test
if __name__ == "__main__":

    base_point = 91.155456, 53.689980
    zone = '46U'

    p1 = 91.154202, 53.685210
    p2 = 91.153700, 53.685435
    p3 = 91.153728, 53.684604
    p4 = 91.152941, 53.684555

    c = Converter(base_point, base_point, base_point, base_point, zone)
    p11 = c.covertToLocal(*p1)
    p12 = c.covertToLocal(*p2)
    p13 = c.covertToLocal(*p3)
    p14 = c.covertToLocal(*p4)

    print(p11)
    print(p12)
    print(p13)
    print(p14)

    p21 = c.covertToWGS84(*p11)
    p22 = c.covertToWGS84(*p12)
    p23 = c.covertToWGS84(*p13)
    p24 = c.covertToWGS84(*p14)
    print(p21)
    print(p22)
    print(p23)
    print(p24)

    x, y = [], []

    #import rosbag
    #import matplotlib.pyplot as plt

    #b = rosbag.Bag("/home/<USER>/robod/bags/lidar/unload_2.bag")

    #for m in b.read_messages(topics=["/SC/state"]):
    #    x.append(m.message.position.x)
    #    y.append(m.message.position.y)

    #plt.plot(x, y)
    #plt.show()


    #print ""
    #print 91.102631474, 53.7729378033
    #print rp1
    #print rp2
    #print bp_utm_x
    #print bp_utm_y

    #c = Converter(
    #    base_point,
    #    ref_point_1,
    #    ref_point_2,
    #    ref_point_3,
    #    zone
    #)

    #t1 = c.to_wgs(0, 0)
    #print c.to_local(-7.4983992299, 32.1299080465)
    #print c.to_wgs(*c.to_local(-7.4983992299, 32.1299080465))
    #print c.to_local(-7.415993, 32.128839)
    #print c.to_local(-7.498219, 32.176520)
    #print c.to_local(37.355561, 55.685267)
    #print c.to_local(37.355518, 55.677533)

    #p1 = Point((0, 0), (37.360610, 55.695233))
    #p2 = Point((405, 0), (37.371961, 55.695148))
    #p3 = Point((0, -319), (37.360524, 55.692288))

    #c = Converter(wgs, p1, p2, p3, zone)

    #from functools import partial
    #import json

    #r = partial(round, ndigits = 8)

    #"""
    #print map(r,list(reversed(c.to_wgs(0, 0)))), list(reversed(wgs))
    #print map(r,list(reversed(c.to_wgs(403, -370)))), list(reversed(park.wgs  ))
    #print map(r,list(reversed(c.to_wgs(387, -103)))), list(reversed(unload.wgs))
    #print map(r,list(reversed(c.to_wgs(179, -361)))), list(reversed(load.wgs  ))
    #"""

    #with open("/home/<USER>/scripts/map/map_data.js", "w") as f:
    #    coords = [
    #        map(r,list(reversed(c.to_wgs(0, 0)))),
    #        map(r,list(reversed(c.to_wgs(400, 0)))),
    #        map(r,list(reversed(c.to_wgs(0, -300)))),
    #        map(r,list(reversed(c.to_wgs(380, -108)))),
    #    ]

    #    f.write("coords = %s;" % json.dumps(coords))
