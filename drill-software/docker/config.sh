#!/bin/bash

TAG_NAME=tests
DOCKERHUB_REPOSITOTY=registry.gitlab.com/vistmt/drill-mover
CONTAINER_NAME=drill-mover-dev

# if uname -m | grep -q 'aarch64'; then
#    TAG_NAME="$TAG_NAME""arm64"
# elif uname -m | grep -q 'x86_64'; then
#    TAG_NAME="$TAG_NAME""amd64"
# else
#    echo "unknown architecture"
#    exit
# fi

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

DEV_PATH_SYMLINK="$(dirname "$SCRIPT_DIR")"

# FLAGS_FOR_DEV="--privileged --net=host"
FLAGS_FOR_DEV="--privileged -v $DEV_PATH_SYMLINK:/catkin_ws/src/drill-software  --name=$CONTAINER_NAME -p 9095:9095 -p 11311:11311 -p 6000:6000"
# -p 9095:9095 -p 11311:11311  -p 49152-49554:49152-49554 -p 5017:5017 -p 5555:5555

FLAGS_FOR_ENV_VARS="-e DEV_PATH_SYMLINK=$DEV_PATH_SYMLINK"

echo "docker/config.sh: TAG_NAME is" $TAG_NAME
echo "docker/config.sh: DOCKERHUB_REPOSITOTY is" $DOCKERHUB_REPOSITOTY
echo "docker/config.sh: DEV_PATH_SYMLINK is" $DEV_PATH_SYMLINK
echo "docker/config.sh: SCRIPT_DIR is" $SCRIPT_DIR
