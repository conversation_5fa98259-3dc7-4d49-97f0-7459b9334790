#!/bin/bash
set -e

# setup ros environment
source "/opt/ros/$ROS_DISTRO/setup.bash"
source /catkin_ws/devel/setup.bash

roscore &

sleep 1

rostopic pub /main_mode drill_msgs/VehicleBehaviorMode "header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: ''
mode: 'drilling'" -r 10 &

rostopic pub /permission common_msgs/MovePermission "header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: ''
info:
  header:
    seq: 0
    stamp: {secs: 0, nsecs: 0}
    frame_id: ''
  vehid: 0
  type: ''
  msg_ver: ''
  comm: ''
permission: 1
source: ''" &

rostopic pub /driller_action common_msgs/Action "header:
  seq: 0
  stamp: {secs: 0, nsecs: 0}
  frame_id: ''
info:
  header:
    seq: 0
    stamp: {secs: 0, nsecs: 0}
    frame_id: ''
  vehid: 0
  type: ''
  msg_ver: ''
  comm: ''
type: ''
data: '{\"first_shaft_flag\": true, \"drill_depth\": 5.63226229, \"pull_depth\": 1.27, \"depth\": 3.0, \"tower_angle\": 0.0, \"id\": 1}'
seq: 0" -l &

rostopic pub /current_robomode drill_msgs/BoolStamped "header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: ''
value: 1" -l &

sleep 1

roslaunch drill_launch_pack drilling_simulate_simple.launch

