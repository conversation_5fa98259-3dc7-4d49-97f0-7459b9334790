# HOWTO Use Docker with PyCharm to Run and Debug Python Apps

 Usually using Docker imposes restrictions on development process in part of run and debug comparing to direct running 
 on host. On other hand, there are more and more cases where docker is a good tradeoff (a vivid example is developing 
 for ROS Melodic with Python2 on modern Ubuntu >= 20.04).
 
This readme gives a hint of possible easy way to integrate Docker and PyCharm.
It is based on PyCharm's official doc [`Configure an interpreter using Docker`](https://www.jetbrains.com/help/pycharm/using-docker-as-a-remote-interpreter.html) 
applied to drill software.

# Build Docker Image
With preconfigured Dockerfile, it's easy as:

```bash
./docker/build_image.sh
```
The significant part of Dockerfile is ```ENTRYPOINT ["/drill_dev_entrypoint.sh"]```, 
which setups environment before running Python app.

# Configure Pycharm
Following steps are according to [https://www.jetbrains.com/help/pycharm/using-docker-as-a-remote-interpreter.html](https://www.jetbrains.com/help/pycharm/using-docker-as-a-remote-interpreter.html).

Here are particular example screenshots for drill software module.

## Connect to Docker Daemon

File -> Settings (or Ctrl+Alt+S) -> Build, Execution, Deployment -> Docker

![](img/DockerConnect.png)

## Configuring Docker as a Remote Interpreter
File -> Settings (or Ctrl+Alt+S) ->
Project -> Python Interpreter -> Add

![](img/PythonInterpreter.png)

Result:
![](img/PythonInterpreterSettings.png)

### Important for autocompletion
This part helps autocompletion for ROS packages work with remote interpreter: 
add ROS paths manually in PyCharm remote interpreter settings.
In Project Settings -> Python Interpreter in Python Interpreter dropdown list select Show All.
![](img/PythonInterpreters.png)
In Python Interpreters window select your Remote Python Interpreter and click tree icon `Show paths for selected interpretr` in top right menu.
Add `/catkin_ws/devel/lib/python2.7/dist-packages` and `/opt/ros/melodic/lib/python2.7/dist-packages` to paths.
Note: In case of autocompletion issues `Reload List of Paths` button here could be the option to try.
![](img/PythonInterpretersPaths.png)

## Add Run/Debug Configuration
Right Click at your script editor and select Run or
create configuration in Run -> Edit Configuration -> + -> Add New Configuration -> Python.

Currently, it will return error at running, so a bit more configuring is needed.
![](img/RunError.png)

## Edit Run/Debug Configuration to Correct Docker Container Options

In Path mapping set Host path to drill-software on your host computer (/home/<USER>/catkin_zyfra_ws/src/drill-software for example) and Container Path to /catkin_ws/src/drill-software


In Edit Docker Container Settings window 
* Do the same as in Path Mapping: set Host path to drill-software on your host computer (/home/<USER>/catkin_zyfra_ws/src/drill-software for example) and Container Path to /catkin_ws/src/drill-software
* In Edit Docker Container Settings edit run options (remove `--entrypoint=` and add `--net=host`) in a following way:
```
--rm --net=host
```
![](img/RunConfPathMapping.png)
![](img/RunConfDockerCont.png)

# Run and Debug
Profit. Debug as usual!

![](img/Debug.png)

# Roslaunch
You could run roslaunch (or, generally, any other command) in the similar way.
![](img/RoslaunchDocker.png)