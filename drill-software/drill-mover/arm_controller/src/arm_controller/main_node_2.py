#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import json

from .constants import OP<PERSON><PERSON>, OP<PERSON>, CL<PERSON>ING, CLOSED, FAILURE
from state_machine_node import AbstractNodeStateMachine
from arm_controller.models import OpenState, OpeningState, ClosedState, ClosingState

from common_msgs.msg import Action, MovePermission, MovingError
from drill_msgs.msg import (StateMachineStatus, InternalError,
                            VehicleBehaviorMode, FloatStamped, ArmState, DrillState)


class ArmControllerNode(AbstractNodeStateMachine):

    def drill_initialize(self, *args, **kwargs):
        # super(ArmControllerStateNode, self).initialize()

        self.set_current_state(OpenState(self))

        self.add_states([OpeningState(self), ClosedState(self), ClosingState(self)])

        self.action = None
        self.new_action_flag = False
        self.do_open = False

        self.arm_ctrl = 0

        self.arm_ctrl_pub = rospy.Publisher(self.global_params['topics']['arm_control'],
                                            FloatStamped, queue_size=10)

        self.arm_present = self.global_params['system_flags']['arm_present']

    def subscribers_initialize(self):

        self.add_subscriber(self.global_params['topics']['arm_switch_state'], ArmState,
                            'arm_switch_state')

        self.add_subscriber(self.global_params['topics']['main_mode'], VehicleBehaviorMode,
                            'main_mode')

        rospy.Subscriber(self.global_params['topics']['arm_action'], Action,
                         self.action_callback)

        self.add_subscriber(self.global_params['topics']['drill_state'],
                            DrillState, 'drill_state')

    def stop_control(self):
        self.arm_ctrl = 0

    def publish_arm_ctrl(self):
        message = FloatStamped()
        message.header.stamp = rospy.Time.now()
        message.value = self.arm_ctrl
        self.arm_ctrl_pub.publish(message)

    def action_callback(self, message):
        try:
            read_json = json.loads(message.data)
            required_keys = (
                'open',
            )
            for k in required_keys:
                if k not in read_json:
                    err_msg = "Action data is incomplete or invalid"
                    self.handle_internal_error(error_message=err_msg)
                    return

            # Все ок - сохраняем задание
            if (read_json['open'] and self.get_current_state().get_name() != OPEN) or \
                    (not read_json['open'] and self.get_current_state().get_name() != CLOSED):
                self.action = message
                self.new_action_flag = True
                self.do_open = read_json['open']
                self._cur_action_seq = message.seq
                self._last_action_seq = message.seq
            else:
                self.log(
                    "Already in requested state, do nothing!",
                    event=self.global_params['events']['already_done']
                )
        except ValueError:
            err_msg = "Action data is not in valid json format"
            self.handle_internal_error(error_message=err_msg)
            return

    def check_data(self):
        if not self.arm_present:
            self.set_state(OPEN)
            return False

        if not self.subs.drill_state.is_valid:
            return False

        is_error = False

        # Проверяем, что текущий режим позвляет работать, если нет - останавливаем работу
        if self.subs.main_mode is None or (self.new_action_flag and self.subs.main_mode.mode not in self.allowed_modes):
            # err_msg = "Main mode [{}] does't allow arm controlling".format(self.main_mode)
            # self.handle_internal_error(error_message=err_msg)
            is_error = True

        if self.subs.main_mode is not None and (self.subs.main_mode.mode == VehicleBehaviorMode.REMOTE or
                self.subs.main_mode.mode == VehicleBehaviorMode.REMOTE_WAIT or
                self.subs.main_mode.mode == VehicleBehaviorMode.REMOTE_PREPARE):
            is_error = True
        
        return not is_error

    def do_work_after(self):
        self.publish_arm_ctrl()
