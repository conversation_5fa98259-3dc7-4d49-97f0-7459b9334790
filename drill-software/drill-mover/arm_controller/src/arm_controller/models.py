#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

from .constants import OPENING, OPEN, CLOSING, CLOSED
from state_machine_node import AbstractState


class OpeningState(AbstractState):
    def __init__(self, node):
        super(OpeningState, self).__init__(node, OPENING)

        self.time_open_push_started = rospy.get_time()

    def on_transition_to(self):
        self.time_open_push_started = rospy.get_time()

    def do_work(self):
        self.node.arm_ctrl = -1
        if rospy.get_time() - max(self.node.get_change_state_timestamp(), self.node.last_empty_loop_ts) > self.node.opening_time:
            err_msg = 'Could not open arm'
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            error_type=err_type,
                                            event=self.node.global_params['events']['rc_open_arm'])

        if self.node.global_params['system_flags']['arm_grip_sensor_present']:
            stuck_condition = (self.node.subs.arm_switch_state.grip and rospy.get_time() -  max(self.node.get_change_state_timestamp(), self.node.last_empty_loop_ts) > self.node.no_reaction_time)
        else:
            stuck_condition = self.node.subs.arm_switch_state.closed and rospy.get_time() - max(self.node.get_change_state_timestamp(), self.node.last_empty_loop_ts) > self.node.no_reaction_time

        if stuck_condition:
            err_msg = 'Could not open arm: stuck or closed switch failed'
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            error_type=err_type,
                                            event=self.node.global_params['events']['rc_open_arm'])

        if not self.node.subs.arm_switch_state.open:
            self.time_open_push_started = rospy.get_time()

        if self.node.subs.arm_switch_state.open and rospy.get_time() - \
                self.time_open_push_started >= self.node.open_push_time:
            self.node.set_state(OPEN)


class OpenState(AbstractState):
    def __init__(self, node):
        super(OpenState, self).__init__(node, OPEN)

    def on_transition_to(self):
        self.node._cur_action_seq = -1

    def do_work(self):
        self.node.stop_control()
        if not self.node.subs.arm_switch_state.open:
            self.node.log("Restore open", loglevel=2)
            self.node.set_state(OPENING)
            return
        if self.node.new_action_flag and not self.node.do_open:
            self.node.set_state(CLOSING)


class ClosingState(AbstractState):
    def __init__(self, node):
        super(ClosingState, self).__init__(node, CLOSING)
        self.time_close_push_started = rospy.get_time()

    def on_transition_to(self):
        self.time_close_push_started = rospy.get_time()

    def do_work(self):
        if self.node.subs.drill_state.spindle_depth > self.node.max_depth_to_close:
            self.node.logwarn("Can't close arm, Head is too low! Opening..", period=2,
                         event=self.node.global_params['events']['head_too_low'])
            self.node.arm_ctrl = 0
            self.node.set_state(OPENING)
            return

        self.node.arm_ctrl = 1
        if rospy.get_time() - max(self.node.get_change_state_timestamp(), self.node.last_empty_loop_ts) > \
                self.node.closing_time:
            err_msg = 'Could not close arm'
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            error_type=err_type,
                                            event=self.node.global_params['events']['rc_close_arm'])

        if self.node.subs.arm_switch_state.open and \
                rospy.get_time() - max(self.node.get_change_state_timestamp(), self.node.last_empty_loop_ts) > \
                self.node.no_reaction_time:
            err_msg = 'Could not close arm: stuck or open switch failed'
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            error_type=err_type,
                                            event=self.node.global_params['events']['rc_close_arm'])

        if self.node.global_params['system_flags']['arm_grip_sensor_present']:
            if not self.node.subs.arm_switch_state.closed or not self.node.subs.arm_switch_state.grip:
                self.time_close_push_started = rospy.get_time()

            if self.node.subs.arm_switch_state.closed and self.node.subs.arm_switch_state.grip and \
                    rospy.get_time() - self.time_close_push_started >= self.node.close_push_time:
                self.node.set_state(CLOSED)
        else:
            if not self.node.subs.arm_switch_state.closed:
                self.time_close_push_started = rospy.get_time()

            if self.node.subs.arm_switch_state.closed and rospy.get_time() - self.time_close_push_started >= self.node.grip_push_time+self.node.close_push_time:
                self.node.set_state(CLOSED)


class ClosedState(AbstractState):
    def __init__(self, node):
        super(ClosedState, self).__init__(node, CLOSED)

    def on_transition_to(self):
        self.node._cur_action_seq = -1

    def do_work(self):
        self.node.stop_control()
        if self.node.subs.drill_state.spindle_depth > self.node.max_depth_to_close:
            self.node.logwarn("Head is too low! Opening..", period=2,
                         event=self.node.global_params['events']['head_too_low'])
            self.node.arm_ctrl = 0
            self.node.set_state(OPENING)
            return
        if (not self.node.subs.arm_switch_state.closed) or (self.node.global_params['system_flags']['arm_grip_sensor_present'] and not self.node.subs.arm_switch_state.grip):
            self.node.log("Restore closed", loglevel=2)
            self.node.set_state(CLOSING)
            return
        if self.node.new_action_flag and self.node.do_open:
            self.node.set_state(OPENING)

