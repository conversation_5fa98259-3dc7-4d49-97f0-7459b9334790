# Bags Recorder

Bags recorder is a node designed to maintain bags recording and backing up the bags related to the given event (similar to the way it is done in a car DVR).

Main node actions (states) are as follows:

## Regular Record

If `do_recording` parameter is `true`, node runs `rosbag record` processes (via popen) to record 2 bags simultaneously:
- with LIDAR;
- without LIDARs: to save space for events not concerning a perception system (drilling, logics, etc.).

If `do_recording` parameter is `false`, node considers recording is performed by another script.

If got event (msg in `topics/bags_save_cmd`) -> switch to **got_event** state.

[!] node should not process new events until current handling is finished.

## Got Event

Actions on new event are as follows:

Save current event timestamp.
Stop receiving new events (just check state in callback).

If `do_recording` parameter is `true`, stopping current record, starting new 
or wait for `wait_before_copy_min` minutes to be sure current file finishes, if `do_recording` parameter is `false` (wait could be ok, if split time is short).

When current file is finished (not active, wait time passed) and new record is started (if needed) -> go to **copying**.

Node also creates `event_description.txt` file with event info, `git describe` info (for `drill-mover` repo).
If we receive a new event with the same name, we add `event_description_add.txt` with new description (and add it to archive later if needed). 
It's done to give operator a time for event description writing.

## Copying

Check space, if not enough -> **low_space**.

Select bags, based on duration and parameters:
- `minutes_to_save` duration (if `minutes_to_save` > 0)
- or duration from last main_action or start + `extra_duration_min` (checking `min_save_minutes` and `max_save_minutes` boundaries)

Copy selected bags to a new folder (in `save_path`) with given in event msg `event_name` and text file comment from operator.

LIDAR bags copying logic is as follows:
- we copy full length of LIDAR and non-LIDAR bags if `with_lidar` flag in event msg is `true`;
- we copy full length of non-LIDAR bags and always copy short duration `lidars_minutes_to_save` of LIDAR bags when if `with_lidar` flag in event msg is `false`. 

When `cp` finished and files are here -> go to **archiving**.

## Archiving 

Archiving is performed if `do_tar` parameter is `true`. 
At this step, node archives previously copied files, including text file(s) with event description.
`tar` params are set to remove source files and leaving archive only.

When archiving is finished and files are here -> go to **regular_record**. 

## Low Space

Print error.

Check free space, if ok --> **regular_record**. 

## Failure

Should be activated on errors:
- error permission, 
- wrong dir,
- etc

## Event Format

Event info, defined in `BagsSaveCmd.msg`, contains:
- `name`: used for saved bags folder name;
- `minutes_to_save`: minutes in past to record; If `-1` value is given, we set this parameter to time from last main_action or start + `extra_duration_min` 
(checking `min_save_minutes` and `max_save_minutes` boundaries).
- `description`: event description from operator;
- `with_lidars`: if the event involves the perception system, LIDAR bags are needed and this flag should be set to save full length of LIDAR bags.

## Node Parameters

Node params are set in `nodes.yaml` in BagsRecorderNode part. Parameters list:
* `rate: 5` — rate to call main `work()` function of node;
* `drill_bags_recorder_lidar: "drill_bags_recorder_lidar"` — rosbag record node with lidars name (if `do_recording` `true`)
* `drill_bags_recorder_nolidar: "drill_bags_recorder_nolidar"` — rosbag record node with no lidars name (if `do_recording` `false`)
* [legacy]  `record_script_nolidar: "rosbag record --split --duration=1m -a -o drill_nolidar"` — rosbag record command with parameters needed
* [legacy]  `record_script_lidar: "rosbag record --split --duration=1m -a -o drill_withlidar"` — rosbag record command with parameters needed
* `bags_prefix_nolidar: "drill_nolidar"` — bag name prefix for bags without lidar data
* `bags_prefix_withlidar: "drill_withlidar"` — bag name prefix for bags with lidar data
* `split_duration_min: 1` — rosbag record param to split bags by duration
* `max_save_minutes: 30` — upper boundary for duration at copying state
* `min_save_minutes: 3` — low boundary for duration at copying state
* `do_recording: false` — starts rosbag record with given params if true or expect it is run externally (if false), in such a case be sure to sync settings (path, prefix, split duration)
* `extra_duration_min: 2` — extra duration to add to minutes_to_save (from last main action)
* `wait_before_copy_min: 1` — sleep given minutes before copying bags to wait current active bag would be finished, (should correspond to --split --duration param of rosbag record)
* `lidars_minutes_to_save: 3` — always save lidar bags of given lifetime
* `topics_nolidar` — list of topics to record in non-lidar bags (if `do_recording`)
* `topics_lidar` — list of topics to record in lidar bags (if `do_recording`) in addition to topics_nolidar
* `record_folder: "/root/bags"` — folder for regular recording (this folder is source to copy bags from on event)
* `save_path: "/root/bags_saved"` — folder to copy to on event
* `free_space_treshold_warn_gb: 3` — minimum free space to print warning (if less)
* `free_space_treshold_stop_gb: 2` — minimum free space to print error and stop recording if less (if `do_recording`)
* `do_tar: true` — flag to turn on/off archiving of saved files
