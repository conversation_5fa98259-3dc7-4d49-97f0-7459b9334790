#!/usr/bin/env python
from __future__ import division
import rospy
import rospkg
import subprocess, shlex, psutil
import os
import signal

import glob
from datetime import datetime, timedelta
import shutil

from drill_msgs.msg import BagsSaveCmd, StateMachineStatus
from common_msgs.msg import Action
from common.base_node import BaseNode


def terminate_ros_node(s):
    if check_node_is_running(s):
        os.system("rosnode kill " + s)


def run_node(script, node_name, cwd):
    command = script + " __name:=" + node_name
    command = shlex.split(command)
    pr = subprocess.Popen(command, stdin=subprocess.PIPE, cwd=cwd)
    return pr


def check_node_is_running(name):
    list_cmd = subprocess.Popen("rosnode list", shell=True, stdout=subprocess.PIPE)
    list_output = list_cmd.stdout.read()
    retcode = list_cmd.wait()
    assert retcode == 0, "List command returned %d" % retcode
    return name in list_output

def get_git_describe(cwd):
    res = ""
    try:
        # subprocess.check_output(['git', 'describe', '--always'], cwd=cwd).decode('ascii') for python3
        res = subprocess.check_output(['git', 'describe', '--always', '--tags'], cwd=cwd).strip()
    except err:
        res = "git_descrb_err"
    return res


class BagsRecorderNode(BaseNode):

    def __init__(self, name):
        super(BagsRecorderNode, self).__init__(name)

        self.lidars_minutes_to_save = None
        self.do_recording = None
        self.extra_duration_min = None
        self.min_save_minutes = None
        self.max_save_minutes = None
        self.last_main_action = None
        self.wait_before_copy_min = None
        self.node_state_pub = None
        self.event_sub = None
        self.save_path = None
        self.record_path = None
        self.free_space_treshold_warn_gb = None
        self.free_space_treshold_stop_gb = None
        self.record_script_lidar = None
        self.record_script_nolidar = None
        self.topics_nolidar = None
        self.topics_lidar = None
        self.record_nolidar_node_name = None
        self.record_with_lidar_node_name = None
        self.bags_prefix_withlidar = None
        self.bags_prefix_nolidar = None
        self.split_duration_min = None
        self.do_tar = None

        self.record_process_nolidar = None
        self.record_process_lidar = None
        self.event = None
        self.timer = None
        self.state = "Init"

    def on_start(self):
        self.start_time = rospy.Time.now()

        self.node_state_pub = rospy.Publisher("/bags_recorder_state", StateMachineStatus, queue_size=10)
        self.event_sub = rospy.Subscriber(self.global_params['topics']['bags_save_cmd'], BagsSaveCmd, self.callback_bags_event)
        self.record_with_lidar_node_name = self.node_params['drill_bags_recorder_lidar']
        self.record_nolidar_node_name = self.node_params['drill_bags_recorder_nolidar']
        self.topics_nolidar = self.node_params['topics_nolidar']
        self.topics_lidar = self.node_params['topics_lidar']
        self.bags_prefix_nolidar = self.node_params['bags_prefix_nolidar']
        self.bags_prefix_withlidar = self.node_params['bags_prefix_withlidar']
        self.record_path = self.node_params['record_folder']
        self.save_path = self.node_params['save_path']
        self.free_space_treshold_warn_gb = self.node_params['free_space_treshold_warn_gb']
        self.free_space_treshold_stop_gb = self.node_params['free_space_treshold_stop_gb']
        self.split_duration_min = self.node_params['split_duration_min']
        self.do_tar = self.node_params['do_tar']
        self.max_save_minutes = self.node_params['max_save_minutes']
        self.min_save_minutes = self.node_params['min_save_minutes']
        self.extra_duration_min = self.node_params['extra_duration_min']
        self.do_recording = self.node_params['do_recording']
        self.wait_before_copy_min = self.node_params['wait_before_copy_min']
        self.lidars_minutes_to_save = self.node_params['lidars_minutes_to_save']


        if self.do_recording:
            # old: full script in params, could use exceptions, etc, should sync split duration, prefix, etc with code
            # self.record_script_nolidar = self.node_params['record_script_nolidar']
            # self.record_script_lidar = self.node_params['record_script_lidar']

            # new: topics list in params, explicit
            # TODO: global topics names (?)
            topics_nolidar_str = ' '.join([str(item) for item in self.topics_nolidar])
            topics_lidar_str = ' '.join([str(item) for item in self.topics_lidar])

            # TODO: add --quiet to suppress console output
            self.record_script_nolidar = "rosbag record --split --duration=%dm %s -o %s --quiet --lz4" % (self.split_duration_min,
                                                                                                          topics_nolidar_str,
                                                                                                          self.bags_prefix_nolidar)
            self.record_script_lidar = "rosbag record --split --duration=%dm %s %s -o %s --quiet --lz4" % (self.split_duration_min,
                                                                                                           topics_nolidar_str,
                                                                                                           topics_lidar_str,
                                                                                                           self.bags_prefix_withlidar)

            if not os.path.isdir(self.record_path):
                self.log("Creating the bags folder %s" % self.record_path)
                os.mkdir(self.record_path)

            if not os.path.isdir(self.save_path):
                self.log("Creating the events folder %s" % self.save_path)
                os.mkdir(self.save_path)

            self.run_record()

        rospy.Subscriber(self.global_params['topics']['main_action'],
                         Action, self.main_action_callback)

        self.timer = rospy.Timer(rospy.Duration(1.0 / self.node_params['rate']), self.timer_callback)


    def do_work(self):
        # if self.last_main_action is not None:
        #     self.log("do_work: last main_action was %f seconds ago" % (
        #                 rospy.Time.now() - self.last_main_action.header.stamp).to_sec())
        # else:
        #     self.log("do_work: time from start is %f seconds" % (
        #                 rospy.Time.now() - self.start_time).to_sec())


        hdd_record_path = psutil.disk_usage(self.record_path)
        hdd_save_path = psutil.disk_usage(self.save_path)

        if hdd_record_path.free / (2**30) < self.free_space_treshold_warn_gb:  # GiB
            self.logerr("No free space to use record_path %s, free space is %f GiB" % (self.record_path, hdd_record_path.free / (2**30)))
            self.state = "low_space"
        if hdd_save_path.free / (2**30) < self.free_space_treshold_warn_gb:  # GiB
            self.logerr("No free space to use save_path %s, free space is %f GiB" % (self.save_path, hdd_save_path.free / (2**30)))
            self.state = "low_space"

        if hdd_record_path.free / (2 ** 30) < self.free_space_treshold_stop_gb:
            self.logerr("No free space, stopping record")
            terminate_ros_node(self.record_nolidar_node_name)
            terminate_ros_node(self.record_with_lidar_node_name)
            rospy.sleep(5)
            return

        rospack = rospkg.RosPack()
        package_path = rospack.get_path('bags_recorder')

        if self.event is not None:
            self.state = "got_event"
            cur_event = self.event
            self.event = None
            self.log("Event info received:\n"
                     "name: %s\n"
                     "description: \n%s\n"
                     "minutes_to_save: %d\n"
                     "with_lidars: %r" % (cur_event.name, cur_event.description, cur_event.minutes_to_save, cur_event.with_lidars))

            event_name = cur_event.name
            event_path = self.save_path + "/" + event_name

            date = datetime.now()

            if os.path.isdir(event_path):
                self.log("Event %s path exists, adding description" % event_name)
                # Add description
                with open(event_path + "/event_description_add.txt", "w") as filename:
                    filename.write("time onboard: %s\n"
                                   "name: %s\n"
                                   "description: \n%s\n"
                                   "minutes_to_save: %d\n"
                                   "with_lidars: %r\n"
                                   "git_describe: %r" % (date.strftime("%Y-%m-%d_%H-%M-%S"),
                                                         cur_event.name,
                                                         cur_event.description,
                                                         cur_event.minutes_to_save,
                                                         cur_event.with_lidars,
                                                         get_git_describe(package_path)))

                if self.do_tar:
                    self.state = "archiving"
                    tar_cmd = "tar -rvf %s.tar --remove-files event_description_add.txt" % event_name
                    # command = shlex.split(tar_cmd)
                    pr = subprocess.Popen(tar_cmd, stdin=subprocess.PIPE, shell=True, cwd=event_path)
                    retcode = pr.poll()
                    while retcode is None:
                        retcode = pr.poll()
                        self.log("tar (add description) is running")
                        rospy.sleep(1)

                    if retcode != 0:
                        self.logerr("tar (add description) retcode is %d" % retcode)
                        self.state = "error"
            else:
                if cur_event.minutes_to_save <= 1:
                    if self.last_main_action is not None:
                        cur_event.minutes_to_save = (rospy.Time.now() - self.last_main_action.header.stamp).to_sec() / 60.0
                        self.log("Setting minutes_to_save to time from last action %f" % cur_event.minutes_to_save)
                    else:
                        cur_event.minutes_to_save = (rospy.Time.now() - self.start_time).to_sec() / 60.0
                        self.log("Setting minutes_to_save to time from start %f" % cur_event.minutes_to_save)

                if cur_event.minutes_to_save > self.max_save_minutes:
                    cur_event.minutes_to_save = self.max_save_minutes
                    self.log("Setting minutes_to_save to max_save_minutes %f" % cur_event.minutes_to_save)
                if cur_event.minutes_to_save < self.min_save_minutes:
                    cur_event.minutes_to_save = self.min_save_minutes
                    self.log("Setting minutes_to_save to min_save_minutes %f" % cur_event.minutes_to_save)

                cur_event.minutes_to_save += self.extra_duration_min

                if self.do_recording:
                    self.log("Restarting record nodes to finish current active bags")
                    terminate_ros_node(self.record_nolidar_node_name)
                    terminate_ros_node(self.record_with_lidar_node_name)
                    self.run_record()
                    rospy.sleep(1)
                else:
                    self.state = "waiting"
                    rospy.sleep(self.wait_before_copy_min * 60.0)

                self.state = "copying"
                list_of_files_nolidar = [
                    file for file in glob.glob(self.record_path + '/*.bag')
                    if self.bags_prefix_nolidar in file and
                       (date - datetime.fromtimestamp(
                           os.path.getmtime(file))).total_seconds() <= cur_event.minutes_to_save * 60
                ]
                list_of_files_withlidar = []
                lidar_minutes_to_save = cur_event.minutes_to_save   # full length of lidar bags
                if not cur_event.with_lidars:
                    lidar_minutes_to_save = self.lidars_minutes_to_save # some default length of lidar bags

                list_of_files_withlidar = [
                    file for file in glob.glob(self.record_path + '/*.bag')
                    if self.bags_prefix_withlidar in file and
                       (date - datetime.fromtimestamp(
                           os.path.getmtime(file))).total_seconds() <= lidar_minutes_to_save * 60
                ]

                list_of_files = list_of_files_nolidar + list_of_files_withlidar

                # list_of_files = [
                #     file for file in glob.glob(self.record_path + '/*.bag')
                #     if (cur_event.with_lidars or self.bags_prefix_nolidar in file) and
                #        (date - datetime.fromtimestamp(os.path.getmtime(file))).total_seconds() <= cur_event.minutes_to_save * 60
                # ]

                # for file in glob.glob(self.record_path + '/*.bag'):
                #     print("file %s time diff %d" % (file, (date - datetime.fromtimestamp(os.path.getmtime(file))).total_seconds()))

                self.log("List of bags to save created in the last %d minutes %r: " % (cur_event.minutes_to_save, list_of_files))

                os.mkdir(event_path)

                for filename in list_of_files:
                    # if cur_event.with_lidars or "nolidar" in filename:
                    shutil.copy2(filename, event_path)

                with open(event_path + "/event_description.txt", "w") as filename:
                    filename.write("time onboard: %s\n"
                                   "name: %s\n"
                                   "description: \n%s\n"
                                   "minutes_to_save: %d\n"
                                   "with_lidars: %r\n"
                                   "git_describe: %r" % (date.strftime("%Y-%m-%d_%H-%M-%S"),
                                                         cur_event.name,
                                                         cur_event.description,
                                                         cur_event.minutes_to_save,
                                                         cur_event.with_lidars,
                                                         get_git_describe(package_path)))

                if self.do_tar:
                    self.state = "archiving"
                    tar_cmd = "tar -cvf %s.tar --remove-files *" % event_name
                    # command = shlex.split(tar_cmd)
                    pr = subprocess.Popen(tar_cmd, stdin=subprocess.PIPE, shell=True, cwd=event_path)
                    retcode = pr.poll()
                    while retcode is None:
                        retcode = pr.poll()
                        self.log("tar is running")
                        rospy.sleep(1)

                    if retcode != 0:
                        self.logerr("tar retcode is %d" % retcode)
                        self.state = "error"

                    self.log("Processed event. Bags archive path is %s. "
                             "You could edit event_description.txt inside the archive to provide more detail about the event. "
                             "Please, send these archive to developers." % event_path)
                else:
                    self.log("Processed event. Bags path is %s. "
                             "You could edit event_description.txt in this folder to provide more detail about the event. "
                             "Please, send these bags to developers." % event_path)

        self.state = "regular_record"




    def timer_callback(self, event):
        # self.log("timer_callback")
        state_msg = StateMachineStatus()
        state_msg.header.stamp = rospy.Time.now()
        state_msg.status = self.state
        self.node_state_pub.publish(state_msg)

    def main_action_callback(self, msg):
        self.last_main_action = msg

    def on_stop(self):
        terminate_ros_node(self.record_nolidar_node_name)
        terminate_ros_node(self.record_with_lidar_node_name)

    def callback_bags_event(self, msg):
        self.event = msg

    def run_record(self):
        self.record_process_nolidar = run_node(self.record_script_nolidar,
                                               self.record_nolidar_node_name, self.record_path)
        # self.record_process_lidar = run_node(self.record_script_lidar,
        #                                      self.record_with_lidar_node_name, self.record_path)


if __name__ == '__main__':
    try:
        node = BagsRecorderNode("BagsRecorderNode")
        node.work()
    except rospy.ROSInterruptException:
        pass
