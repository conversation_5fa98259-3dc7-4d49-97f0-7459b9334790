
# Cats calibration


## Goal

![](https://habrastorage.org/webt/jl/ab/-c/jlab-cwy2tkluozerzp7p8rhli8.png)

The goal of this procedure is to find:
1) The dead-band values for left and right cats control. To define dead-bands we need to find minimum controls, when drill just start moving forward and backward. 
Thus, dead-band is a range `[deadband_value_neg, deadband_value_pos]` where control makes no sense.
2) The range of control possible `[min_value, max_value]`: maximum forward and backward control values for left and right cats. 
We need to find such a control value, that increasing it has no effect.

## Pre-requirements 

>**Safety warning!** Before you start calibration procedure make sure that area around the drill is clear from other vehicles, people and obstacles!
> _Ask assistant to watch for possible collisions while your attention is focused on numbers!_

Expected preconditions to perform cats calibration:
- the vehicle is on a flat surface with enough free space to move around, engine is running;
- software is run as usual;
- you and drill are free from any other tasks: no drilling, demonstrations, checks and so on;
- network connection is stable
- operator must control that no obstacles exist and prevent collisions by switch to manual control.

>**Important!** Do not try to execute drilling plan and calibrate at the same time!

>**Important!** Read this manual to the end before you start.

## Step 1: Prepare tools

### 1.1 Open configuration tool and connect it to drill 

Open a new terminal.

Run configuration tool with following commands:
```commandline
cd configuration-tool
python3 main.py
```

Using dropdown menu in bottom left corner select the drill you want to calibrate. Make sure it is online (marked with green circle)

![](https://habrastorage.org/webt/pg/jw/p4/pgjwp4vaktaqzarqhjhxka1vgvs.png)

Press **Import** button.

The list of all available parameters will appear. Use "Cats calibration" filter on the left to display only relevant parameters.
You can fold groups of `polynomial_pos` and `polynomial_neg` params as you don't need them now.
![](https://habrastorage.org/webt/wu/k1/pa/wuk1par908twfcppkerygz5njbk.png)

### 1.2 Open Foxglove to view drill telemetry

Run Foxglove Studio, you will see the followng window:

![](https://habrastorage.org/webt/lb/mv/sn/lbmvsnhu54febr9ujk5ia6qxn80.png)

Select ws://<drill_IP>:9090 in "Recent data sources" list. (ws://*********:9090 for Hudbay Perf 02) 

If you don't have one, create it by pressing "Open connection" option.

![](https://habrastorage.org/webt/20/eb/is/20ebiskddjfrk_ouwnfhsh0fabg.png)

Select Rosbridge as connection type and enter desired URL (see above).

Press "Open" button.

Telemetry window will appear.

![](https://habrastorage.org/webt/xv/si/vn/xvsivnaqlszqc722mjfhhao9t9u.png)

Select "Tracks calibration" tab. 

The window contains two plots: Current estimated speeds of left and right tracks in upper part of the window and current remote control input in lower part. Make sure that data for current speed updates continiously. Remote control data will appear as you start remote control.

## Step 2: Initial calibration.

>If you already have some calibration values that you want to tune just a bit, you can skip this part and proceed to step #3.
>
>Perform this step if some hydraulic components were replaced during maintenance.

### 2.1 Reset parameters

Using configuration tool set `deadband_value_pos` to 1, `deadband_value_neg` to -1
`max_value` to 1000 and `min_value` to -1000 for both _left_ and _right_ tracks.

Press **Export** button to apply changes.

### 2.2 Switch to Remote Driving Control
- Switch RMO to remote driving control, give move permission;
- During control, it could be convenient to switch the Nonlinear mode on (for low speeds) and off (for high speeds) for joysticks in RMO touch window;


### 2.3: Find Dead-bands

What we need to do next is to find dead-bands: minimum controls, when drill just start moving.

> Note:
> Minimum cat speed is usually less 0.04 (4 cm per second). <br> 
> Try to move at a speed no faster than this.

#### 2.3.1 Find Forward Dead-band Value for Left (Cabin-side) Cat

- Move the _left_ cat joystick slowly _forward_ until drill just starts moving; Check current track speed in telemetry window.
- Write down the control of the _left_ joystick (get it from lower plot of telemetry window, 3 digits after `0.`) that starts the drill moving.
- You could repeat to be sure, that control value you found is minimum.

#### 2.3.2 Find Forward Dead-band Value for Right (Non-cabin-side) Cat

- Move the _right_ cat joystick slowly _forward_ until drill just starts moving; Check current track speed in telemetry window.
- Write down the control of the _right_ joystick (get it from lower plot of telemetry window, 3 digits after `0.`) that starts the drill moving.
- You could repeat to be sure, that control value you found is minimum.

#### 2.3.3 Find Backward Dead-band Value for Left (Cabin-side) Cat

- Move the _left_ cat joystick slowly _backward_ until drill just starts moving; Check current track speed in telemetry window.
- Write down the control of the _left_ joystick (get it from lower plot of telemetry window, 3 digits after `0.`) that starts the drill moving.
- You could repeat to be sure, that control value you found is minimum.

#### 2.3.4 Find Backward Dead-band Value for Right (Non-cabin-side) Cat

- Move the _right_ cat joystick slowly _backward_ until drill just starts moving; Check current track speed in telemetry window.
- Write down the control of the _right_ joystick (get it from lower plot of telemetry window, 3 digits after `0.`) that starts the drill moving.
- You could repeat to be sure, that control value you found is minimum.

### Step 2.4: Find Maximum Control Range

> Note:
> Maximum cat speed is usually about 0.4 — 0.5 (meters per second). <br>

Next part is about maximum ranges of control.
We need to find such a control value, that increasing it has no effect.
Be careful, drill should move fast.

#### 2.4.1 Find Maximum Forward Control for Left (Cabin-side) Cat

- Move _left_ cat joystick slowly forward until you feel control increasing has no effect. 
- Write down the control of the _left_ joystick (get it from lower plot of telemetry window, 3 digits after `0.`), when increasing has no effect.

> You should also control the _right_ cat forward for straight motion, but at this step, you should find the _left_ control maximum.

> **Attention!** If one track has maximum velocity bigger then another, move with the lowest maximum velocity to avoid turning. 
> `max_value` should be set taking into account this condition (it could be less than actual maximum for one track to keep it balanced). 

#### 2.4.2 Find Maximum Forward Control for Right (Non-cabin-side) Cat

- Move _right_ cat joystick slowly forward until you feel control increasing has no effect. 
- Write down the control of the _right_ joystick (get it from lower plot of telemetry window 3 digits after `0.`), when increasing has no effect.

> You should also control the _left_ cat forward for straight motion, but at this step, you should find the _right_ control maximum.

> **Attention!** If one track has maximum velocity bigger then another, move with the lowest maximum velocity to avoid turning. 
> `max_value` should be set taking into account this condition (it could be less than actual maximum for one track to keep it balanced). 

#### 2.4.3 Find Maximum Backward Control for Left (Cabin-side) Cat

- Move _left_ cat joystick slowly backward until you feel control increasing has no effect. 
- Write down the control of the _left_ joystick (get it from lower plot of telemetry window, 3 digits after `0.`), when increasing has no effect.

> You should also control the _right_ cat backward for straight motion, but at this step, you should find the _left_ control maximum.

> **Attention!** If one track has maximum velocity bigger then another, move with the lowest maximum velocity to avoid turning. 
> `min_value` should be set taking into account this condition (it could be less than actual maximum for one track to keep it balanced). 

#### 2.4.4 Find Maximum Backward Control for Right (Non-cabin-side) Cat

- Move _right_ cat joystick slowly backward until you feel control increasing has no effect. 
- Write down the control of the _right_ joystick (get it from lower plot of telemetry window, 3 digits after `0.`), when increasing has no effect.

> You should also control the _left_ cat backward for straight motion, but at this step, you should find the _right_ control maximum.

> **Attention!** If one track has maximum velocity bigger then another, move with the lowest maximum velocity to avoid turning. 
> `min_value` should be set taking into account this condition (it could be less than actual maximum for one track to keep it balanced). 

### 2.5 Apply new parameters.

> **Attention!** The control range is `[-1000, 1000]`, so multiply the controls viewed in telemetry window by 1000 and take integer part.
> 
> Usual values for min/max params are in the range ±600-900, dead-band values ±250-550.

Using configuration tool set for both left and right tracks your values, found on previous steps:<br>
`deadband_value_pos` - minimum forward control<br>
`deadband_value_neg` - minimum backward control<br>
`max_value` - maximum forward control<br>
`min_value` - maximum backward control

Press **Export** button in configuration window.

## 3 Test and fine tune your calibration

### 3.1 Switch to Remote Driving Control
Switch to Remote Driving Control
- Switch RMO to remote driving control, give move permission;
- During control, it could be convenient to switch the Nonlinear mode on (for low speeds) and off (for high speeds) for joysticks in RMO touch window;

### 3.2 Test and tune deadbands

Use left and right joysticks to move straight at lowest possible speed in both directions: forward and backward. Check current speed and control in telemetry window. <br>
>**You must be able to move at 0.04 (m/s) or slower.**

Use configuration tool to adjust calibration parameters:
- if any track (left, right or both) moves too fast - reduce its deadband, press **Export** and try again
- if you need to move joystick for more than 1% before machine starts moving - increase deadband for the corresponding track, press **Export** and try again

Note, that the drill should go straight when you move both joystick to the same position. If you observe any turn, reduce deadband of the track moving too fast.

Repeat this procedure for both directions: use `deadband_value_pos` - for forward motion and `deadband_value_neg` - for backward.

### 3.3 Test and tune maximum controls.
Use left and right joysticks to move straight at highest possible speed in both directions: forward and backward. Check current speed and control in telemetry window. <br>
>**Make sure the drill is moving straight, and you don't reach maximum speed while joysticks are not at 100%**

Use configuration tool to adjust calibration parameters:
- if any track (left, right or both) reach its maximum speed at control less than 100% - reduce its min/max value, press **Export** and try again
- if any track (left or right) moves faster, then maximum speed of another one - reduce its min/max value, press **Export** and try again
- if any track (left, right or both) moves too slow at 100% control - increase its min/max value, press **Export** and try again

>**Important!** `min_value` and `max_value` **must not** be smaller than corresponding deadbands.

Repeat this procedure for both directions: use `max_value` - for forward motion and `min_value` - for backward.

## Step 4: Permanently Save Parameters

Take a screenshot or write down your final values to be sure you won't lose them.

In configuration tool press **Dump** button.

## Step 5: Test

Perform soft drill restart.

In configuration tool press **Import** button, make sure your parameters did not change and was saved correctly.
Try remote control, make sure both tracks works as expected.
