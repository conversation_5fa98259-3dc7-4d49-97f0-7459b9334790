# Drill head calibration

> Please be patient and careful!

## Goal

![](https://habrastorage.org/webt/ea/ap/6z/eaap6zoqte8rels6r3q08jh8mfu.png)

The goal of this procedure is to find
1) The upper deadband value and the maximum possible control value before the saturation for CW rotation of the drill head;
2) The lower deadband value for CCW rotation of the drill head .

> :warning: **Finding the maximum CCW rotation speed is unsafe and can cause the drill rod or drill bit to unscrew, 
> resulting in damage to the machine and to personnel.**

## Pre-requirements 

Expected conditions to perform calibration:

- The vehicle is on a flat surface of stable terrain with no known voids or cracks;
- The vehicle is levelled with jacks;
- The software is run as usual;
- you and drill are free from any other tasks: no demonstrations, checks and so on.


## Step 1: Prepare tools

### 1.1 Open configuration tool and connect to drill 

Open a new terminal.

Run the configuration tool with the following commands:
```commandline
cd configuration-tool
python3 main.py
```
Select the drill you wish to calibrate from the drop-down menu in the bottom left-hand corner.
Make sure it is online (marked with a green circle)

![](https://habrastorage.org/webt/pg/jw/p4/pgjwp4vaktaqzarqhjhxka1vgvs.png)

Press the **Import** button.

The list of available parameters will appear. Use the `Drill Calibration` filter on the left to display only the relevant parameters.
![](https://habrastorage.org/webt/7g/zt/zx/7gztzxz8qro1anlmqjnsv4w1t2e.png)



### 1.2 Open Foxglove to view drill telemetry

Run Foxglove Studio. You will see the following window:

![](https://habrastorage.org/webt/lb/mv/sn/lbmvsnhu54febr9ujk5ia6qxn80.png)

Select `ws://<drill_IP>:9090` in "Recent data sources" list. (`ws://*********:9090` for Hudbay Perf 02) 

If you don't have one, create it by pressing `Open connection` option.

![](https://habrastorage.org/webt/20/eb/is/20ebiskddjfrk_ouwnfhsh0fabg.png)

Select `Rosbridge` as the connection type and enter the desired URL (see above).

Press the `Open` button.

The telemetry window will appear.

![](https://habrastorage.org/webt/nz/je/nf/nzjenfekebdwut_mjdsrqw7kor0.png)

Select the `Drill Calibration` tab. 
The window contains 3 pairs of graphs (current measured or estimated values in the upper part of the window and corresponding remote controls in the lower part): 
- The current measured RPM and the remote rotation speed control input;
- The current estimated feed speed and the remote feed speed control input;
- The current measured pulldown pressure and the remote pulldown pressure control input.



## 2 Reset calibration data

Using the configuration tool set 
- `/CanNode/IOs/rot/deadband_value_pos` to 1, 
- `/CanNode/IOs/rot/deadband_value_neg` to -1,
- `/CanNode/IOs/rot/max_value` to 1000 
- and `/CanNode/IOs/rot/min_value` to -1000.

Press **Export** button to apply changes.


## 3 Calibrating
### 3.1 CW calibrating in air

#### 3.1.1 Find CW deadband

- In RMO Switch to remote control. 
- Start very slowly increasing rotation until the drilling bit starts very slow rotation (check it by camera). 
- Write down the **minimal CW** `Remote rotation control` value (get it from plot of telemetry window, 3 digits after `0.`) that starts the drill bit rotation.
- Repeat the experiment 3-5 times to be sure you found the smallest value.

### 3.2 CW calibrating in terrain

- Start drilling the hole manually or in autonomous mode after the first pull-up.
- Set the pull down pressure to the normal drilling value for this type of terrain.
- Repeat the process described in the previous paragraph [CW calibrating in air](#31-cw-calibrating-in-air).

### 3.3 CCW calibration in air

#### 3.3.1 Find CCW deadband

- Start very slowly increasing rotation in CCW direction until the drilling bit starts very slow rotation (check it by camera). 
- Write down the **minimal CCW** `Remote rotation control` value (get it from plot of telemetry window, 3 digits after `0.`) that starts the drill bit rotation in CCW direction.
- Repeat the experiment 3-5 times to be sure you found the smallest value.

#### 3.3.2 Find maximum CCW rotation control

- Start increasing the rotation speed until reaching the maximum permitted value (120..140 RPM), 
- Write down the **maximum CCW in air** `Remote rotation control` value (get it from plot of telemetry window, 3 digits after `0.`)


> :warning: **CCW rotation is unsafe and can cause the drill rod or drill bit to unscrew, 
> resulting in damage to the machine and to personnel.**

## 4 Apply new parameters

> **Attention!** The control range is `[-1000, 1000]`, so multiply the controls viewed in telemetry window by 1000 and take integer part.
> 
> Usual values for min/max params are in the range ±600-900, dead-band values ±250-550.

Using configuration tool set for `CanNode/IOs/rot` your values, found on previous steps:

- `min_value`: CСW_in_air_max_value 
- `max_value`: CW_in_terrain_max_value
- `deadband_value_pos`: CW_in_air_min_value
- `deadband_value_neg`: CCW_in_air_min_value

Press **Export** button in configuration window.


## 5 Test and fine tune your calibration

### 5.1 Switch to Remote Drilling Control
Switch to Remote Drilling Control
- Switch RMO to remote drilling control, give move permission;
- During control, it could be convenient to switch the Nonlinear mode on (for low speeds) and off (for high speeds) for joysticks in RMO touch window;

### 5.2 Test and tune deadbands

Use joysticks to rotate bit at lowest possible speed in both directions: CW and CCW (be careful with CCW!). Check current rpm and control in telemetry window.

>**You should be able to detect rod slow rotation in camera.**

Use configuration tool to adjust calibration parameters:

- if drill bit rotates too fast, reduce its deadband, press **Export** and try again;
- if you need to move joystick for more than 1% before drill bit starts rotation - increase deadband, press **Export** and try again.

Repeat this procedure for both directions: use `deadband_value_pos` - for CW rotation and `deadband_value_neg` - for CCW.

### 5.3 Test and tune maximum controls

Use joysticks to rotate CW at maximum rotation speed (CW only!). Check current rotation speed and control in telemetry window.

Use configuration tool to adjust calibration parameters:
- if drill bit reaches its maximum rotation speed at control less than 100% - reduce min/max values, press **Export** and try again
- if drill bit rotates faster, then maximum speed - reduce min/max values, press **Export** and try again
- if drill bit rotates too slow at 100% control - increase its min/max value, press **Export** and try again

>**Important!** `min_value` and `max_value` **must not** be smaller than corresponding deadbands.


## 6 Permanently Save Parameters

Take a screenshot or write down your final values to be sure you won't lose them.

In configuration tool press **Dump** button.

## 7 Test

Perform soft drill restart.

In configuration tool press **Import** button, make sure your parameters was saved correctly.
Try remote control, make sure drilling works as expected.