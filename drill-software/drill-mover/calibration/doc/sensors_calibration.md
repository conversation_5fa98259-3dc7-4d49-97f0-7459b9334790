# Calibration of `LinearPredictor`-Based Sensor Processors

![LinearPredictor](https://habrastorage.org/webt/l3/s6/eo/l3s6eocr3f8xk-4jsyc8glfmi5w.png)<br>
Picture 1: `LinearPredictor` Sensor Processor

## Applicability

This technique applies to the following raw sensors utilizing a `LinearPredictor` Processor:
- Head rangefinder;
- Rotation speed sensor;
- Feed pressure sensor;
- Rotation pressure sensor;
- Air pressure sensor;
- Water level sensor;
- Fuel level sensor.

This list may be extended in the future as more sensors are deemed compatible with this calibration process.
The key is to understand the principle and apply it appropriately to any compatible sensor.

## The Goal and General Concept of Sensor Calibration

The purpose of sensor calibration is to align raw sensor readings with actual real-world values. 

The primary purpose of this calibration process is to provide the `LinearPredictor` with these values:

- `(measure_1, result_1)` and 
- `(measure_2, result_2)`.

With this information, the software can effectively transform the raw sensor measurements into calibrated values, 
leading to accurate representation of the real-world environment.

<details>
  <summary>Technical Background</summary>

### Technical Background

Zyfra's robot drill on-board software includes a component known as the `LinearPredictor` Processor, 
which is housed within the `State Tracker` package.

The primary role of the `State Tracker` is to manage sensor data. 
Its structure is based on a computational graph that is configured via a specific file. 
The purpose of this graph is to transform units of measurement, to filter data, 
to calculate values that cannot be measured directly, and to categorise data into types within ROS messages.

The `LinearPredictor` processor, an element of the State Tracker, converts raw sensor readings into calibrated data 
that is more suitable for practical use. 
This conversion process applies a linear transformation using two known pairs of raw and desired values: 
`(measure_1, result_1)` and `(measure_2, result_2)`.


During its initialization, the LinearPredictor calculates the transformation coefficients from the input-output pairs.

Using this computed transformation coefficients, the LinearPredictor applies a linear transformation 
(a combination of scaling and shifting) to the raw data, aligning it with desired, real-world measurements.

On initialisation, the `LinearPredictor` uses these input/output pairs to calculate the coefficients required for the transformation.

During processing, the `LinearPredictor` performs a linear transformation on the raw data using these coefficients.
This transformation, which includes scaling and shifting, ensures that the data matches the desired real world values.


`LinearPredictor` usage example: State tracker graph branch for drilling sensors processing:

![](https://habrastorage.org/webt/nx/ek/9w/nxek9wkiylxgpfhcigkfakfqc58.png)<br>
Picture 2: `LinearPredictor` usage example: State tracker graph branch for drilling sensors processing

</details>


## Calibration Procedure

Calibrating your sensors involves obtaining raw sensor data and the corresponding desired values (in the appropriate units)
at two different points. 
The first point should be **close to zero (minimum) but not zero (minimum)**. 

> :warning: **For many sensors zero would not work properly!** 

The second point should be close to maximum.

For example, for the fuel sensor: the first point could be at 20% fuel level and the second at 90% full fuel level, 
or for the RPM sensor: at some low RPM (but > 0) and high RPM (not necessarily maximum), etc. 

Table 1, which contains helpful calibration information, can be referred to during the process.

Table 1: General Information on Calibration of `LinearPredictor`-Based Sensor Processors

| # | Sensor                   | StateTracker2Node field to set (in Configuration tool) during calibration | Raw data topic          | Calibrated data topic and value                     | In Foxglove Studio (`drill_user` configuration)                                                                  |
|---|--------------------------|---------------------------------------------------------------------------|-------------------------|-----------------------------------------------------|------------------------------------------------------------------------------------------------------------------|
| 1 | Head rangefinder         | `head_rangefinder_pred`                                                   | `/head_rangefinder`     | `spindle_depth` and `feed_speed` in `/drill_state`  | In `Drill sensor calibration` tab:<br/>- Raw: `Raw laser data` plot <br/>- Calibrated: `Head position` plot      |
| 2 | Rotation speed sensor    | `rot_speed_pred`                                                          | `/rotation_speed`,      | `rotation_speed` in `/drill_state`                  | In `Drill sensor calibration` tab:<br/>- Raw: `Raw rotation speed` plot <br/>- Calibrated: `Rotation speed` plot |
| 3 | Feed pressure sensor     | `feed_press_pred`                                                         | `/pressure_state_raw`   | `feed_pressure` in `/pressure_state`                | In `Pressure sensor calibration` tab:<br/>- Raw: `Raw pulldown` plot <br/>- Calibrated: `Pulldown` plot          |
| 4 | Rotation pressure sensor | `rot_press_pred`                                                          | `/pressure_state_raw`   | `rotation_pressure` in `/pressure_state`            | In `Pressure sensor calibration` tab:<br/>- Raw: `Raw torque` plot <br/>- Calibrated: `Torque` plot              |
| 5 | Air pressure sensor      | `air_press_pred`                                                          | `/pressure_state_raw`   | `air_pressure` in `/pressure_state`                 | In `Pressure sensor calibration` tab:<br/>- Raw: `Raw air pressure` plot <br/>- Calibrated: `Air pressure` plot  |
| 6 | Water level sensor       | `water_level_pred`                                                        | `/water_level_raw`      | `value` in `/water_level`                           | In `Drill sensor calibration` tab:<br/>- Raw: `Raw water level` plot <br/>- Calibrated: `Water level` plot       |
| 7 | Fuel level sensor        | `fuel_level_pred`                                                         | `/fuel_level_raw`       | `value` in `/fuel_level`                            | In `Engine` tab:<br/>- Raw: `Fuel Level Raw` plot <br/>- Calibrated: `Fuel Level Calibrated` plot                |

To determine the correct values your sensor should return, you can refer to the information provided by the drill's onboard driver.
Raw sensor readings can be sourced from the topic or from `Foxglove`, 
as detailed in "Table 1 General Information on Calibration of LinearPredictor-Based Sensor Processors".

Here's the step-by-step procedure:

1) Start with the sensor in its first state (close to zero, but not zero). 
2) For your sensor, find out how to obtain the raw sensor value according to Table 1.
3) At the same time:
   - Determine and record the raw sensor value (`measure_1`).
   - Determine and record the corresponding desired value (`result_1`) that your sensor should return from the information provided by the onboard driver of the drill.
4) Change the state to near maximum by remote control (as with RPM or pulldown, for example), or wait for it to change naturally (as with fuel level).
5) Once the state has changed, record the new raw sensor value (`measure_2`) and the corresponding desired value (`result_2`).
6) Enter these values into the 'Configuration Tool' and set them for the specific sensor processor. Refer to Table 1 for the exact field names.
7) Apply the values using the 'Configuration Tool' and perform a verification check to ensure the calibration is accurate.
8) Save the values permanently using the 'Configuration Tool' after successful verification or repeat the procedure in any other case. 

> :note: In this process, `measure_1` and `measure_2` represent the raw sensor readings, 
> while `result_1` and `result_2` denote the corresponding desired real-world values.

<br><hr>

# Calibration of `LengthChecker`-Based Sensor Processors 

![LengthChecker](https://habrastorage.org/webt/uu/-j/zh/uu-jzhe2jyeq4rhm9ndnl7wvdjq.png)<br>
Picture 3: `LengthChecker` Sensor Processor 

## Applicability

The following calibration technique applies to these raw sensors that utilize the `LengthChecker` Processor:

- Three wrench retract and extended length sensors
- Fork front and rear length sensor

This list may be extended in the future as more sensors are deemed compatible with this calibration process. 
The key is to understand the principle and apply it appropriately to any compatible sensor.


## The Goal and General Concept of Sensor Calibration 

The aim of sensor calibration in this context is to convert raw data into a Boolean state (either true or false) based on predefined limits. 
For example, if the fork length falls within a specified range (between `min_val` and `max_val` for `fork_len_rear_check`),
we set a flag indicating the fork's rear position.


During calibration, our goal is to establish two key values:

- `min_val`, and
- `max_val`.

After setting these calibration data, the software will adjust the raw sensor readings and return 
the appropriate Boolean state or flag values.

<details>
  <summary>Technical Background</summary>

### Technical Background

Zyfra's robot drill on-board software includes a component known as the `LengthChecker` Processor, 
which is housed within the `State Tracker` package.

The primary role of the `State Tracker` is to manage sensor data. 
Its structure is based on a computational graph that is configured via a specific file. 
The purpose of this graph is to transform units of measurement, to filter data, 
to calculate values that cannot be measured directly, and to categorise data into types within ROS messages.

The `LengthChecker` Processor specifically takes in a floating-point number (representing some measurement like length) 
as input and outputs a Boolean value. 
It checks if the input lies within a defined range (`min_val` to `max_val`). 
If the input is within this range, it returns `True`; otherwise, it returns `False`.

![](https://habrastorage.org/webt/_k/pa/bv/_kpabviao6clbhme6sadkzhu3ko.png)
Picture 4: `LengthChecker` usage example: State tracker graph branch for fork length processing

</details>


## Calibration Procedure

Table 2 provides details that are useful during the calibration process.

Table 2: General Information on Calibration of `LengthChecker`-Based Sensor Processors

| # | Sensor                       | StateTracker2Node field to set (in Configuration tool) during calibration | Raw data topic and value                      | Calibrated data topic and value                                                                                                             | In Foxglove Studio (`drill_user` configuration) |
|---|------------------------------|---------------------------------------------------------------------------|-----------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------|
| 1 | Wrench stage 1 linear sensor | `wrench_st1_retract_len_check`                                            | `stage_len_1` in `/wrench_switch_state_raw`   | `stage_1_open` in `/wrench_switch_state`                                                                                                    | `Fork & Wrench` tab `Wrench stage 1` plot       |
| 2 | Wrench stage 1 linear sensor | `wrench_st1_extend_len_check`                                             | `stage_len_1` in `/wrench_switch_state_raw`   | `stage_1_closed` in `/wrench_switch_state`                                                                                                  | `Fork & Wrench` tab `Wrench stage 1` plot       |
| 3 | Wrench stage 2 linear sensor | `wrench_st2_retract_len_check`                                            | `stage_len_2` in `/wrench_switch_state_raw`   | `stage_2_closed` in `/wrench_switch_state` is LogicalConjunction of `wrench_st2_extend_len_check` and `wrench_st3_extend_len_check` results | `Fork & Wrench` tab `Wrench stage 2` plot       |
| 4 | Wrench stage 2 linear sensor | `wrench_st2_extend_len_check`                                             | `stage_len_2` in `/wrench_switch_state_raw`   | `stage_2_open` in `/wrench_switch_state` is LogicalConjunction of `wrench_st2_retract_len_check` and `wrench_st3_retract_len_check` results | `Fork & Wrench` tab `Wrench stage 2` plot       |
| 5 | Wrench stage 3 linear sensor | `wrench_st3_retract_len_check`                                            | `stage_len_3` in `/wrench_switch_state_raw`   | See `stage_2_closed` upper                                                                                                                  | `Fork & Wrench` tab `Wrench stage 3` plot       |
| 6 | Wrench stage 3 linear sensor | `wrench_st3_extend_len_check`                                             | `stage_len_3` in `/wrench_switch_state_raw`   | See `stage_2_open` upper                                                                                                                    | `Fork & Wrench` tab `Wrench stage 3` plot       |
| 7 | Fork linear sensor           | `fork_len_front_check`                                                    | `length` in `/fork_switch_state_raw`          | `front_switch_on` in `/fork_switch_state`                                                                                                   | `Fork & Wrench` tab `Fork length raw` plot      |
| 8 | Fork linear sensor           | `fork_len_rear_check`                                                     | `length` in `/fork_switch_state_raw`          | `rear_switch_on` in `/fork_switch_state`                                                                                                    | `Fork & Wrench` tab `Fork length raw` plot      |


Here's the step-by-step procedure:

1) Move the actuator from the lower to the upper position. While the actuator is moving (possibly using a remote control), 
observe the raw sensor readings. 

   These measurements can be obtained from the ROS topic or from Foxglove as described in 
   "Table 2: General information on calibrating LengthChecker based sensor processors. 

2) Record the values at the lower and upper limits of the range for each desired state.

    For example, consider a fork with two states: rear and front:
   - find length values `min_val` and `max_val` for `fork_len_rear_check`, defining a range where the `rear_switch_on` flag should be set to `true`, indicating that the fork has been retracted.
   - Find the length values `min_val` and `max_val` for `fork_len_front_check`, defining a range where the `front_switch_on` flag should be set to `True`, indicating that the fork is being extended.

3) Set these values in the `Configuration Tool` for the processor of your sensor. 

   Refer to "Table 2: General information for calibrating `LengthChecker`-based sensor processors" for the appropriate fields.

4) Apply the values using the 'Configuration Tool' and verify that the calibration is accurate.
5) Save the values permanently using the 'Configuration Tool' after successful verification or repeat the procedure in any other case.

<br><hr>
<br>
<br>
