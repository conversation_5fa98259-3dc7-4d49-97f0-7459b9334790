# can_node
`can_node` — нода, предназначенная для взаимодействия с внешними устройствами. 
Публикует сообщения для дальнейшей ретрансляции в шину, читает топик с данными из неё.  
Также читает и публикует ROS топики, описанные в конфигурационном файле для взаимодействия 
с остальными нодами. 

Конфигурационный файл содержит информацию о CAN протоколе для каждого канала ввода-вывода,
описание полей, их соответствие топику ROS, модификаторы, такие как линейное преобразование 
(диапазоны входа и выхода), и коэффициенты полинома нелинейного преобразования для выходов.

Является основным узлом для взаимодействия бортового ПО с внешним миром (через контроллер B&R X90),
читает данные инклинометров, инерциальных датчиков, телеметрию двигателя.

Имеет встроенный мультиплексор, переключаемый по топику main_mode – управляет выбором топиков с управлением 
(ДУ от remote_connector или управление автономным движением от регуляторов хода, бурения, домкратов и пр.).

Безопасность: если не получает сообщения с управлением дольше установленного времени — шлет на выход (в кан) нулевые значения. 
Модуль имеет максимальные требования к стабильности: никогда, ни при каких обстоятельствах недопустимо зависание публикуемых значений.
