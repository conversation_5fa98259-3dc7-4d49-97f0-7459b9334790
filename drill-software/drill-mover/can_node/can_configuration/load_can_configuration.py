#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import yaml
import rospy

import common.can_base as can_base
import can_msgs.msg as can_msgs

can1_config_publisher = rospy.Publisher('/can_1/rx', can_msgs.Frame, queue_size=10)
can2_config_publisher = rospy.Publisher('/can_2/rx', can_msgs.Frame, queue_size=10)
can3_config_publisher = rospy.Publisher('/can_3/rx', can_msgs.Frame, queue_size=10)

if __name__ == "__main__":
    rospy.init_node('load_config_node', anonymous=True)
    if len(sys.argv) > 1:
        try:
            with open(sys.argv[1], mode='r') as a:
                my_configuration = yaml.load(a.read())
                can1_exclude_messages_id = my_configuration['can1_exclude_messages_id']
                can3_exclude_messages_id = my_configuration['can3_exclude_messages_id']

                try:
                    with open('GeneralSetup.yaml', 'r') as protocol:
                        yaml_params = yaml.safe_load(protocol.read())
                        all_to_can2_message_class = can_base.get_message_class(yaml_params)

                        new_msg = all_to_can2_message_class()
                        setattr(new_msg, 'device_id', my_configuration['my_device_id'])
                        setattr(new_msg, 'can1_speed', my_configuration['can1_speed'])
                        setattr(new_msg, 'can2_speed', my_configuration['can2_speed'])
                        setattr(new_msg, 'can3_speed', my_configuration['can3_speed'])
                        setattr(new_msg, 'flag_for_can1', my_configuration['all_can1_to_can2'])
                        setattr(new_msg, 'flag_for_can3', my_configuration['all_can3_to_can2'])
                        can2_config_publisher.publish(new_msg.serialize())
                except AttributeError as e:
                    rospy.logerr("Something went wrong while reading GeneralSetup.yaml")
                    raise

                try:
                    with open('FilterEdit.yaml', 'r') as protocol:
                        yaml_params = yaml.safe_load(protocol.read())
                        ret_from_can_message_class = \
                        can_base.get_message_class(yaml_params)

                        for single_msg_id in can1_exclude_messages_id:
                            new_msg = ret_from_can_message_class()
                            setattr(new_msg, 'device_id', my_configuration['my_device_id'])
                            setattr(new_msg, 'message_id', single_msg_id)
                            setattr(new_msg, 'source_can', 0x01)
                            can1_config_publisher.publish(new_msg.serialize())
                        for single_msg_id in can3_exclude_messages_id:
                            new_msg = ret_from_can_message_class()
                            setattr(new_msg, 'device_id', my_configuration['my_device_id'])
                            setattr(new_msg, 'message_id', single_msg_id)
                            setattr(new_msg, 'source_can', 0x03)
                            can3_config_publisher.publish(new_msg.serialize())
                except AttributeError as e:
                    rospy.logerr("Something went wrong while reading FilterEdit.yaml")
                    raise

                print("Can configuration applied successfully!")

        except EnvironmentError:
            print('Something went wrong while reading %s file' % sys.argv[1])
            raise

    else:
        print("Usage: python load_can_configuration.py *name_of_a_configuration_file.json*")
        sys.exit()