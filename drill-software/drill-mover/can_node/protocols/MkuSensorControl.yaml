# Протокол управления МКУ от компании Сенсор-плюс
# 08.2018

parser_version: 2

name: MKU Sensor Control
class_name: MkuSensorControl

rate: 5
is_extended: True
source_id: 0xFD
pgn: 0xFF5a
dlc: 8
message_type: J1939_general

data:
  # ID сообщения - равен 0
  id:
    type: B
    byte: 1
    default: 0

  # Роборежим
  # format: 0 - disabled, 1 - enabled
  robo_mode:
    type: u2
    byte: 2
    bit: 1
  # Стояночный тормоз (индикация)
  # format: 0 - disabled, 1 - enabled
  parking:
    type: u2
    byte: 2
    bit: 3

  # Включение аварийной сигнализации
  # format: 0 - disabled, 1 - enabled
  alarm:
    type: u2
    byte: 5
    bit: 1
  # Включение габаритов
  # format: 0 - disabled, 1 - enabled
  profile_light:
    type: u2
    byte: 5
    bit: 3
  # Включение ближнего света
  # format: 0 - disabled, 1 - enabled
  daytime_light:
    type: u2
    byte: 5
    bit: 5
    default: 0
  # Включение дальнего света
  # format: 0 - disabled, 1 - enabled
  distant_light:
    type: u2
    byte: 5
    bit: 7

  # Включение противотуманных фар
  # format: 0 - disabled, 1 - enabled
  front_fog_light:
    type: u2
    byte: 6
    bit: 1
  # Включение заднего противотуманного фонаря
  # format: 0 - disabled, 1 - enabled
  rear_fog_light:
    type: u2
    byte: 6
    bit: 3
  # Открытие жалюзи ДВС
  # format: 0 - disabled, 1 - enabled
  engine_door:
    type: u2
    byte: 6
    bit: 5
    default: 1
  # Включение правого поворота
  # format: 0 - disabled, 1 - enabled
  right_turn_signal:
    type: u2
    byte: 6
    bit: 7

  # Включение левого поворота
  # format: 0 - disabled, 1 - enabled
  left_turn_signal:
    type: u2
    byte: 7
    bit: 1
  # Включение фары боковой левой
  # format: 0 - disabled, 1 - enabled
  left_lateral_light:
    type: u2
    byte: 7
    bit: 3
  # Включение фар подсветки шасси и бокового пространства
  # format: 0 - disabled, 1 - enabled
  chassis_light:
    type: u2
    byte: 7
    bit: 5
  # Включение звукового сигнала
  # format: 0 - disabled, 1 - enabled
  sound_signal:
    type: u2
    byte: 7
    bit: 7

  # Включение фары боковой правой
  # format: 0 - disabled, 1 - enabled
  right_lateral_light:
    type: u2
    byte: 8
    bit: 1

  # Протокол предусматривает 3 взаимоисключающих поля,
  # поэтому мы заменим их одним.

  # # Подъём кузова
  # # format: 0 - disabled, 1 - enabled
  # platform_up:
  #   type: u2
  #   byte: 7
  #   bit: 2
  # # Опускание кузова
  # # format: 0 - disabled, 1 - enabled
  # platform_down:
  #   type: u2
  #   byte: 7
  #   bit: 4
  # # Блокировка кузова
  # # format: 0 - disabled, 1 - enabled
  # platform_hold:
  #   type: u2
  #   byte: 7
  #   bit: 6

  # Управление кузовом
  platform_control:
    type: u6
    byte: 8
    bit: 3
    defines:
      PLATFORM_UP: 0b000001
      PLATFORM_DOWN: 0b000100
      PLATFORM_HOLD: 0b010000
