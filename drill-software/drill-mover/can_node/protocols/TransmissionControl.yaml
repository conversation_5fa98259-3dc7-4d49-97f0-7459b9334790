# Протокол управления трансмиссией от компании Стрим
# 07.2018

parser_version: 2

name: Stream Transmission control
class_name: TransmissionControl

rate: 100
is_extended: true
dlc: 5

id: 0x300

data:
  # Роборежим
  # format: 0 - disabled, 1 - enabled
  robo_mode:
    type: t
    byte: 1
    bit: 1
  # Режим стабилизации скорости
  # format: 0 - disabled, 1 - enabled
  stabilize:
    type: t
    byte: 1
    bit: 2
  # Сброс аварии
  # format: 0 - disabled, 1 - enabled
  crash_reset:
    type: t
    byte: 1
    bit: 3

  # Направление движения
  # format: 0 - N, 1 - D, 2 - R, 3 - P
  gear:
    type: u2
    byte: 2
    bit: 1
    defines:
      NEUTRAL: 0
      FORWARD: 1
      BACKWARD: 2
      PARKING: 3

  # Нажатие педали акселератора
  # format: 0.4% / bit
  # range: 0 ... 100
  accel:
    type: B
    byte: 3

  # Нажатие педали электродинамического торможения
  # format: 0.4% / bit
  # range: 0 ... 100
  dyn_brakes:
    type: B
    byte: 4

  # Скорость (максимальная/стабилизации)
  # format: 0.5km/h / bit
  # range: 0 ... 65
  speed_limit:
    type: B
    byte: 5
    default: 76
