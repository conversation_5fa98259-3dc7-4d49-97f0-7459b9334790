# J1939 ARDP
# Angular Rate Data Packet

parser_version: 2

name: Acceleration Data Packet
class_name: ADP

rate: 10
is_extended: true
dlc: 8

message_type: J1939_general
pgn: 0xf02d  # 61485
priority: 0x06

data:
  # Range: 0 to 640/55 m/s**2
  acceleration_x: {
    type: H, byte: 1, resolution: 0.01, offset: -320
  }

  # Range: 0 to 640/55 m/s**2
  acceleration_y: {
    type: H, byte: 3, resolution: 0.01, offset: -320
  }

  # Range: 0 to 640/55 m/s**2
  acceleration_z: {
    type: H, byte: 5, resolution: 0.01, offset: -320
  }
