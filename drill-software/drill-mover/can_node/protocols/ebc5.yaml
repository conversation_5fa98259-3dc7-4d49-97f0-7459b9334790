# J1939 EBC5
# Information on brake control.

parser_version: 2

name: Electronic Brake Controller 5 info
class_name: EBC5

rate: 10
is_extended: true
dlc: 8

message_type: J1939_general
pgn: 0xfdc4  # 64964
priority: 0x06

data:
  # This parameter indicates if the brake system presently uses the foundation brakes.
  # 00 - Foundation brakes not in use
  # 01 - Foundation brakes in use
  foundation_brake_use: {
    type: u2, byte: 2, bit: 1,
    defines: { BRAKE_IN_ISE: 0b00, BRAKE_NOT_IN_USE: 0b01 }
  }
  # This parameter indicates which external brake control is allowed.
  # 00 - Any external brake demand will be accepted (brake system fully operational)
  # 01 - Only external brake demand of highest XBR Priority (00) will be accepted  (e.g. because the temperature limit of the brake system is exceeded)
  # 10 - No external brake demand will be accepted (e.g. because of fault in brake system)
  xbr_system_state: {
    type: u2, byte: 2, bit: 3,
    defines: { STATE_NORMAL: 0b00, STATE_LIMITED: 0b01, STATE_FAILURE: 0b10 }
  }
  # This parameter indicates which XBR Control Mode is executed by the brake system.
  # 0000 - No brake demand being executed (default mode)
  # 0001 - Driver's brake demand being executed, no external brake demand
  # 0010 - Addition mode of XBR acceleration control being executed
  # 0011 - Maximum mode of XBR acceleration control being executed
  xbr_active_ctrl_mode: {
    type: u4, byte: 2, bit: 5
  }
  # Maximum brake performance available for external systems. The limit is only effective in the XBR Priorities 01 to 11.
  # It is specified as an absolute acceleration in reference to the road.
  # Units: m/s^2, range: -10.0 to +10.0
  xbr_acceleration_limit: {
    type: B, byte: 3, resolution: 0.1, offset: -12.5
  }
