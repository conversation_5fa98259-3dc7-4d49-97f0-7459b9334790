# J1939 EEC1
# Electronic Engine Controller 1.

parser_version: 2

name: Electronic Engine Controller 1
class_name: EEC1

rate: 10
is_extended: true
dlc: 8

message_type: J1939_general
pgn: 0xf004  # 61444
priority: 0x03

data:
  # Driver's demand engine - percent torque.
  # Range: -125 to 125%
  demand_torque: {
    type: B, byte: 2, resolution: 1, offset: -125
  }

  # Actual engine - percent torque.
  # Range: -125 to 125%
  actual_torque: {
    type: B, byte: 3, resolution: 1, offset: -125
  }

  # Engine speed.
  # Range: 0 to 8031.875 rpm
  engine_speed: {
    type: H, byte: 4, resolution: 1/8, offset: 0
  }
