# J1939 ETC2
# Electronic Transmission Controller 2.

parser_version: 2

name: Electronic Transmission Controller 2 info
class_name: ETC2

rate: 10
is_extended: true
dlc: 8

message_type: J1939_general
pgn: 0xf005  # 61445
priority: 0x06

data:
  # The gear that the transmission will attempt to achieve during the current shift if a shift is in progress,
  # or the next shift if one is pending (i.e., waiting for torque reduction to initiate the shift).
  # Units: gear value, range: -125 to +125 (126 is park)
  selected_gear: {
    type: B, byte: 1, offset: -125
  }
  # Actual ratio of input shaft speed to output shaft speed.
  # Units: ratio, range: 0 to 64.255
  actual_gear_ratio: {
    type: H, byte: 2, resolution: 0.001, offset: 0
  } 
  # The gear currently engaged in the transmission or the last gear engaged while
  # the transmission is in the process of shifting to the new or selected gear.
  # Transitions toward a destination gear will not be indicated.
  # Units: gear value, range: -125 to +125 (126 is park)
  current_gear: {
    type: B, byte: 4, offset: -125
  }
  # Range selected by the operator.
  # Characters may include
  # P, Rx, Rx-1...R2, R1, R, Nx, Nx-1...N2, N1, N, D, D1, D2..., Dx, L, L1, L2..., Lx-1, 1, 2, 3,...
  # If only one displayed character is required, the second character shall be used and
  # the first character shall be a space (ASCII 32) or a control character (ASCII 0 to 31).
  # If the first character is a control character, refer to the manufacturer’s application document for definition.
  # Units: 2 byte ASCII string
  requested_range: {
    type: 2s, byte: 5
  }
  # Range currently being commanded by the transmission control system.
  # Characters may include
  # P, Rx, Rx-1...R2, R1, R, Nx, Nx-1...N2, N1, N, D, D1, D2..., Dx, L, L1, L2..., Lx-1, 1, 2, 3,...
  # If only one displayed character is required, the second character shall be used and
  # the first character shall be a space (ASCII 32) or a control character (ASCII 0 to 31).
  # If the first character is a control character, refer to the manufacturer’s application document for definition.
  # Units: 2 byte ASCII string
  current_range: {
    type: 2s, byte: 7
  }
