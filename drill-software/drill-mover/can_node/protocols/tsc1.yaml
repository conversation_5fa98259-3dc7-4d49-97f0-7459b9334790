# J1939 TSC1
# Torque Speed Controller 1.

parser_version: 2

name: Torque/Speed Control 1
class_name: TSC1

rate: 100
is_extended: true
dlc: 8

message_type: J1939_general
pgn: 0x0  # 0
priority: 0x03

data:
  mandatory_bits: {
    type: u2, byte: 1, bit: 7, default: 3
  }
  override_control_mode_priority: {
    type: u2, byte: 1, bit: 5, default: 2
  }
  requested_speed_conditions: {
    type: u2, byte: 1, bit: 3, default: 3
  }

  override_control_modes: {
    type: u2, byte: 1, bit: 1, default: 1
  }

  requested_speed: {
    type: H, byte: 2, default: 0, resolution: 0.125
  }

  requested_torque: {
    type: B, byte: 4, default: 0, offset: -125
  }

