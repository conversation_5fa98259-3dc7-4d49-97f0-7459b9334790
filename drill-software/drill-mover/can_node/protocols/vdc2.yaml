# J1939 VDC2
# Vehicle Dynamic Stability Control 2.
# Contains information which relates to the vehicle's movement.

parser_version: 2

name: Vehicle Dynamic Stability Control info
class_name: VDC2

rate: 100
is_extended: true
dlc: 8

message_type: J1939_general
pgn: 0xf009  # 61449
priority: 0x06

data:
  # The main operator`s steering wheel angle (on the steering column, not the actual wheel angle).
  # The vehicle being steered to the left (counterclockwise) results in a positive steering wheel angle.
  # This is the yaw angle of the steering wheel with the z-axis along the centerline of the steering column.
  # Units: rad, range: -31.374 to 31.374
  steering_wheel_angle: {
    type: H, byte: 1, resolution: 1/1024, offset: -31.374
  }
  # Indicates number of steering wheel turns, absolute position or relative position at ignition on.
  # Positive values indicate left turns.
  # Units: count, range: -32 to 29 (-10 to 10 operational)
  steering_wheel_turn_counter: {
    type: u6, byte: 3, bit: 1, resolution: 1, offset: -32
  }
  # Indicates whether the steering wheel angle sensor is capable of absolute measuring
  # of the number of steering wheel turns or not (relative measuring to position at ignition on).
  # 00 - Relative measuring principle
  # 01 - Absolute measuring principle
  steering_wheel_angle_sensor_type: {
    type: u2, byte: 3, bit: 7
  }
  # Indicates the rate of rotation about the vertical axis (i.e. the z-axis).
  # A positive yaw rate signal results when the vehicle turns counter-clockwise.
  # Units: rad/s, range: -3.92 to 3.92
  yaw_rate: {
    type: H, byte: 4, resolution: 1/8192, offset: -3.92
  }
  # Indicates a lateral acceleration of the vehicle (the component of vehicle acceleration vector along the Y-axis).
  # A positive lateral acceleration signal results when the vehicle is accelerated to the left.
  # Units: m/s^2, range: -15.687 to 15.687
  lateral_acceleration: {
    type: H, byte: 6, resolution: 1/2048, offset: -15.687
  }
  # Indicates the longitudinal acceleration of the vehicle.
  # A positive longitudinal acceleration signal results when the vehicle speed increases,
  # regardless of driving the vehicle forward or backward.
  # Units: m/s^2, range: -12.5 to 12.5
  longitudinal_acceleration: {
    type: B, byte: 8, resolution: 0.1, offset: -12.5
  }
