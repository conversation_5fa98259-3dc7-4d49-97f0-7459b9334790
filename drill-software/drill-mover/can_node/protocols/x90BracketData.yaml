#вилки

parser_version: 2

name: x90 Brackets Data
class_name: x90BracketData

rate: 10
is_extended: true
dlc: 5

id: 0x104

data:
  # вилка данные 1 - выход 6
  bracket_dt_1:
    type: t
    byte: 1
    bit: 1
  # вилка влево - (AO)
  bracket_left:
    type: t
    byte: 1
    bit: 2
  # вилка вправо - (AO)
  bracket_right:
    type: t
    byte: 1
    bit: 3
  # вилка данные - выход 16 : 0 - 10 V -> 0 - 32767
  bracket_dt_2:
    type: H
    byte: 2
    resolution: 32/32767
