#карусели

parser_version: 2

name: x90 Carousels' Data
class_name: x90CarouselData

rate: 10
is_extended: true
dlc: 5

id: 0x110

data:
  # карусель втянута DI
  carousel_o:
    type: t
    byte: 1
    bit: 1
  # карусель выдвинута DI
  carousel_c:
    type: t
    byte: 1
    bit: 2
  # карусель индекс 1 DI
  carousel_1:
    type: t
    byte: 1
    bit: 3
  # карусель индекс 2 DI
  carousel_2:
    type: t
    byte: 1
    bit: 4
  # длина актуатора выдвижения карусели (основная ось)
  carousel_main_axis_len:
    type: H
    byte: 2
    resolution: 32/32767

  # длина актуатора выбора стакана карусели (связанная ось)
  carousel_index_axis_len:
    type: H
    byte: 4
    resolution: 32/32767