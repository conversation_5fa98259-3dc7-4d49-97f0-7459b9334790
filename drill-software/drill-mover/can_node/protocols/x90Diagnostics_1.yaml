# output diagnostics part 1

parser_version: 2

name: x90 Output Diagnostics
class_name: x90Diagnostics_1

rate: 10
is_extended: true
dlc: 8

id: 0x200

data:
  # incl_pin_push Overload
  inc_pin_push_overload:
    type: t
    byte: 1
    bit: 1
  inc_pin_push_error:
    type: t
    byte: 1
    bit: 2
  # incl_pin_desc Overload
  inc_pin_desc_overload:
    type: t
    byte: 1
    bit: 3
  inc_pin_desc_error:
    type: t
    byte: 1
    bit: 4
  # vertical pin_push Overload
  vert_pin_push_overload:
    type: t
    byte: 1
    bit: 5
  vert_pin_push_error:
    type: t
    byte: 1
    bit: 6
  # vertical pin_desc Overload
  vert_pin_desc_overload:
    type: t
    byte: 1
    bit: 7
  vert_pin_desc_error:
    type: t
    byte: 1
    bit: 8

  # wrench
  wrench_lock_overload:
    type: t
    byte: 2
    bit: 1
  wrench_lock_error:
    type: t
    byte: 2
    bit: 2
  wrench_unclock_overload:
    type: t
    byte: 2
    bit: 3
  wrench_unclock_error:
    type: t
    byte: 2
    bit: 4
  wrench_push_overload:
    type: t
    byte: 2
    bit: 5
  wrench_push_error:
    type: t
    byte: 2
    bit: 6
  wrench_desc_overload:
    type: t
    byte: 2
    bit: 7
  wrench_desc_error:
    type: t
    byte: 2
    bit: 8


  # dust_flap
  dustflap_push_overload:
    type: t
    byte: 3
    bit: 1
  dustflap_push_error:
    type: t
    byte: 3
    bit: 2
  dustflap_desc_overload:
    type: t
    byte: 3
    bit: 3
  dustflap_desc_error:
    type: t
    byte: 3
    bit: 4
  #tower
  tower_push_overload:
    type: t
    byte: 3
    bit: 5
  tower_push_error:
    type: t
    byte: 3
    bit: 6
  tower_desc_overload:
    type: t
    byte: 3
    bit: 7
  tower_desc_error:
    type: t
    byte: 3
    bit: 8

  # carousel
  carousel_push_overload:
    type: t
    byte: 4
    bit: 1
  carousel_push_error:
    type: t
    byte: 4
    bit: 2
  carousel_desc_overload:
    type: t
    byte: 4
    bit: 3
  carousel_desc_error:
    type: t
    byte: 4
    bit: 4
  carousel_rotCW_overload:
    type: t
    byte: 4
    bit: 5
  carousel_rotCW_error:
    type: t
    byte: 4
    bit: 6
  carousel_rotCCW_overload:
    type: t
    byte: 4
    bit: 7
  carousel_rotCCW_error:
    type: t
    byte: 4
    bit: 8

  # rotation HPWM
  rotation_A_overload:
    type: t
    byte: 5
    bit: 1
  rotation_A_error:
    type: t
    byte: 5
    bit: 2
  rotation_B_overload:
    type: t
    byte: 5
    bit: 3
  rotation_B_error:
    type: t
    byte: 5
    bit: 4
  #feed
  feed_A_overload:
    type: t
    byte: 5
    bit: 5
  feed_A_error:
    type: t
    byte: 5
    bit: 6
  feed_B_overload:
    type: t
    byte: 5
    bit: 7
  feed_B_error:
    type: t
    byte: 5
    bit: 8

  # cat left HPWM
  cat_left_A_overload:
    type: t
    byte: 6
    bit: 1
  cat_left_A_error:
    type: t
    byte: 6
    bit: 2
  cat_left_B_overload:
    type: t
    byte: 6
    bit: 3
  cat_left_B_error:
    type: t
    byte: 6
    bit: 4
  # cat right HPWM
  cat_right_A_overload:
    type: t
    byte: 6
    bit: 5
  cat_right_A_error:
    type: t
    byte: 6
    bit: 6
  cat_right_B_overload:
    type: t
    byte: 6
    bit: 7
  cat_right_B_error:
    type: t
    byte: 6
    bit: 8

  # jack back
  jack_back_left_push_overload:
    type: t
    byte: 7
    bit: 1
  jack_back_left_push_error:
    type: t
    byte: 7
    bit: 2
  jack_back_left_desc_overload:
    type: t
    byte: 7
    bit: 3
  jack_back_left_desc_error:
    type: t
    byte: 7
    bit: 4
  jack_back_right_push_overload:
    type: t
    byte: 7
    bit: 5
  jack_back_right_push_error:
    type: t
    byte: 7
    bit: 6
  jack_back_right_desc_overload:
    type: t
    byte: 7
    bit: 7
  jack_back_right_desc_error:
    type: t
    byte: 7
    bit: 8

  # jack front
  jack_front_left_push_overload:
    type: t
    byte: 8
    bit: 1
  jack_front_left_push_error:
    type: t
    byte: 8
    bit: 2
  jack_front_left_desc_overload:
    type: t
    byte: 8
    bit: 3
  jack_front_left_desc_error:
    type: t
    byte: 8
    bit: 4
  jack_front_right_push_overload:
    type: t
    byte: 8
    bit: 5
  jack_front_right_push_error:
    type: t
    byte: 8
    bit: 6
  jack_front_right_desc_overload:
    type: t
    byte: 8
    bit: 7
  jack_front_right_desc_error:
    type: t
    byte: 8
    bit: 8