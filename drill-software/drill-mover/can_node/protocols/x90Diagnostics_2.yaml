# output diagnostics part 2

parser_version: 2

name: x90 Output Diagnostics part 2
class_name: x90Diagnostics_2

rate: 10
is_extended: true
dlc: 8

id: 0x201

data:
  # arm Overload
  arm_open_overload:
    type: t
    byte: 1
    bit: 1
  arm_open_error:
    type: t
    byte: 1
    bit: 2
  arm_close_overload:
    type: t
    byte: 1
    bit: 3
  arm_close_error:
    type: t
    byte: 1
    bit: 4
  # fork
  fork_push_overload:
    type: t
    byte: 1
    bit: 5
  fork_push_error:
    type: t
    byte: 1
    bit: 6
  fork_desc_overload:
    type: t
    byte: 1
    bit: 7
  fork_desc_error:
    type: t
    byte: 1
    bit: 8

  # fan
  fan_overload:
    type: t
    byte: 2
    bit: 1
  fan_error:
    type: t
    byte: 2
    bit: 2
  # feed control
  feed_overload:
    type: t
    byte: 2
    bit: 3
  feed_error:
    type: t
    byte: 2
    bit: 4
  #rotation control
  rotation_overload:
    type: t
    byte: 2
    bit: 5
  rotation_error:
    type: t
    byte: 2
    bit: 6
  #hoover
  hoover_overload:
    type: t
    byte: 2
    bit: 7
  hoover_error:
    type: t
    byte: 2
    bit: 8

  # driller relay
  driller_relay_overload:
    type: t
    byte: 3
    bit: 1
  driller_relay_error:
    type: t
    byte: 3
    bit: 2
  #compressor relay
  compressor_relay_overload:
    type: t
    byte: 3
    bit: 3
  compressor_relay_error:
    type: t
    byte: 3
    bit: 4