# overload clear

parser_version: 2

name: x90 Output Overload Clear
class_name: x90OverloadClear

rate: 10
is_extended: true
dlc: 8

id: 0x315

data:
  # pins
  inc_pin_push_overloadClear:
    type: t
    byte: 1
    bit: 1
  inc_pin_desc_overloadClear:
    type: t
    byte: 1
    bit: 2
  vert_pin_push_overloadClear:
    type: t
    byte: 1
    bit: 3
  vert_pin_desc_overloadClear:
    type: t
    byte: 1
    bit: 4
  # wrench
  wrench_lock_overloadClear:
    type: t
    byte: 1
    bit: 5
  wrench_unlock_overloadClear:
    type: t
    byte: 1
    bit: 6
  wrench_push_overloadClear:
    type: t
    byte: 1
    bit: 7
  wrench_desc_overloadClear:
    type: t
    byte: 1
    bit: 8

 #dust
  dust_flaps_push_overloadClear:
    type: t
    byte: 2
    bit: 1
  dust_flaps_desc_overloadClear:
    type: t
    byte: 2
    bit: 2
  #tower
  tower_push_overloadClear:
    type: t
    byte: 2
    bit: 3
  tower_desc_overloadClear:
    type: t
    byte: 2
    bit: 4
  #carousel
  carousel_push_overloadClear:
    type: t
    byte: 2
    bit: 5
  carousel_desc_overloadClear:
    type: t
    byte: 2
    bit: 6
  carousel_rotCW_overloadClear:
    type: t
    byte: 2
    bit: 7
  carousel_rotCCW_overloadClear:
    type: t
    byte: 2
    bit: 8

    #rotation AB
  rotation_A_overloadClear:
    type: t
    byte: 3
    bit: 1
  rotation_B_overloadClear:
    type: t
    byte: 3
    bit: 2
    #feed
  feed_A_overloadClear:
    type: t
    byte: 3
    bit: 3
  feed_B_overloadClear:
    type: t
    byte: 3
    bit: 4
  #cats
  cat_left_A_overloadClear:
    type: t
    byte: 3
    bit: 5
  cat_left_B_overloadClear:
    type: t
    byte: 3
    bit: 6
  cat_right_A_overloadClear:
    type: t
    byte: 3
    bit: 7
  cat_right_B_overloadClear:
    type: t
    byte: 3
    bit: 8

   #jacks
  #jack_back
  jack_back_left_push_overloadClear:
    type: t
    byte: 4
    bit: 1
  jack_back_left_desc_overloadClear:
    type: t
    byte: 4
    bit: 2
  jack_back_right_push_overloadClear:
    type: t
    byte: 4
    bit: 3
  jack_back_right_desc_overloadClear:
    type: t
    byte: 4
    bit: 4
  #jack_front
  jack_front_left_push_overloadClear:
    type: t
    byte: 4
    bit: 5
  jack_front_left_desc_overloadClear:
    type: t
    byte: 4
    bit: 6
  jack_front_right_push_overloadClear:
    type: t
    byte: 4
    bit: 7
  jack_front_right_desc_overloadClear:
    type: t
    byte: 4
    bit: 8

    #arm
    arm_open_overloadClear:
      type: t
      byte: 5
      bit: 1
    arm_close_overloadClear:
      type: t
      byte: 5
      bit: 2
    #fork
    fork_push_overloadClear:
      type: t
      byte: 5
      bit: 3
    fork_desc_overloadClear:
      type: t
      byte: 5
      bit: 4
    #fan
    fan_cntrl_overloadClear:
      type: t
      byte: 5
      bit: 5
    #feed ctrl
    feed_overloadClear:
      type: t
      byte: 5
      bit: 6
    #rot ctrl
    rotation_overloadClear:
      type: t
      byte: 5
      bit: 7
    #hoover
    hoover_overloadClear:
      type: t
      byte: 5
      bit: 8

    #driller relay
    driller_relay_overloadClear:
      type: t
      byte: 6
      bit: 1
    #compressor relay
    compressor_relay_overloadClear:
      type: t
      byte: 6
      bit: 2