#данные о воде , уровни топлива

parser_version: 2

name: x90 Water Data
class_name: x90WaterData

rate: 10
is_extended: true
dlc: 8

id: 0x117

data:
  #  подача воды BC
  water_supply:
    type: t
    byte: 1
    bit: 1
  # уровень воды в баке BC
  water_level:
    type: H
    byte: 2
    resolution: 10/32767
  # уровень жидкости сцепления BC
  level_clutch_fuild:
    type: H
    byte: 4
    resolution: 10/32767
  # уровень топлива BC
  level_fuel:
    type: H
    byte: 6
    resolution: 32/32767
