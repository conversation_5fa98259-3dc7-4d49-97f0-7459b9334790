# J1939 XBR
# External Brake Request.

parser_version: 2

name: External Brake Request
class_name: XBR

rate: 50
is_extended: true
dlc: 8

message_type: J1939_general
pgn: 0x040b
priority: 0x03

data:
  # Acceleration which the brake system is expected to realize.
  # It is specified as an absolute acceleration in reference to the road.
  # Positive values lead to increasing vehicle speed, negative values lead to decreasing vehicle speed.
  # Units: m/s^2, range: -10.0 to +10.0
  external_acceleration_demand: {
    type: H, byte: 1, resolution: 1/2048, offset: -15.687
  }
  # Use endurance brakes (i. e. retarder) during deceleration.
  # 00 - No endurance brake integration allowed
  # 01 - Only endurance brakes allowed (not safe, disabled)
  # 10 - Endurance brake integration allowed
  xbr_ebi_mode: {
    type: u2, byte: 3, bit: 1,
    defines: { RETARDER_DISABLED: 0b00, RETARDER_ENABLED: 0b10 }
  }
  # Priority of overlapping external and internal requests.
  # 00 - Highest priority – overrides any brake protection measures of the brake system
  # 01 - High priority – not defined (disabled)
  # 10 - Medium priority – does not override brake protection measures of the brake system
  # 11 - Low priority – used in ”override disabled” XBR Control Mode
  xbr_priority: {
    type: u2, byte: 3, bit: 3,
    defines: { PRIORITY_MEDIUM: 0b10, PRIORITY_HIGHEST: 0b00 }
  }
  # Defines how the external acceleration demand has to be realized.
  # 00 - Override disabled – Disable any existing control commanded by the source of this command.
  # 01 - Acceleration control with addition mode - Add the XBR acceleration demand to the driver’s acceleration demand.
  # 10 - Acceleration control with maximum mode - Execute the XBR acceleration demand if it is higher than the driver’s acceleration demand.
  xbr_control_mode: {
    type: u2, byte: 3, bit: 5,
    defines: { MODE_DISABLED: 0b00, MODE_ADDITION: 0b01, MODE_MAXIMUM: 0b10 }
  }
  # Not functional now. See J1939 DA for details.
  # Units: %, range: 0 to 100
  xbr_urgency: {
    type: H, byte: 4, resolution: 0.4, offset: 0
  }
  # The XBR message counter.
  # In every following message the counter is incremented by 1 (0 follows 15).
  xbr_message_counter: {
    type: u4, byte: 8, bit: 1, do_not_clear: true
  }
  # The XBR message checksum.
  # Checksum = (Byte1 + Byte2 + Byte3 + Byte4 + Byte5 + Byte6 + Byte7 + (message counter & 0x0F) +
  #             message ID low byte + message ID mid low byte + message ID mid high byte + message ID high byte)
  # XBR Message Checksum = ((Checksum >> 4) + Checksum) & 0x0F
  xbr_message_checksum: {
    type: u4, byte: 8, bit: 5
  }

functions:
  - name: counter_func
    args: { field: xbr_message_counter, max_value: 15 }

  - name: xbr_checksum_func
    args: { }
