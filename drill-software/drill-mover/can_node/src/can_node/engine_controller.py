#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import common.can_base as can_base
from common.base_node import BaseNode
from can_msgs.msg import Frame
from drill_msgs.msg import FloatStamped
import os
import yaml


class EngineControllerNode(BaseNode):
    def __init__(self, name):
        super(EngineControllerNode, self).__init__(name)

        self.engine_cmd_pub = rospy.Publisher(self.node_params['engine_topic'],
                                                    Frame, queue_size=10)

        rospy.Subscriber(rospy.get_param('Global/topics/engine_ctrl'),
                         FloatStamped, self.engine_ctrl_callback)

        path, _ = os.path.split(os.path.realpath(__file__))
        protocol_file = path + '/../../protocols/tsc1.yaml'
        try:
            with open(protocol_file, 'r') as protocol:
                yaml_params = yaml.safe_load(protocol.read())

            self.message_class = can_base.get_message_class(yaml_params, source_id=0xFF)()
        except AttributeError as e:
            rospy.logerr("Exception while handling yaml_params file: " + protocol_file)
            raise e

        self.message_class.requested_speed = self.node_params['default_rpm']

    def engine_ctrl_callback(self, msg):
        self.message_class.requested_speed = msg.value

    def do_work(self):
        msg = self.message_class.serialize()
        msg.data = (msg.data[0], msg.data[1], msg.data[2], 0xFF, 0xFF, 0xFF, 0xFF, 0xFF)
        self.engine_cmd_pub.publish(msg)