#!/usr/bin/env python
# -*- coding: utf-8 -*-


import importlib
import numpy as np
import os
import sys
import yaml
import functools
from std_msgs.msg import String

import common.can_base as can_base
import roslib
import rospy
from common.base_node import BaseNode
from common_msgs.msg import MovePermission
from common.subscriber import Subscriber
import can_msgs.msg as can_msgs
from drill_msgs.msg import VehicleBehaviorMode


def rsetattr(obj, attr, val):
    pre, _, post = attr.rpartition('.')
    return setattr(rgetattr(obj, pre) if pre else obj, post, val)

def rgetattr(obj, attr, *args):
    def _getattr(obj, attr):
        return getattr(obj, attr, *args)
    return functools.reduce(_getattr, [obj] + attr.split('.'))


class CanNode(BaseNode):

    def __init__(self, name):
        super(CanNode, self).__init__(name)

        self.publishers = {}
        self.subscribers = {}
        self.can_msgs = {}
        self.modules = {}

        self.ios_values_store = {}
        self.move_permission = True
        self.main_mode = None
        self.pub_threads = {}

        self.input_topics = {}
        self.switch_input_topics_parameters(self.main_mode)
        self.output_topics = self.set_output_topics_params()
        self.ios_params = self.set_ios_params()
        self.can_channels_names_in, self.can_channels_names_out = self.get_can_channels_names()

        self.can_msg_serializers = dict.fromkeys(self.can_channels_names_out)
        self.can_msg_deserializers = dict.fromkeys(self.can_channels_names_in)

        self.can_ios_storage = self.make_can_ios_storage()


        for channel_name in self.can_channels_names_out:
            self.publishers[channel_name] = rospy.Publisher(channel_name, can_msgs.Frame, queue_size=100)

        for io, single_io in list(self.ios_params.items()):
            self.can_msgs[single_io['protocol'] + str(single_io.get('source_id', '_'))] = \
                {'file': single_io['protocol'] + '.yaml', 'source_id': single_io.get('source_id', '_')}

        _module = sys.modules[__name__]
        path, _ = os.path.split(os.path.realpath(__file__))
        for io, params in list(self.can_msgs.items()):
            protocol_file = path + '/../../protocols/' + params.pop('file')
            try:
                with open(protocol_file, 'r') as protocol:
                    yaml_params = yaml.safe_load(protocol.read())
                message_class = can_base.get_message_class(yaml_params, **params)
            except AttributeError as e:
                rospy.logerr("Exception while handling yaml_params file: " + protocol_file)
                raise
            setattr(_module, io, message_class)

        for can_channel_name in self.can_channels_names_out:
            single_serializer = dict()
            for io_name, io_params in list(self.ios_params.items()):
                if can_channel_name in io_params['can_channel']:
                    self.ios_values_store[io_name] = {'value': 0, 'upd_time': 0}

                    _module = sys.modules[__name__]
                    x = getattr(_module, io_params['protocol'] + str(io_params.get('source_id', '_')))
                    single_serializer[io_params['protocol'] + str(io_params.get('source_id', '_'))] = x()
            self.can_msg_serializers[can_channel_name] = single_serializer

        for can_channel_name in self.can_channels_names_in:
            single_deserializer = dict()
            for io_name, io_params in list(self.ios_params.items()):
                if can_channel_name in io_params['can_channel']:
                    self.ios_values_store[io_name] = {'value': 0, 'upd_time': 0}
                    _module = sys.modules[__name__]
                    x = getattr(_module, io_params['protocol'] + str(io_params.get('source_id', '_')))
                    single_deserializer[io_params['protocol'] + str(io_params.get('source_id', '_'))] = x()
            self.can_msg_deserializers[can_channel_name] = single_deserializer

        for topic, single_topic in list(self.output_topics.items()):
            topic_type_module, topic_type_name = tuple(single_topic['msg_type'].split('/'))
            try:
                roslib.load_manifest(topic_type_module)
                msg_module = importlib.import_module(topic_type_module + '.msg', topic_type_module)
                rostype = getattr(msg_module, topic_type_name)
                self.publishers[topic] = rospy.Publisher(topic, rostype, queue_size=10)
            except AttributeError as e:
                rospy.logerr('Could not find the required resource: %s', str(e))

        rospy.Subscriber(rospy.get_param('Global/topics/permission'),
                         MovePermission, self.permission_callback)

        rospy.Subscriber(rospy.get_param("Global/topics/main_mode"),
                         VehicleBehaviorMode, self.main_mode_callback)

        self.can_node_log = rospy.Publisher('can_node_log', String, queue_size=10)

    def on_start(self):

        # SUBSCRIBERS SECTION

        for channel_name in self.can_channels_names_in:
            self.subscribers[channel_name] = Subscriber(channel_name, can_msgs.Frame,
                                                    self.all_can_topics_callback,
                                                    channel_name, queue_size=150)

        for topic, single_topic in list(self.input_topics.items()):
            topic_type_module, topic_type_name = tuple(single_topic['msg_type'].split('/'))
            try:
                roslib.load_manifest(topic_type_module)
                msg_module = importlib.import_module(topic_type_module + '.msg', topic_type_module)
                rostype = getattr(msg_module, topic_type_name)
                self.subscribers[topic] = Subscriber(topic, rostype, self.all_topics_callback,
                                                     topic, queue_size=10)
                if not single_topic.get('same_in_remote'):
                    self.subscribers['remote_%s' % topic] = Subscriber('remote_%s' % topic, rostype,
                                                                     self.all_topics_callback,
                                                                     'remote_%s' % topic)

            except AttributeError as e:
                rospy.logerr(
                    'Could not find the required resource: %s', str(e))

    def initialize_params(self):
        self.ios_params = self.set_ios_params()

    def make_can_ios_storage(self):
        can_ios = {}
        single_can_ios = []
        for can_channel in self.can_channels_names_in:
            for io_name, io_params in list(self.ios_params.items()):
                if can_channel == io_params['can_channel']:
                    single_can_ios.append(io_name)
            can_ios[can_channel] = single_can_ios
        return can_ios

    def get_can_channels_names(self):
        can_channels_names_in = []
        can_channels_names_out = []
        for io_name, io_params in list(self.ios_params.items()):
            # can_channel = io_params['can_channel'].translate(None, '/trx')
            if 'rx' in io_params['can_channel'] and io_params['can_channel'] not in can_channels_names_out:
                can_channels_names_out.append(io_params['can_channel'])
            elif 'tx' in io_params['can_channel'] and io_params['can_channel'] not in can_channels_names_in:
                can_channels_names_in.append(io_params['can_channel'])
        return can_channels_names_in, can_channels_names_out

    def req_value_field(self, io_name):
        io_params = self.ios_params.get(io_name)
        return str(io_params['value_field'])

    def get_rostype(self, type):
        topic_type_module, topic_type_name = tuple(type['msg_type'].split('/'))
        msg_module = importlib.import_module(topic_type_module + '.msg', topic_type_module)
        rostype = getattr(msg_module, topic_type_name)
        return rostype

    def set_ios_params(self):
        all_ios_params = self.node_params['IOs']
        for io in all_ios_params:
            all_ios_params[io]['updated'] = False
            all_ios_params[io]['upd_time'] = 0
            if all_ios_params[io].get('polynomial_pos'):
                all_ios_params[io]['_polynomial_func_pos'] = (
                    np.polynomial.polynomial.Polynomial(all_ios_params[io]['polynomial_pos']))
            if all_ios_params[io].get('polynomial_neg'):
                all_ios_params[io]['_polynomial_func_neg'] = (
                    np.polynomial.polynomial.Polynomial(all_ios_params[io]['polynomial_neg']))
            if all_ios_params[io].get('polynomial'):
                all_ios_params[io]['_polynomial_func'] = (
                    np.polynomial.polynomial.Polynomial(all_ios_params[io]['polynomial']))
        return all_ios_params

    def set_output_topics_params(self):
        all_output_topics_params = self.node_params['output_topics']
        for io in all_output_topics_params:
            all_output_topics_params[io]['last_pub_time'] = 0
        return all_output_topics_params

    def permission_callback(self, message):
        self.move_permission = message.permission
        if not self.move_permission:
            self.set_all_req_topics_defaults()

    def main_mode_callback(self, message):
        if self.main_mode != message.mode:
            self.main_mode = message.mode
            self.switch_input_topics_parameters(self.main_mode)

    def switch_input_topics_parameters(self, main_mode):
        input_topics = {}
        for topic_name, topic_params in list(self.node_params['input_topics'].items()):
            if main_mode is not None and main_mode in (VehicleBehaviorMode.REMOTE,) and not topic_params.get('same_in_remote'):
                input_topics['remote_' + topic_name] = topic_params
            else:
                input_topics[topic_name] = topic_params
            topic_params['update_time'] = 0

        self.input_topics = input_topics

    def set_all_req_topics_defaults(self):
        for topic in self.input_topics:
            if ('ignore_move_permission' not in list(self.input_topics[topic].keys()) or
                not self.input_topics[topic]['ignore_move_permission']):
                self.set_single_topic_default(topic)

    def set_single_topic_default(self, topic_name):
        topic_params = self.input_topics.get(topic_name)
        fields = topic_params['fields']
        for f_name, f_params in list(fields.items()):
            self.ios_values_store[f_params['io']]['value'] = f_params['default']

    def from_can_to_sys_data_pub(self):
        for topic_name, topic_params in list(self.output_topics.items()):
            msg_type = self.get_rostype(topic_params)
            fields = topic_params['fields']
            new_msg = msg_type()
            for field_name, field_params in list(fields.items()):
                if field_params['io'] in list(self.ios_values_store.keys()):
                    setattr(new_msg, field_name,
                            self.ios_values_store[field_params['io']]['value'])
            self.publishers[topic_name].publish(new_msg)

    def from_sys_to_can_data_pub(self):
        for io_name, io_params in list(self.ios_params.items()):
            can_channel_name = io_params['can_channel']
            if 'rx' not in can_channel_name:
                continue
            io_value = self.ios_values_store.get(str(io_name))

            if io_value is not None:
                setattr(self.can_msg_serializers[can_channel_name][io_params['protocol'] + str(io_params.get('source_id', '_'))],
                        io_params['value_field'], io_value['value'])

        # for io_name, io_params in self.ios_params.items():
        #     if 'rx' in io_params['can_channel']:
        #         can_channel_name = io_params['can_channel'].translate(None, '/trx')
        #         self.publishers[io_params['can_channel']].publish(
        #             self.can_msg_serializers[can_channel_name][io_params['protocol'] +
        #                                                        str(io_params.get('source_id', '_'))].serialize())

        for can_channel_name in list(self.can_msg_serializers.keys()):
            for protocol in list(self.can_msg_serializers[can_channel_name].keys()):
                self.publishers[can_channel_name].publish(self.can_msg_serializers[can_channel_name][protocol].serialize())

    def convert_value_for_can(self, value, field):
        io_params = self.ios_params.get(field['io'])
        deadband_threshold = io_params.get('deadband_threshold', 0.001)

        if value == 0 or abs(value - 0) < deadband_threshold:
            io_value = 0

        elif value > 0:
            deadband_value_pos = io_params.get('deadband_value_pos', 0)
            pos_input_range = max(field['max_value'], 1e-9)
            pos_io_range = max(io_params['max_value'] - deadband_value_pos, 0)
            normalized_value = value / pos_input_range

            if io_params.get('_polynomial_func_pos'):
                normalized_value = io_params['_polynomial_func_pos'](normalized_value)
            elif io_params.get('_polynomial_func'):
                normalized_value = io_params['_polynomial_func'](normalized_value)

            io_value = normalized_value * pos_io_range

            if io_value != 0:
                io_value += deadband_value_pos

            io_value = min(io_value, pos_io_range + deadband_value_pos)

        elif value < 0:
            deadband_value_neg = io_params.get('deadband_value_neg', 0)
            neg_input_range = min(field['min_value'], -1e-9)
            neg_io_range = min(io_params['min_value'] - deadband_value_neg, 0)
            normalized_value = value / neg_input_range

            if io_params.get('_polynomial_func_neg'):
                normalized_value = io_params['_polynomial_func_neg'](normalized_value)
            elif io_params.get('_polynomial_func'):
                normalized_value = io_params['_polynomial_func'](normalized_value)

            io_value = normalized_value * neg_io_range

            if io_value != 0:
                io_value += deadband_value_neg

            io_value = max(io_value, neg_io_range + deadband_value_neg)

        return io_value

    def get_topic_from_io_name(self, io_name):
        for topic, topic_params in list(self.output_topics.items()):
            fields = topic_params['fields']
            for field_name, field_io in list(fields.items()):
                if io_name == field_io['io']:
                    return topic

    def all_topics_callback(self, msg, topic):
        topic_info = self.input_topics.get(topic)
        if not topic_info:
            return
        if not self.move_permission and not topic_info.get('ignore_move_permission'):
            return

        for f in topic_info['fields']:
            field = topic_info['fields'].get(f)
            original_value = getattr(msg, f)
            if 'min_value' in field and 'max_value' in field:
                if field['min_value'] <= original_value <= field['max_value']:
                    self.ios_values_store[field['io']]['value'] = \
                        self.convert_value_for_can(original_value, field)
                    self.ios_values_store[field['io']]['updated'] = True
                    self.ios_values_store[field['io']]['upd_time'] = rospy.get_time()
                else:
                    rospy.logerr(
                        'Received value is not in its range (topic field: %s %s)',
                        str(topic), str(f))

            else:
                self.ios_values_store[field['io']]['value'] = original_value
                self.ios_values_store[field['io']]['updated'] = True
                self.ios_values_store[field['io']]['upd_time'] = rospy.get_time()

        topic_info['update_time'] = rospy.get_time()

    def all_can_topics_callback(self, message, topic):
        can_channel_name = topic
        if can_channel_name in list(self.can_msg_deserializers.keys()):
            for ser_name, io_deserializer in list(self.can_msg_deserializers[can_channel_name].items()):
                if io_deserializer.deserialize(message):
                    for io_name in self.can_ios_storage[can_channel_name]:
                        io_params = self.ios_params.get(io_name)
                        io_ser_name = io_params['protocol'] + str(io_params.get('source_id', '_'))
                        if ser_name == io_ser_name:
                            self.ios_values_store[io_name]['value'] = \
                                getattr(self.can_msg_deserializers[can_channel_name][ser_name],
                                        self.req_value_field(io_name))
                            if 'inverse' in list(self.ios_params[io_name].keys()):
                                do_inverse = self.ios_params[io_name].get('inverse')
                                if do_inverse:
                                    self.ios_values_store[io_name]['value'] = not self.ios_values_store[io_name]['value']
                            self.ios_values_store[io_name]['upd_time'] = rospy.get_time()
                            required_topic = self.get_topic_from_io_name(io_name)
                            if required_topic:
                                self.check_if_whole_msg_updated(required_topic)

    def check_if_whole_msg_updated(self, topic_name):
        whole_check = True
        topic_params = self.output_topics.get(topic_name)
        fields = topic_params['fields']
        for field_name, field_params in list(fields.items()):
            if self.ios_values_store[field_params['io']]['upd_time'] == 0 or \
                    self.ios_values_store[field_params['io']]['upd_time'] < \
                    topic_params['last_pub_time']:
                whole_check = False
                break
        if whole_check:
            msg_type = self.get_rostype(topic_params)
            fields = topic_params['fields']
            new_msg = msg_type()
            new_msg.header.stamp = rospy.Time.now()
            for field_name, field_params in list(fields.items()):
                if field_params['io'] in list(self.ios_values_store.keys()):
                    rsetattr(new_msg, field_name,
                            self.ios_values_store[field_params['io']]['value'])
                    self.ios_params[field_params['io']]['updated'] = False

            self.publishers[topic_name].publish(new_msg)
            self.output_topics[topic_name]['last_pub_time'] = rospy.get_time()
        else:
            pass

    def check_if_input_topics_updated(self):
        for topic, topic_params in list(self.input_topics.items()):
            if rospy.get_time() - topic_params['update_time'] > topic_params['timeout']:
                self.set_single_topic_default(topic)

    def do_work(self):
        self.check_if_input_topics_updated()
        self.from_sys_to_can_data_pub()

    def on_stop(self):
        self.set_all_req_topics_defaults()
