#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
from common.base_node import BaseNode
from drill_msgs.msg import BoolStamped
from can_msgs.msg import Frame


class RoboModeControllerNode(BaseNode):
    def __init__(self, name):
        super(RoboModeControllerNode, self).__init__(name)
        self.new_robomode = False
        self.selector_switch_allowed = True

        self.current_robomode_pub = rospy.Publisher(self.global_params['topics']['current_robomode'],
                                            BoolStamped, queue_size=10)

        self.selector_robomode_sp_pub = rospy.Publisher(self.global_params['topics']['selector_robomode_sp'],
                                                    BoolStamped, queue_size=10)

        self.can_robomode_cmd_pub = rospy.Publisher(self.node_params['can_module_topic'],
                                                    Frame, queue_size=10)

        rospy.Subscriber(rospy.get_param('Global/topics/rmo_robomode_sp'),
                         Bo<PERSON><PERSON>tamped, self.rmo_robomode_callback)

        rospy.Subscriber(rospy.get_param('Global/topics/selector_robomode_fb'),
                         BoolStamped, self.selector_robomode_callback)

    def swith_can_module(self, mode):
        msg = Frame()
        msg.header.stamp = rospy.get_rostime()
        msg.id = 419429885
        msg.is_extended = True
        msg.dlc = 2
        msg.data = [240, 0 if not mode else 1, 0, 0, 0, 0, 0, 0]
        self.can_robomode_cmd_pub.publish(msg)


    def swith_selector(self, mode):
        msg = BoolStamped()
        msg.header.stamp = rospy.get_rostime()
        msg.value = mode
        self.selector_robomode_sp_pub.publish(msg)

    def rmo_robomode_callback(self, message):
        if message.value != self.new_robomode:
            self.swith_can_module(message.value)
            self.swith_selector(message.value)
            self.new_robomode = message.value
            self.selector_switch_allowed = False

    def selector_robomode_callback(self, message):
        if message.value != self.new_robomode:
            if self.selector_switch_allowed:
                self.swith_can_module(message.value)
                self.new_robomode = message.value
                self.selector_switch_allowed = False
        else:
            self.selector_switch_allowed = True


    def do_work(self):
        msg = BoolStamped()
        msg.header.stamp = rospy.get_rostime()
        msg.value = self.new_robomode
        self.current_robomode_pub.publish(msg)

    def on_stop(self):
        pass
