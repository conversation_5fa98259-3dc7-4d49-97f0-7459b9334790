#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy

from .constants import ID<PERSON>, CL<PERSON>ING, TURN_CW, TURN_CCW
from state_machine_node import AbstractState


class IdleState(AbstractState):
    def __init__(self, node):
        super(IdleState, self).__init__(node, IDLE)

    def on_transition_to(self):
        self.node._cur_action_seq = -1

    def do_work(self):
        self.node.stop_control()
        with self.node.task_lock:
            if self.node.carousel_action == 0 and (not self.node.subs.carousel_state.closed or not self.node.subs.carousel_state.index_1):
                self.node.set_state(CLOSING)
            elif self.node.carousel_action == 1 and (not self.node.subs.carousel_state.index_1
                                                     or not self.node.subs.carousel_state.open):
                self.node.set_state(TURN_CW)
            elif self.node.carousel_action == 2 and (not self.node.subs.carousel_state.index_2
                                                     or not self.node.subs.carousel_state.open):
                self.node.set_state(TURN_CCW)
            else:
                self.node._cur_action_seq = -1


class ClosingState(AbstractState):
    def __init__(self, node):
        super(ClosingState, self).__init__(node, CLOSING)

    def on_transition_to(self):
        self.rotation_complete = False

    def do_work(self):
        if not self.rotation_complete:
            if not self.node.subs.carousel_state.index_1:
                if rospy.get_time() - self.node.get_change_state_timestamp() > self.node.max_turn_time:
                    err_descr = "Can't rotate carousel to index 1!"
                    err_type = self.node.global_params['Reports']['Critical']['Action_failed']
                    self.node.handle_internal_error(error_message=err_descr,
                                                    error_type=err_type,
                                                    event=self.node.global_params['events']['rc_rotate_carousel_idx1'])
                self.node.carousel_control.rotate = -self.node.rotate_speed
            else:
                self.rotation_complete = True
                self.node.carousel_control.rotate = 0
        else:
            if not self.node.subs.carousel_state.closed:
                if rospy.get_time() - self.node.get_change_state_timestamp() > self.node.max_oc_time:
                    err_descr = "Can't close carousel!"
                    err_type = self.node.global_params['Reports']['Critical']['Action_failed']
                    self.node.handle_internal_error(error_message=err_descr,
                                                    error_type=err_type,
                                                    event=self.node.global_params['events']['rc_close_carousel'])
                self.node.carousel_control.move = -self.node.close_speed
            else:
                self.node.stop_control()
                self.node.set_state(IDLE)


class TurnCwState(AbstractState):
    def __init__(self, node):
        super(TurnCwState, self).__init__(node, TURN_CW)

    def on_transition_to(self):
        self.rotation_complete = False

    def do_work(self):
        if not self.rotation_complete:
            if not self.node.subs.carousel_state.index_1:
                if rospy.get_time() - self.node.get_change_state_timestamp() > self.node.max_turn_time:
                    err_descr = "Can't rotate carousel to index 1!"
                    err_type = self.node.global_params['Reports']['Critical']['Action_failed']
                    self.node.handle_internal_error(error_message=err_descr,
                                                    error_type=err_type,
                                                    event=self.node.global_params['events']['rc_rotate_carousel_idx1'])
                self.node.carousel_control.rotate = -self.node.rotate_speed
            else:
                self.rotation_complete = True
                self.node.carousel_control.rotate = 0
        else:
            if not self.node.subs.carousel_state.open:
                if rospy.get_time() - self.node.get_change_state_timestamp() > self.node.max_oc_time:
                    err_descr = "Can't open carousel!"
                    err_type = self.node.global_params['Reports']['Critical']['Action_failed']
                    self.node.handle_internal_error(error_message=err_descr,
                                                    error_type=err_type,
                                                    event=self.node.global_params['events']['rc_open_carousel'])
                self.node.carousel_control.move = self.node.open_speed
            else:
                self.node.stop_control()
                self.node.set_state(IDLE)


class TurnCcwState(AbstractState):
    def __init__(self, node):
        super(TurnCcwState, self).__init__(node, TURN_CCW)

    def on_transition_to(self):
        self.rotation_complete = False

    def do_work(self):
        if not self.rotation_complete:
            if not self.node.subs.carousel_state.index_2:
                if rospy.get_time() - self.node.get_change_state_timestamp() > self.node.max_turn_time:
                    err_descr = "Can't rotate carousel to index 2!"
                    err_type = self.node.global_params['Reports']['Critical']['Action_failed']
                    self.node.handle_internal_error(error_message=err_descr,
                                                    error_type=err_type,
                                                    event=self.node.global_params['events']['rc_rotate_carousel_idx2'])
                self.node.carousel_control.rotate = self.node.rotate_speed
            else:
                self.rotation_complete = True
                self.node.carousel_control.rotate = 0
        else:
            if not self.node.subs.carousel_state.open:
                if rospy.get_time() - self.node.get_change_state_timestamp() > self.node.max_oc_time:
                    err_descr = "Can't open carousel!"
                    err_type = self.node.global_params['Reports']['Critical']['Action_failed']
                    self.node.handle_internal_error(error_message=err_descr,
                                                    error_type=err_type,
                                                    event=self.node.global_params['events']['rc_open_carousel'])
                self.node.carousel_control.move = self.node.open_speed
            else:
                self.node.stop_control()
                self.node.set_state(IDLE)