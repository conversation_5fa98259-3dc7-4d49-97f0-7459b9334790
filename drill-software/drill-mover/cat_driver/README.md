# cat_driver
cat_driver – управляет движением гусеничной платформы. Получает целевую траекторию, формирует целевые скорости (в м/с) для левой и правой гусениц. Имеет отдельное состояние для более аккуратного наведения на целевую точку в конце траектории.

![](./docs/rosgraph.png)

# Subscribed Topics 

* `planned_route` (`common_msgs/PlannedRoute`)
* `state` (`common_msgs/State`)
* `permission` (`common_msgs/MovePermission`)
* `system_status` (`common_msgs/SystemStatus`)

_Note: topics names are set in `topics` parameters (`drill_launch_pack/params/drill/global.yaml`)_.

# Published Topics

* `driver_out` (`drill_msgs/CatsStamped`)
* `speed_ctrl_stopped` (`drill_msgs/BoolStamped`)
* `moving_error` (`common_msgs/MovingError`)
* `internal_error` (`drill_msgs/InternalError`)

## Debug topics

* DEBUG_rotation_speed (`drill_msgs/FloatStamped`)
* DEBUG_path_deviation (`drill_msgs/FloatStamped`)
* DEBUG_target_yaw_pose (`geometry_msgs/PoseStamped`)
* DEBUG_correction_ang (`drill_msgs/FloatStamped`)
* DEBUG_yaw_sp (`drill_msgs/FloatStamped`)
* DEBUG_yaw (`drill_msgs/FloatStamped`)

# Parameters

_Note: for actual parameters list and values check `CatDriverNode` section
in nodes parameters ([`drill_launch_pack/params/drill/nodes.yaml`](../drill_launch_pack/params/drill/nodes.yaml)) file 
and vehicles params ([`drill_launch_pack/params/drill/vehicle.yaml`](../drill_launch_pack/params/drill/vehicle.yaml))._

## Node parameters

* `debug`&mdash;
* `rate` &mdash;
* `yaw_p` &mdash;
* `yawrate_pid`, `correction_pid`, `approach_yawrate_pid` gains (`p`, `i`, `d`, `i_saturation` for each pid regulator)
* `slowdown_k` &mdash; 
* `max_turn_rate` &mdash; 0.09
* `approach_max_turn_rate` &mdash; 0.008

* `future_yaw_len` &mdash; where to take target yaw
* `max_uncut_len` &mdash; Максимальное допустимое расстояние между машиной и началом маршрута метры

* `max_approach_speed`&mdash; 
* `max_approach_ang_err` &mdash; deg
* `GOAL_ACHIVED_ZONE` &mdash; 0.06
* `max_allowed_target_dist`: 0.15

* `yaw_thr_k` &mdash; Параметры для ErrorHandler
* `dist_thr_k` &mdash; Параметры для ErrorHandler

## Vehicle parameters

* `geometry.tracks_dist` &mdash; distance between tracks
* `max_speed` &mdash; max cat speed
* `geometry.tower2center` &mdash; расстояние от мачты до центра между гусеницами метры
