#!/usr/bin/env python
# -*- coding: utf-8-*-
from __future__ import division
import math
import rospy
import rosparam
import numpy as np

from collections import deque
from common_msgs.msg import State, MovePermission, MovingError, SystemStatus
from common.base_node import BaseNode
from common.geometry import MoverRoute, MoverPoint
from common.geometry.geometry import len_between, len_to_segment, route_length, norm_angle_minusPI_plusPI
from drill_msgs.msg import CatsStamped, BoolStamped, InternalError, FloatStamped
from common_msgs.msg import PlannedRoute
from std_msgs.msg import Header
from threading import Lock
from geometry_msgs.msg import PoseStamped
from tf.transformations import quaternion_from_euler


class CatDriverNode(BaseNode):

    def __init__(self, name):
        super(CatDriverNode, self).__init__(name)

        self.distance_between_tracks = self.vehicle_params["geometry"]["tracks_dist"]

        # Параметры ноды
        self.yawrate_pid = self.node_params["yawrate_pid"]
        self.approach_yawrate_pid = self.node_params["approach_yawrate_pid"]
        self.yaw_p = self.node_params["yaw_p"]

        self.max_cat_speed = self.vehicle_params["max_speed"]
        self.max_turn_rate = self.node_params["max_turn_rate"]

        self.future_yaw_dist = self.node_params["future_yaw_len"]

        # TODO: сохранять State как MoverPose?
        # Переменные для сохранение текущих State и PlannedRoute
        self.state = None
        self.route = MoverRoute()
        self.yaw_lock = Lock()
        self.route_lock = Lock()

        self.move_permission = True
        # Флаг для одноразового логирования отсутствия пермишина
        self.allow_log_perm_err = False
        self.wait_for_stop = False

        # Паблишер угловых скоростей колес
        self.angular_velocity_pub = rospy.Publisher(self.global_params['topics']['driver_out'],
                                                    CatsStamped, queue_size=10)

        self.stop_pub = rospy.Publisher(self.global_params['topics']['speed_ctrl_stopped'],
                                        BoolStamped, queue_size=1)

        self._moving_error_pub = rospy.Publisher(self.global_params['topics']['moving_error'],
                                                 MovingError, queue_size=10)

        self._system_error_pub = rospy.Publisher(self.global_params['topics']['internal_error'],
                                                 InternalError, queue_size=10)

        # DEBUG
        self.rot_speed_pub = rospy.Publisher("DEBUG_rotation_speed", FloatStamped, queue_size=10)
        self.deviation_pub = rospy.Publisher("DEBUG_path_deviation", FloatStamped, queue_size=10)
        self.debug_tyaw_pub = rospy.Publisher("DEBUG_target_yaw_pose", PoseStamped, queue_size=10)

        self.correction_ang_pub = rospy.Publisher("DEBUG_correction_ang", FloatStamped, queue_size=10)

        self.yaw_sp_pub = rospy.Publisher("DEBUG_yaw_sp", FloatStamped, queue_size=10)

        self.yaw_pub = rospy.Publisher("DEBUG_yaw", FloatStamped, queue_size=10)

        self.pid = PID(rospy.get_time,
                       self.yawrate_pid['i_saturation'],
                       self.yawrate_pid['p'],
                       self.yawrate_pid["i"],
                       self.yawrate_pid['d'],
                       is_angular=True,
                       )

        self.approach_pid = PID(rospy.get_time,
                       self.approach_yawrate_pid['i_saturation'],
                       self.approach_yawrate_pid['p'],
                       self.approach_yawrate_pid["i"],
                       self.approach_yawrate_pid['d'],
                       is_angular=False,
                       )

        self.correction_pid = PID(rospy.get_time,
                                  self.node_params["correction_pid"]['i_saturation'],
                                  self.node_params["correction_pid"]['p'],
                                  self.node_params["correction_pid"]["i"],
                                  self.node_params["correction_pid"]['d'],
                                  is_angular=False,
                                  )

        self.sm_status = "idle"

        self.state_updated = False

    def initialize_params(self):
        self.distance_between_tracks = self.vehicle_params["geometry"]["tracks_dist"]

        self.yawrate_pid = self.node_params["yawrate_pid"]
        self.approach_yawrate_pid = self.node_params["approach_yawrate_pid"]
        self.yaw_p = self.node_params["yaw_p"]

        self.max_cat_speed = self.vehicle_params["max_speed"]
        self.max_turn_rate = self.node_params["max_turn_rate"]

        self.future_yaw_dist = self.node_params["future_yaw_len"]

        self.pid.i_saturation = self.node_params["yawrate_pid"]['i_saturation']
        self.pid.p = self.node_params["yawrate_pid"]['p']
        self.pid.i = self.node_params["yawrate_pid"]['i']
        self.pid.d = self.node_params["yawrate_pid"]['d']

        self.approach_pid.i_saturation = self.node_params["yawrate_pid"]['i_saturation']
        self.approach_pid.p = self.node_params["approach_yawrate_pid"]['p']
        self.approach_pid.i = self.node_params["approach_yawrate_pid"]['i']
        self.approach_pid.d = self.node_params["approach_yawrate_pid"]['d']

        self.correction_pid.i_saturation = self.node_params["correction_pid"]['i_saturation']
        self.correction_pid.p = self.node_params["correction_pid"]['p']
        self.correction_pid.i = self.node_params["correction_pid"]['i']
        self.correction_pid.d = self.node_params["correction_pid"]['d']



    def on_start(self):
        rospy.Subscriber(self.global_params['topics']['planned_route'],
                         PlannedRoute, self.planned_route_callback)

        rospy.Subscriber(self.global_params['topics']['state'],
                         State, self.state_callback)

        rospy.Subscriber(self.global_params['topics']['permission'],
                         MovePermission, self.permission_callback)

        rospy.Subscriber(self.global_params['topics']['system_status'],
                         SystemStatus, self.system_status_callback)

    def do_work(self):
        # Если нет разрешения двигаться - тормозим, логируем и паблишим ошибку
        if not self.move_permission:
            if self.allow_log_perm_err:
                self.log("No permission for moving", loglevel=2)
                self.allow_log_perm_err = False

            moving_error = MovingError()
            moving_error.err_num = moving_error.BLOCKED_BY_PERMISSION
            self._moving_error_pub.publish(moving_error)

            self.stop_ctrl()
            return

        if self.state is None:
            self.log("waiting for state or RTK corrections", loglevel=2)
            self.stop_ctrl()
            return

        if self.sm_status != "idle" and self.state.quality != 4:
            self.log("waiting for RTK corrections", loglevel=2)
            self.stop_ctrl()
            return

        if rospy.Time.now().to_sec() - self.state.header.stamp.to_sec() > self.global_params['MSG_TTL'] \
                and self.sm_status != 'idle':
            err_msg = 'Old state! Stop!'
            err_type = self.global_params['Reports']['Errors']['Internal_error']
            self.handle_internal_error(error_message=err_msg,
                                       error_type=err_type,
                                       event=self.global_params['events']['rc_move'])
            return

        if not self.state_updated:
            return

        self.state_updated = False

        cat_left = 0
        cat_right = 0
        direction = None

        with self.route_lock:
            if self.route is None or len(self.route.points) < 2:
                self.sm_status = "idle"
                self.stop_ctrl()
                return

        with self.yaw_lock:
            yaw = self.state.yaw

        if self.route.direction == 'forward':
            direction = 1
        elif self.route.direction == 'backward':
            direction = -1
        else:
            self.log("wrong direction in plan!", loglevel=3)

        # Если едем задом - добавляем pi к yaw
        if direction < 0:
            yaw = norm_angle_minusPI_plusPI(yaw + math.pi)

        if self.sm_status == "idle":
            self.log("Got new route, switched to RUNNING", loglevel=1)
            self.sm_status = "running"
            self.correction_pid.integral = 0
            self.correction_pid.previous_error = 0
            self.correction_pid.previous_time = rospy.get_time()
            self.pid.integral = 0
            self.pid.previous_error = 0
            self.pid.previous_time = rospy.get_time()


        if (self.sm_status == "running"):
                # check for switching to approach mode
                if self.route.extra_data is not None and self.route.extra_data["do_approach"]:
                    self.sm_status = "approach"
                    self.log("Starting final approach", loglevel=1)
                    self.approach_pid.integral = 0
                    self.approach_pid.previous_error = 0
                    self.approach_pid.previous_time = rospy.get_time()
                    return

                # Если находимся далеко от 2й точки маршрута, значит планнер не обрезал маршрут - прерываем иттерацию
                #uncut_len = self.find_route_uncut_len(self.route, self.state.position)
                if len(self.route.points) >= 4 and \
                        len_to_segment(self.state.position, self.route.points[0], self.route.points[2]) > \
                        len_to_segment(self.state.position, self.route.points[2], self.route.points[3]):
                    err_msg = 'Planner did not cut route.'
                    err_type = self.global_params['Reports']['Errors']['Internal_error']
                    self.handle_internal_error(error_message=err_msg,
                                               error_type=err_type)
                    return

                # Считаем угол целевой ориентации по точкам траектории находящимся на растоянии x от текущего положения,
                # чтобы заранее начать исполнять ожидаемый впереди угол
                for i in range(1, len(self.route.points)):
                    segment_len = route_length(self.route, start=0, stop=i)

                    if segment_len >= self.future_yaw_dist:
                        t_yaw = math.atan2(self.route.points[i].y - self.route.points[i-1].y,
                                           self.route.points[i].x - self.route.points[i-1].x)
                        break
                    # Если маршрут короче future_yaw_len - считаем по двум последним точкам
                    else:
                        t_yaw = math.atan2(self.route.points[-1].y - self.route.points[-2].y,
                                           self.route.points[-1].x - self.route.points[-2].x)

                # Растояние от машины до отрезка траектории
                dist_to_segment = len_to_segment(
                    self.state.position, self.route.points[0], self.route.points[1]
                )
                # Угол направления от машины до второй точки траектории
                yaw_2 = math.atan2(self.route.points[1].y - self.state.position.y,
                                   self.route.points[1].x - self.state.position.x)


                if norm_angle_minusPI_plusPI(yaw_2 - t_yaw) < 0:
                    dist_to_segment *= -1.0
                self.deviation_pub.publish(FloatStamped(header=Header(stamp=rospy.get_rostime()),
                                                    value=dist_to_segment)
                )
                # Угол коррекции ориентации машины для устранения отклонения от траектории
                correction_angle = self.correction_pid.update(err=dist_to_segment)
                if correction_angle < -math.pi/2:
                    correction_angle = -math.pi/2
                if correction_angle > math.pi/2:
                    correction_angle = math.pi/2


                yaw_sp = norm_angle_minusPI_plusPI(t_yaw + correction_angle)
                # Целевая скорость движения на данном отрезке
                target_speed = (self.route.points[0].target_speed + self.route.points[1].target_speed) / 2
                yaw_err = norm_angle_minusPI_plusPI(yaw_sp - yaw)
                target_speed /= (1+abs(yaw_err)*self.node_params['slowdown_k'])
                rotation_speed = self.pid.update(yaw_sp, yaw)
                if abs(rotation_speed) > abs(self.max_turn_rate):
                    rotation_speed = self.max_turn_rate * np.sign(rotation_speed)
                if target_speed > self.get_max_speed(rotation_speed):
                    target_speed = self.get_max_speed(rotation_speed)

                # Получаем угловые скорости колес для заданных целевой скорости и скорости угла поворота
                cat_left, cat_right = self.calculate_cat_velocities(target_speed, rotation_speed)

                # Если едем задом - меняем местами скорости колес и делаем их отрицательными
                if direction < 0:
                    cat_left, cat_right = -cat_right, -cat_left

                self.stop_pub.publish(BoolStamped(header=Header(stamp=rospy.get_rostime()), value=False))
                self.pub_debug_topics(rotation_speed, correction_angle, yaw_sp, yaw)

        if self.sm_status == "approach":
            t_x = self.route.extra_data["hole_x"]
            t_y = self.route.extra_data["hole_y"]

            kobus_pos_x = self.state.position.x + self.vehicle_params['geometry']['tower2center'] * math.cos(self.state.yaw)
            kobus_pos_y = self.state.position.y + self.vehicle_params['geometry']['tower2center'] * math.sin(self.state.yaw)

            if math.hypot(t_x - kobus_pos_x, t_y-kobus_pos_y) > self.node_params['max_allowed_target_dist']:
                self.wait_for_stop = False
            if self.wait_for_stop or math.hypot(t_x - kobus_pos_x, t_y-kobus_pos_y) <= self.node_params['GOAL_ACHIVED_ZONE']:
                self.log("Target reached with error %.2f, stop!"%math.hypot(t_x - kobus_pos_x, t_y-kobus_pos_y), loglevel=1)
                self.stop_ctrl()
                self.wait_for_stop = True
                if self.state.speed < 0.01:
                    self.log("Stopped!", loglevel=1)
                    self.stop_pub.publish(BoolStamped(header=Header(stamp=rospy.get_rostime()), value=True))
                    self.sm_status = "idle"
                    self.wait_for_stop = False
                return

            yaw_to_target = math.atan2(t_y - kobus_pos_y, t_x - kobus_pos_x)
            #print "ytt: ", yaw_to_target

            yaw_err = norm_angle_minusPI_plusPI(self.state.yaw - yaw_to_target)
            #print "yerr: ", yaw_err

            target_speed = self.node_params['max_approach_speed'] - (abs(yaw_err)/math.radians(self.node_params['max_approach_ang_err']))*self.node_params['max_approach_speed']
            if target_speed < 0:
                target_speed = 0

            if abs(yaw_err) > math.pi / 2:  # target is behing drill
                yaw_err = (math.pi-abs(yaw_err))*np.sign(yaw_err)
                target_speed = self.node_params['max_approach_speed'] - (abs(yaw_err) / math.radians(
                    self.node_params['max_approach_ang_err'])) * self.node_params['max_approach_speed']
                if target_speed < 0:
                    target_speed = 0
                target_speed *= -1
            #print "nye: ", yaw_err
            #print "ts: ", target_speed

            rotation_speed = -self.approach_pid.update(err=yaw_err)

            if abs(rotation_speed) > abs(self.node_params['approach_max_turn_rate']):
                rotation_speed = self.node_params['approach_max_turn_rate'] * np.sign(rotation_speed)

            cat_left, cat_right = self.calculate_cat_velocities(target_speed, rotation_speed)

        # Формируем и публикуем сообщение угловых скоростей
        self.publish_angular_velocity(cat_left=cat_left, cat_right=cat_right)

    def pub_debug_topics(self, rotation_speed, correction_angle, yaw_sp, yaw):
        u"""
        Публикует параметры работы для отладки
        """
        self.rot_speed_pub.publish(FloatStamped(header=Header(stamp=rospy.get_rostime()),
                                                value=rotation_speed))

        self.correction_ang_pub.publish(FloatStamped(header=Header(stamp=rospy.get_rostime()),
                                                     value=correction_angle))

        self.yaw_sp_pub.publish(FloatStamped(header=Header(stamp=rospy.get_rostime()),
                                             value=yaw_sp))

        self.yaw_pub.publish(FloatStamped(header=Header(stamp=rospy.get_rostime()),
                                          value=yaw))

        msg = PoseStamped()
        msg.header.stamp = rospy.get_rostime()
        msg.header.frame_id = 'map'
        msg.pose.position.x = self.state.position.x
        msg.pose.position.y = self.state.position.y
        x, y, z, w = quaternion_from_euler(0, 0, yaw_sp)
        msg.pose.orientation.x = x
        msg.pose.orientation.y = y
        msg.pose.orientation.z = z
        msg.pose.orientation.w = w
        self.debug_tyaw_pub.publish(msg)

    @staticmethod
    def find_route_uncut_len(route, pose):
        u"""
        Находит длину уже пройденной, но еще не обрезанной планером траектории.

        :param route: Траектория MoverRoute
        :param pose: Текущее положение машины State
        """
        min_len_to_segment = float('inf')
        closest_point_idx = 0

        for idx, point in enumerate(route.points[:-1]):
            lts = len_to_segment(pose, point, route.points[idx + 1])
            if lts <= min_len_to_segment:
                min_len_to_segment = lts
                closest_point_idx = idx
        uncut_len = route_length(route, 0, closest_point_idx)

        return uncut_len

    def stop_ctrl(self):
        self.publish_angular_velocity(cat_left=0, cat_right=0)

    def publish_angular_velocity(self, cat_left, cat_right):
        msg = CatsStamped()
        msg.header.stamp = rospy.Time.now()
        msg.cat_left = cat_left
        msg.cat_right = cat_right

        self.angular_velocity_pub.publish(msg)

    def get_max_speed(self, turn_rate):
        return self.max_cat_speed - abs(max(self.calculate_cat_velocities(0, turn_rate)))

    def calculate_cat_velocities(self, target_speed, rotation_speed):
        velocity_l = target_speed - rotation_speed * self.distance_between_tracks / 2
        velocity_r = target_speed + rotation_speed * self.distance_between_tracks / 2

        return velocity_l, velocity_r

    def handle_internal_error(self, error_message=None, error_type=None, event=''):
        is_warning = error_type in self.global_params['Reports']['Warnings'].values()
        loglevel = 2 if is_warning else 3
        self.publish_alert_once(error_type, error_message, loglevel, event=event)

        # Останавливаем работу, обнуляя скорости, если тип ошибки - error
        if not is_warning:
            self.stop_ctrl()

    def planned_route_callback(self, message):
        with self.route_lock:
            self.route.from_msg(message)

    def state_callback(self, message):
        with self.yaw_lock:
            self.state = message
        self.state_updated = True

    def permission_callback(self, message):
        self.move_permission = message.permission
        if self.move_permission:
            self.allow_log_perm_err = True

    def system_status_callback(self, message):
        u"""
        Обрабатывает сообщение о статусе системы от Супервизора.
        Если пришло сообщение об ошибке - останавливаем работу,
        обнуляя скорости.
        """
        pass


class PID:
    def __init__(self, get_time, i_saturation, p=0, i=0, d=0, is_angular=False):
        self.get_time = get_time

        self.proportional_factor = p
        self.integral_factor = i
        self.derivative_factor = d

        self.previous_time = None
        self.previous_error = None
        self.integral = 0

        self.i_saturation = i_saturation

        self.sp = None
        self.feedback = None
        self.is_angular = is_angular

    def _get_time(self):
        return self.get_time

    def update(self, sp=0, feedback=0, err=None):
        if self.previous_time is None:
            self.previous_time = self.get_time()
            return 0

        time_delta = self.get_time() - self.previous_time

        if err is None:
            if self.is_angular:
                error = norm_angle_minusPI_plusPI(sp - feedback)
            else:
                error = sp - feedback
        else:
            error = err

        proportional = error * self.proportional_factor
        self.integral += error * self.integral_factor * time_delta
        if self.previous_error is not None:
            derivative = (error - self.previous_error) * self.derivative_factor / time_delta
        else:
            derivative = 0.0

        self.previous_error = error
        self.previous_time = self.get_time()

        # Оганичиваем значение интегральной составляющий сверху и снизу
        if abs(self.integral) > self.i_saturation:
            self.integral = self.i_saturation * abs(self.integral) / self.integral

        return proportional + self.integral + derivative
