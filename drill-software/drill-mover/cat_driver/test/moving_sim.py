#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import time
import math
from std_msgs.msg import Header
from common_msgs.msg import State
from geometry_msgs.msg import PoseStamped
from tf.transformations import quaternion_from_euler
from drill_msgs.msg import CatsStamped

r = 0.0225
l = 0.225  # растояние между гусеницами

wl = 0
wr = 0
x = 0
y = 0
a = math.pi

ts = time.time()

rospy.init_node("cat_sim")


def control_cb(msg):
    global wl
    global wr
    wr = msg.cat_right
    wl = msg.cat_left

pos_pub = rospy.Publisher("/state", State, queue_size=1)
debug_pos_pub = rospy.Publisher("/debug_drill_pose", PoseStamped, queue_size=1)
rospy.Subscriber("/angular_velocity", CatsStamped, control_cb)
rate = rospy.Rate(10)

while not rospy.is_shutdown():
    vl = wl*r
    vr = wr*r
    dt = rospy.get_rostime().to_sec() - ts
    da = (r/l)*(wr-wl)
    a += da*dt
    x += ((vl+da*l/2) * math.cos(a))*dt
    y += ((vl+da*l/2) * math.sin(a))*dt
    p = PoseStamped()

    p.pose.position.x = x
    p.pose.position.y = y

    _x, _y, _z, _w = quaternion_from_euler(0, 0, a)
    p.pose.orientation.x = _x
    p.pose.orientation.y = _y
    p.pose.orientation.z = _z
    p.pose.orientation.w = _w
    p.header.stamp = rospy.get_rostime()
    p.header.frame_id = "map"

    debug_pos_pub.publish(p)

    s = State()
    s.position.x = x
    s.position.y = y
    s.yaw = a
    s.header = Header(stamp=rospy.get_rostime())

    pos_pub.publish(s)

    ts = time.time()
    rate.sleep()
