#!/usr/bin/python
# -*- coding: utf-8 -*-

import rospy

from drill_msgs.msg import CatsStamped
from common_msgs.msg import PlannedRoute, RoutePoint, VehiclePosition
from geometry_msgs.msg import Point

def angular_velocity_callback(msg):
	rospy.loginfo("angular_velocity_l: %f angular_velocity_r: %f" % (msg.cat_left, msg.cat_right))

def test_cat_driver():

	route_pub = rospy.Publisher('planned_route', PlannedRoute, queue_size=10)
	pose_pub = rospy.Publisher('drill_pose', VehiclePosition, queue_size=10)

	# Sub for checking if CatDriverNode publishes angular_velocities in response to the test
	rospy.Subscriber('angular_velocity', CatsStamped, angular_velocity_callback)

	rospy.init_node('cat_driver_test', anonymous=True)

	point_1 = RoutePoint(target_speed=1, local_pose=Point(x=0, y=0))
	point_2 = RoutePoint(target_speed=1, local_pose=Point(x=8, y=-5))

	route_msg = PlannedRoute(direction='forward', points=[point_1, point_2])
	pose_msg = VehiclePosition(x=1, y=1, yaw=90)

	route_pub.publish(route_msg)
	#pose_pub.publish(pose_msg)

	rospy.spin()

if __name__=="__main__":
	test_cat_driver()
