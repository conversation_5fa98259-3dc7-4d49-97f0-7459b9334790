#!/usr/bin/env python
# -*- coding: utf-8 -*-
from __future__ import division
import rospy
import numpy as np
import json
import math

from common.geometry import norm_angle_minusPI_plusPI
from common.base_node import BaseNode
from std_msgs.msg import String
from common_msgs.msg import MovePermission, MovingError
from drill_msgs.msg import CatsStamped, RemoteType, VehicleBehaviorMode

MOVING = "moving"
IDLE = "idle"
REMOTE = "remote"
SMART = "smart"


class CatRegulatorNode(BaseNode):

    def __init__(self, name):
        super(CatRegulatorNode, self).__init__(name)

        self.move_permission = True

        self.main_mode = IDLE

        self.remote_type = None

        self.fb_updated = False
        self.sp_updated = False

        self.cat_ctrl_pub = rospy.Publisher(self.global_params['topics']['cat_control'],
                                            CatsStamped, queue_size=10)

        self.moving_error_pub = rospy.Publisher(self.global_params['topics']['moving_error'],
                                                MovingError, queue_size=10)

        self.left_pid = PID(
            rospy.get_time,
            self.node_params["left_reg"]["i_saturation"],
            self.node_params["left_reg"]['p'],
            self.node_params["left_reg"]["i"],
            self.node_params["left_reg"]['d'],
            self.node_params["left_reg"]['ff'],
            self.node_params["left_reg"]['min'],
            self.node_params["left_reg"]['max'],
            self.node_params["left_reg"]['out_min'],
            self.node_params["left_reg"]['out_max'],
            self.node_params["left_reg"]['d_term_tc'],
            self.node_params["left_reg"]['out_tc'],
            is_angular=False,
            force_zero=self.node_params["left_reg"]['force_zero']
        )

        self.right_pid = PID(
            rospy.get_time,
            self.node_params["right_reg"]["i_saturation"],
            self.node_params["right_reg"]['p'],
            self.node_params["right_reg"]["i"],
            self.node_params["right_reg"]['d'],
            self.node_params["right_reg"]['ff'],
            self.node_params["right_reg"]['min'],
            self.node_params["right_reg"]['max'],
            self.node_params["right_reg"]['out_min'],
            self.node_params["right_reg"]['out_max'],
            self.node_params["right_reg"]['d_term_tc'],
            self.node_params["right_reg"]['out_tc'],
            is_angular=False,
            force_zero=self.node_params["right_reg"]['force_zero']
        )

        self.last_fb_ts = 0
        self.last_sp_ts = 0
        self.left_sp = 0
        self.right_sp = 0
        self.left_fb = None
        self.right_fb = None
        self.stop_due_to_remote_mode = False
        
    def initialize_params(self):
        self.right_pid.i_saturation = self.node_params["right_reg"]["i_saturation"]
        self.right_pid.p = self.node_params["right_reg"]['p']
        self.right_pid.i = self.node_params["right_reg"]['i']
        self.right_pid.d = self.node_params["right_reg"]['d']
        self.right_pid.ff = self.node_params["right_reg"]['ff']
        self.right_pid.min = self.node_params["right_reg"]['min']
        self.right_pid.max = self.node_params["right_reg"]['max']
        self.right_pid.out_min = self.node_params["right_reg"]['out_min']
        self.right_pid.out_max = self.node_params["right_reg"]['out_max']
        self.right_pid.d_term_tc = self.node_params["right_reg"]['d_term_tc']
        self.right_pid.out_tc = self.node_params["right_reg"]['out_tc']

        self.left_pid.i_saturation = self.node_params["left_reg"]["i_saturation"]
        self.left_pid.p = self.node_params["left_reg"]['p']
        self.left_pid.i = self.node_params["left_reg"]['i']
        self.left_pid.d = self.node_params["left_reg"]['d']
        self.left_pid.ff = self.node_params["left_reg"]['ff']
        self.left_pid.min = self.node_params["left_reg"]['min']
        self.left_pid.max = self.node_params["left_reg"]['max']
        self.left_pid.out_min = self.node_params["left_reg"]['out_min']
        self.left_pid.out_max = self.node_params["left_reg"]['out_max']
        self.left_pid.d_term_tc = self.node_params["left_reg"]['d_term_tc']
        self.left_pid.out_tc = self.node_params["left_reg"]['out_tc']


    def on_start(self):
        rospy.Subscriber(self.global_params['topics']['driver_out'],
                         CatsStamped, self.driver_callback)

        rospy.Subscriber(self.global_params['topics']['cat_feedback'],
                         CatsStamped, self.fb_callback)

        rospy.Subscriber(self.global_params['topics']['main_mode'],
                         VehicleBehaviorMode, self.main_mode_callback)

        rospy.Subscriber(self.global_params['topics']['remote_type'],
                         RemoteType, self.remote_type_callback)

        rospy.Subscriber(self.global_params['topics']['remote_moving'],
                         CatsStamped, self.remote_callback)

    def do_work(self):

        # Если нет режима или состояния мачты или задания - не работаем
        if self.main_mode is None or self.left_fb is None or self.right_fb is None:
            return

        is_error = False

        # Проверяем, что текущий режим позвляет работать, если нет - останавливаем работу
        allowed_modes = (
            MOVING,
        )
        if self.main_mode not in allowed_modes and not self.node_params['disable_mode_check']:
            self.publish_control(0, 0)
            return

        # Проверяем что есть разрешение работать
        if not self.move_permission:
            error_message = "No permission for moving"
            err_num = MovingError().BLOCKED_BY_PERMISSION
            self.handle_moving_error(error_message, err_num=err_num)
            is_error = True

        # Проверяем что последний полученый стейт не слишком старый
        if rospy.Time.now().to_sec() - self.last_fb_ts > self.global_params['MSG_TTL']:
            error_message = "Outdated cat state"
            self.handle_internal_error(error_message)
            is_error = True

        # Проверяем что последний полученый контрол не слишком старый
        if not self.node_params['disable_msg_check'] and rospy.Time.now().to_sec() - self.last_sp_ts > self.global_params['MSG_TTL']:
            error_message = "Outdated cat control"
            self.handle_internal_error(error_message)
            is_error = True

        left = 0
        right = 0

        # Если ошибок нет - работаем
        if not is_error and not self.stop_due_to_remote_mode:
            if self.fb_updated or self.sp_updated:
                left = self.left_pid.update(
                    self.left_sp,
                    self.left_fb
                )
                right = self.right_pid.update(
                    self.right_sp,
                    self.right_fb
                )
                self.fb_updated = False

        self.publish_control(left, right)

    def publish_control(self, left, right):
        message = CatsStamped()
        message.header.stamp = rospy.get_rostime()

        message.cat_left = left
        message.cat_right = right

        self.cat_ctrl_pub.publish(message)

    def handle_internal_error(self, error_message=None, warning=False, err_type=None):
        # Логгируем ошибку, если нужно
        if error_message is not None:
            loglevel = 2 if warning else 3

            if err_type is None:
                warning_num = self.global_params['Reports']['Warnings']['Internal_warning']
                error_num = self.global_params['Reports']['Errors']['Internal_error']
                err_type = warning_num if warning else error_num

            self.publish_alert_once(err_type, error_message, loglevel=loglevel)

    def handle_moving_error(self, error_message, err_num, warning=False):
        loglevel = 2 if warning else 3
        self.log(error_message, loglevel=loglevel)

        moving_error = MovingError()
        moving_error.err_num = err_num

        self.moving_error_pub.publish(moving_error)

    def fb_callback(self, message):
        self.last_fb_ts = message.header.stamp.to_sec()
        self.left_fb = message.cat_left
        self.right_fb = message.cat_right
        self.fb_updated = True
        #print "FB: ", self.left_fb, "  ", self.right_fb

    def driver_callback(self, message):
        if self.main_mode == MOVING or (self.node_params['disable_mode_check'] and self.main_mode != REMOTE):
            self.last_sp_ts = message.header.stamp.to_sec()
            self.left_sp = message.cat_left
            self.right_sp = message.cat_right
            self.stop_due_to_remote_mode = False
            self.sp_updated = True

    def remote_callback(self, message):
        """
        Обрабатываем управление полученное в режиме ДУ.
        """
        if self.main_mode == REMOTE:
            if self.remote_type == SMART:
                self.last_sp_ts = message.header.stamp.to_sec()
                self.left_sp = message.cat_left
                self.left_sp = message.cat_right
                self.sp_updated = True
            else:
                self.last_sp_ts = message.header.stamp.to_sec()
                self.left_sp = 0
                self.left_sp = 0
                self.stop_due_to_remote_mode = True
                self.sp_updated = True

    def main_mode_callback(self, message):
        self.main_mode = message.mode

    def permission_callback(self, message):
        self.move_permission = message.permission

    def remote_type_callback(self, message):
        self.remote_type = message.type


class PID:
    def __init__(self, get_time, i_saturation, p=0, i=0, d=0, ff=0, min=-np.inf, max=np.inf, out_min=-1,
                 out_max=1, d_term_tc=0.0001, out_tc=0.0001, is_angular=False, force_zero=False):
        self.get_time = get_time

        self.proportional_factor = p
        self.integral_factor = i
        self.derivative_factor = d

        self.previous_time = None
        self.previous_error = None
        self.integral = 0

        self.i_saturation = i_saturation
        self.ff = ff

        self.sp = None
        self.feedback = None
        self.is_angular = is_angular
        self.min = min
        self.max = max
        self.out_min = out_min
        self.out_max = out_max
        self.d_old = 0
        self.out_old = 0
        self.d_term_tc = d_term_tc
        self.out_tc = out_tc
        self.force_zero = force_zero


    def _get_time(self):
        return self.get_time

    def update(self, sp, feedback):
        if self.previous_time is None:
            self.previous_time = self.get_time()
            return 0
        cur_time = self.get_time()
        time_delta = cur_time - self.previous_time

        if self.is_angular:
            error = norm_angle_minusPI_plusPI(sp - feedback)
        else:
            error = sp - feedback

        if abs(sp) < 0.005 and self.force_zero:
            self.integral = 0
            self.d_old = 0
            out = 0

        else:
            proportional = error * self.proportional_factor

            self.integral += error * self.integral_factor * time_delta
            d = ((error - self.previous_error) * self.derivative_factor / time_delta) if self.previous_error is not None else 0.0
            derivative = self.d_old + (1-math.exp(-time_delta/self.d_term_tc)) * (d - self.d_old)
            self.d_old = derivative
            # Оганичиваем значение интегральной составляющий сверху и снизу
            if abs(self.integral) > self.i_saturation:
                self.integral = self.i_saturation * abs(self.integral) / self.integral

            #print "P: ", proportional, " I: ", self.integral, " D: ", derivative
            out = proportional + self.integral + derivative + sp * self.ff
            #print "out: ", out

        out_smoothed = self.out_old + (1-math.exp(-time_delta/self.out_tc)) * (out - self.out_old)
        #print "Out smoothed: ", out_smoothed
        if sp >= self.max:
            out_smoothed = self.out_max
        elif sp <= self.min:
            out_smoothed = self.out_min

        if out_smoothed > self.out_max:
            out_smoothed = self.out_max
        elif out_smoothed < self.out_min:
            out_smoothed = self.out_min

        self.previous_error = error
        self.previous_time = cur_time

        self.out_old = out_smoothed

        return out_smoothed
