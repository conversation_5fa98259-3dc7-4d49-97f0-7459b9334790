#!/usr/bin/env python
# -*- coding: utf-8 -*-

import socket
import threading
import json
import rospy
from common.base_node import BaseNode
from drill_msgs.msg import DetectedObject, DetectedObjectArray


class AiConnectorNode(BaseNode):

    def __init__(self, name):
        super(AiConnectorNode, self).__init__(name)
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)  # UDP
        self.sock.bind(('0.0.0.0', self.node_params['port']))
        self.detections_pubs = {}
        for cam in self.node_params['cameras']:
            self.detections_pubs[cam] = rospy.Publisher(rospy.get_param('Global/topics/camera_detections') + '_' + cam,
                                                        DetectedObjectArray, queue_size=10)
        self.lt = threading.Thread(target=self.listen)
        self.lt.start()

    def listen(self):
        while not rospy.is_shutdown():
            data, addr = self.sock.recvfrom(1024)  # buffer size is 1024 bytes
            data = json.loads(data)
            msg = DetectedObjectArray()
            msg.header.stamp = rospy.get_rostime()
            msg.camera = data['camera']
            for obj in data['objects']:
                o = DetectedObject()
                o.object_class = obj['class']
                o.score = obj['score']
                o.box.x = obj['box'][0]
                o.box.y = obj['box'][1]
                o.box.width = obj['box'][2]
                o.box.height = obj['box'][3]
                msg.objects.append(o)
            if data['camera'] in self.detections_pubs:
                self.detections_pubs[data['camera']].publish(msg)
            else:
                self.logwarn("Camera %s is missing in config"%data['camera'], period=5, event='')

    def do_work(self):
        pass

