#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import ros_numpy
import numpy as np
from scipy.spatial import KDTree

from common.base_node import BaseNode
from common_msgs.msg import State, MovePermission
from drill_msgs.msg import FloatStamped, VehicleBehaviorMode, DetectedObjectArray
from std_msgs.msg import Header

from drill_msgs.srv import IgnoreObstacles

from sensor_msgs.msg import PointCloud2
from shapely.geometry import Polygon, Point, MultiPolygon
from shapely import affinity


def make_whole_polygon(veh_params):
    """
    Calculates the vehicle polygon from its parameters

    :param veh_params (dict) - vehicle parameters
    :return veh_polygon (Polygon) - vehicle's polygon
    """

    polygon_shell = []
    for point in veh_params['geometry']['polygon_points']:
        polygon_shell.append((point['x'], point['y']))
    veh_polygon = Polygon(polygon_shell)
    return veh_polygon


class CollisionPreventerNode(BaseNode):

    def __init__(self, name):
        super(CollisionPreventerNode, self).__init__(name)

        self.action = None
        self.state = None
        self.updated = False
        self.permission = True
        self.main_mode = None
        self.allow_ignoring_obstacles = False
        self.end_of_ignoring_obstacles_time = 0
        self.obstacles = np.empty((0, 2))
        self.tailing = np.empty((0, 2))
        self.dangerous_events = 0
        self.danger_timestamp = 0
        self.vehicle_poly = make_whole_polygon(self.vehicle_params)
        self.max_body_dist = self.furthest_dist_to_veh_polygon()
        self.last_obst_msg_timestamp = rospy.Time.now().to_sec()

        self.move_permission_pub = rospy.Publisher(rospy.get_param('Global/topics/permission'),
                                                   MovePermission,
                                                   queue_size=10)
        self.shortest_dist_pub = rospy.Publisher(
            rospy.get_param('Global/topics/obst_shortest_dist'),
            FloatStamped,
            queue_size=10)

    def on_start(self):
        rospy.Subscriber(self.global_params['topics']['state'],
                         State, self.state_callback)

        rospy.Subscriber(self.global_params['topics']['permission'],
                         MovePermission, self.permission_callback)

        rospy.Subscriber(self.global_params['topics']['main_mode'],
                         VehicleBehaviorMode, self.main_mode_callback)

        rospy.Subscriber('/segmented_obstacles', PointCloud2, self.obstacles_callback)

        rospy.Subscriber('/tailing_map', PointCloud2, self.tailing_callback)

        rospy.Subscriber(rospy.get_param('Global/topics/camera_detections')+'_left', DetectedObjectArray, self.camera_callback)
        rospy.Subscriber(rospy.get_param('Global/topics/camera_detections')+'_right', DetectedObjectArray, self.camera_callback)



        self.ignore_obstacles_service = rospy.Service('ignore_obstacles',
                                                      IgnoreObstacles, self.ignoring_obstacles)

    def camera_callback(self, msg):
        if self.main_mode is None or (self.main_mode not in (VehicleBehaviorMode.MOVING, VehicleBehaviorMode.LEVELING, VehicleBehaviorMode.GROUNDING)):
            return
        if rospy.get_time() < self.end_of_ignoring_obstacles_time:
            return
        stop_area = self.node_params['cameras_stop_areas'].get(msg.camera)
        if stop_area is None:
            return
        stop_area = Polygon(stop_area)
        for obj in msg.objects:
            min_score = self.node_params['cameras_stop_classes'].get(obj.object_class)
            if min_score is None or obj.score < min_score:
                continue
            box = Polygon(((obj.box.x, obj.box.y),
                           (obj.box.x+obj.box.width, obj.box.y),
                           (obj.box.x+obj.box.width, obj.box.y+obj.box.height),
                           (obj.box.x, obj.box.y+obj.box.height),
                           )
                          )
            if box.intersects(stop_area):
                self.move_permission_pub.publish(
                    MovePermission(permission=False, source=self._name))
                self.log('%s detected by %s camera with confidence %s in danger zone!, STOP MOVING!' % (obj.object_class, msg.camera, obj.score),
                         event=self.global_params['events']['collision_preventer_stoppage'],
                         loglevel=2
                         )



    def state_callback(self, message):
        self.state = message
        self.updated = True

    def obstacles_callback(self, msg):
        self.last_obst_msg_timestamp = msg.header.stamp.to_sec()
        if self.check_msg_ttl(msg):
            if msg.header.frame_id == 'map':
                self.obstacles = ros_numpy.point_cloud2.pointcloud2_to_xyz_array(msg)
                self.updated = True
            else:
                self.log('Wrong frame id in obstacles data!', loglevel=3)
        else:
            self.log('check_msg_ttl failed in obstacles_callback!', loglevel=3)

    def tailing_callback(self, msg):
        if self.check_msg_ttl(msg):
            if msg.header.frame_id == 'map':
                self.tailing = ros_numpy.point_cloud2.pointcloud2_to_xyz_array(msg)
            else:
                self.log('Wrong frame id in obstacles data!', loglevel=3)

    def exclude_tailing_points(self, obstacles, tailing):
        """
        Excludes obstacle points which are too close to a tailing

        :param obstacles (tuple) - obstacles' points
        :param tailing (tuple) - tailing's points
        """
        tailing_array = np.array(tailing)
        tailing_array = tailing_array.transpose()[:2].transpose()
        if len(tailing_array) < 1:
            return obstacles

        kd_tree = KDTree(tailing_array)

        n = 0
        res = []
        for o in obstacles:
            idx = kd_tree.query_ball_point((o[0], o[1]), self.node_params['tailing_include_dist'])
            if not len(idx):
                res.append(o)

        return res

    def furthest_dist_to_veh_polygon(self):
        """
        Calculates how far is the furthest point of the vehicle's polygon from state position

        :return furthest_dist (float): furthest distance from state position to the vehicle's polygon
        """
        furthest_dist = 0
        for point in self.vehicle_params['geometry']['polygon_points']:
            dist = np.hypot(point['x'], point['y'])
            furthest_dist = max(furthest_dist, dist)
        return furthest_dist

    def exclude_further_obstacles_points(self, obstacles):
        """
        Excludes obstacle points which are too far from state position

        :param obstacles (tuple) - obstacles' points
        :return filtered_obstacles (егзду): given polygon placed into the given position
        """

        # TODO: use do_transform_cloud
        # import sensor_msgs.point_cloud2 as pc2
        # from sensor_msgs.msg import PointCloud2
        # from tf2_sensor_msgs.tf2_sensor_msgs import do_transform_cloud
        # import tf2_ros
        #
        # tf_buffer = tf2_ros.Buffer()
        # tf_listener = tf2_ros.TransformListener(tf_buffer)
        # transform = tf_buffer.lookup_transform("camera_link", "map", rospy.Time())
        # pcd2_camera = do_transform_cloud(pcd2, transform)

        points_array = np.array(obstacles).transpose()[:2].transpose()
        if len(points_array) < 1:
            return points_array

        kd_tree = KDTree(points_array)
        include_distance = self.node_params['safety_dist'] + self.max_body_dist
        included_points_indexes = kd_tree.query_ball_point((self.state.position.x, self.state.position.y), include_distance)
        filtered_obstacles = np.empty((0, 2))

        arr = []
        for idx in included_points_indexes:
            # arr.append(obstacles[idx])
            arr.append(points_array[idx])
            # np.append(filtered_obstacles, obstacles[idx])

        filtered_obstacles = np.array(arr)
        return filtered_obstacles

    def is_close_veh_polygon(self, obsty_points, polygon, safety_dist):
        """
        Calculates how far are all obstacles from the veh
        Finds the shortest distance of the closest point in obstacles' point cloud

        :param obsty_points (tuple) - obstacles' points
        :param polygon (Polygon) - vehicle's polygon
        """
        dangerous_points = 0
        shortest_dist = 1000

        for point in obsty_points:
            sp_point = Point(point[0], point[1])
            dist = sp_point.distance(polygon)
            shortest_dist = min(dist, shortest_dist)
            if dist <= safety_dist:
                dangerous_points += 1
                if dangerous_points >= self.node_params['min_dangerous_points']:
                    self.danger_timestamp = rospy.get_time()
                    self.dangerous_events += 1
                    break

        return shortest_dist

    def get_positioned_polygon(self, veh_polygon, state):
        """
        Places the given polygon into the given position

        :param veh_polygon (Polygon) - vehicle's polygon
        :param state (State) - given position
        :return positioned_polygon (Polygon): given polygon placed into the given position
        """

        rotated_polygon = affinity.rotate(
            veh_polygon,
            state.yaw,
            origin=Point(0, 0),
            use_radians=True
        )
        positioned_polygon = affinity.translate(rotated_polygon, state.position.x, state.position.y)
        return positioned_polygon

    def ignoring_obstacles(self, request):
        """
        Ignoring obstacles request handler.

        :param request (drill_msgs.IgnoreObstacles)
        :return success (Bool)
        """

        if request.ignoring_time <= self.node_params['max_ignoring_period']:
            self.end_of_ignoring_obstacles_time = rospy.get_time() + request.ignoring_time
            return True
        else:
            self.log('Ignoring period is too big!', loglevel=3)
            return False

    def do_work(self):
        if self.main_mode is not None and (self.main_mode == VehicleBehaviorMode.MOVING):
                                           # or self.main_mode == VehicleBehaviorMode.LEVELING
                                           # or self.main_mode == VehicleBehaviorMode.GROUNDING):
            if self.end_of_ignoring_obstacles_time > rospy.Time.now().to_sec():
                # rospy.loginfo_throttle(3, "Collision prevent module is off for %0.1f more seconds" %
                #                        (self.end_of_ignoring_obstacles_time - rospy.Time.now().to_sec()))
                self.loginfo("Collision prevent module is temporary off (by request or after permission switch)",
                             period=3)
                pass
            elif rospy.get_time() - self.last_obst_msg_timestamp > self.node_params['obstacle_topic_timeout']:
                self.move_permission_pub.publish(MovePermission(permission=False, source=self._name))
                self.log('No data in segmented obstacles topic, LIDARs or detection node malfunction, STOP MOVING!',
                         event=self.global_params['events']['collision_preventer_stoppage'])
            elif self.updated and self.state is not None:
                positioned_poly = self.get_positioned_polygon(self.vehicle_poly, self.state)
                obstacles = self.exclude_further_obstacles_points(self.obstacles)
                obstacles = self.exclude_tailing_points(obstacles, self.tailing)
                shortest_dist_obst = self.is_close_veh_polygon(obstacles, positioned_poly, self.node_params['safety_dist'])
                # check close to tailing too
                shortest_dist_tailing = self.is_close_veh_polygon(self.tailing, positioned_poly, self.node_params['safety_dist_tailing'])
                shortest_dist = min(shortest_dist_obst, shortest_dist_tailing)
                # rospy.loginfo_throttle(0.5, "shortest_dist obst: %f tailing %f" % (shortest_dist_obst, shortest_dist_tailing))
                self.shortest_dist_pub.publish(FloatStamped(header=Header(stamp=rospy.get_rostime()),
                                                            value=shortest_dist))
                if rospy.get_time() - self.danger_timestamp < self.node_params['danger_timeout']:
                    if self.dangerous_events > self.node_params['min_dangerous_events'] and self.permission:
                        self.move_permission_pub.publish(
                            MovePermission(permission=False, source=self._name))
                        self.log('Dangerous obstacles detection, STOP MOVING! '
                                 'Shortest distance to obstacle %0.1f, to tailing %0.1f ' % (shortest_dist_obst,
                                                                                             shortest_dist_tailing),
                                 event=self.global_params['events']['collision_preventer_stoppage']
                        )

                else:
                    self.dangerous_events = 0
                self.updated = False

    def on_stop(self):
        pass

    def permission_callback(self, msg):
        cur_perm = self.permission
        if not cur_perm and msg.permission:
            request = IgnoreObstacles()
            request.ignoring_time = self.node_params['ignoring_period']
            self.ignoring_obstacles(request)

        self.permission = msg.permission

    def main_mode_callback(self, msg):
        self.main_mode = msg.mode
