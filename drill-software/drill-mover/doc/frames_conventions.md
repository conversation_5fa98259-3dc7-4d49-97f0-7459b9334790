# Системы координат

Используем системы координат ROS, в соответствии с [REP-103 "Standard Units of Measure and Coordinate Conventions"](https://www.ros.org/reps/rep-0103.html):
* Земная локальная система координат ENU (east-north-up);
* связанная система координат FLU (forward-left-up):
  - x - вперед
  - y - влево
  - z - вверх

Используются правосторонние системы координат, соответствуют правилу правой руки.

![](https://upload.wikimedia.org/wikipedia/commons/thumb/d/d2/Right_hand_rule_cross_product.svg/220px-Right_hand_rule_cross_product.svg.png)

# Направления углов roll, pitch, yaw
Положительные направления углов roll, pitch, yaw определяются правилом буравчика. 

![](https://upload.wikimedia.org/wikipedia/commons/thumb/3/34/Right-hand_grip_rule.svg/170px-Right-hand_grip_rule.svg.png)

Смотрим на ось сзади и вращаем буравчик по часовой:
* roll положительный при наклоне на правый бок;
* pitch положительный при наклоне носом вниз
* yaw против часовой (если смотреть сверху).

![](img/pitch_positive_down.png)

![](img/roll_positive_right.png)

На рисунках представлены визуально наклоны станка в положительную сторону по тангажу и крену и правильные измерения углов (в топике).

# Оси и направления датчиков

Для измерения углов используются 3 типа датчиков:
* инклинометр IS1BP360-C-CL, 1 ось, для определения угла наклона мачты;
* инклинометр IS2BP090-C-CL, 2 оси, для определения угла наклона платформы (кабина);
* IMU OpenIMU300RI на мачте и впереди и сзади совместно с лидарами.

## Инклинометры IS1BP360-C-CL (1 ось) и IS2BP090-C-CL (2 оси)

Measurement axes orientation - IS1BP360-U-CL + IS2BP090-U-CL big plastic housing (factory default settings):

![](img/gemac_incs.png)

## IMU OpenIMU300RI

The following diagram shows the default coordinate frame for the OpenIMU300RI (The coordinate frame can be changed using a UART or CAN message).

![](img/OpenIMU300RI_CoordinateFrame.png)

## Станок в Перу

IMU и 1-осевой инклинометр на мачте (фото с левого борта, перед на фото слева): 

![](img/IMU_INC_mast.drawio.jpg)

### IMU (в шкафу, на ней надпись 130): 
* крен имеет положительное направление на правый бок (как и надо), 
* тангаж надо инвертировать (чтоб угол вниз носом был с плюсом).

В nodes.yaml для CanNode output_topics есть такое 
```json
    tower_imu_data:
      msg_type: "drill_msgs/ImuData"
      period: 5
      rate: 10
      fields:
        roll:
          io: "tower_imu_pitch"
        pitch:
          io: "tower_imu_roll"
```
Вероятно, менять местами крен и тангаж тут не нужно.

### Инклинометр мачты (1 ось):
* измеряет тангаж;
* от его измерений надо отнять 90 градусов 
(т.к. повёрнут относительно своего нулевого положения в положительном направлении на 90 градусов);
* после этого угол надо инвертировать, т.к. у нас тангаж в другую сторону.

### Инклинометр палубы/кабины (2 оси)

![](img/INC_deck.drawio.jpg)

* angle_x &ndash; это крен;
* angle_y &ndash; тангаж;
* направления совпадают.

Поэтому в state_tracker2 `'swap_rp': False` (изменено с `True`)
```json
      - { 'module': 'processors', 'class': 'RPAlign', 'params': {'name': 'inc_roll_pitch_align', 'swap_rp': False, 'roll_offset': '$roll_offset', 'pitch_offset': '$roll_offset'}}
```

### IMU лидаров

Судя по bag-у, перевернуты.

### Видео в симуляторе
Tower IMU:
* OpenIMU Directions Sim https://youtu.be/C7-3YGRNPrw
* OpenIMU300 Directions with Drill (/tower_imu_data) Sim https://youtu.be/9LA2yUFUI0U

Tower INC:
* Gemac 1-axis Inc Directions with Drill (/tower_inc_angle) Sim https://youtu.be/7AEqAixi0BA
* Gemac 1-axis Inc Directions Sim https://youtu.be/VHpmLV9n6rg

Board INC:
* Gemac 2-axes Inc Directions Sim https://youtu.be/ZkkD90Ne9fg
* Gemac 2-axes Inc Directions with Drill (/board_inc_angles & /platform_level) Sim https://youtu.be/ZIyLxcHBu7I


# State tracker 
## tower_state