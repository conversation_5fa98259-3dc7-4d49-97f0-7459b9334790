<launch>
  <group>
    <include file="$(find can_node)/launch/run.launch"/>
    <include file="$(find can_node)/launch/engine_controller.launch"/>
    <include file="$(find can_node)/launch/robo_mode_ctrl.launch"/>
    <include file="$(find qarl_trimble)/launch/normal.launch"/>
    <include file="$(find state_tracker2)/launch/run.launch"/>
    <include file="$(find nmttcan_interface)/launch/run.launch"/>
    <include file="$(find modbus_node)/launch/run.launch"/>
    <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch"/>
    <!--include file="$(find drill_launch_pack)/launch/lidar_sensors.launch"/-->
    <node name="SensorsPubNode" pkg="leveler" type="sensors_publisher2.py" respawn="true" respawn_delay="5" output="screen"/>
  </group>
</launch>

