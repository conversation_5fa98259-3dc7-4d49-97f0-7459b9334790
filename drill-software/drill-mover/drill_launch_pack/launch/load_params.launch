<launch>
    <rosparam file="$(find drill_launch_pack)/params/drill/global.yaml" command="load" ns='Global'/>
    <rosparam file="$(find drill_launch_pack)/params/drill/vehicle.yaml" command="load" ns='Vehicle'/>
    <rosparam file="$(find drill_launch_pack)/params/drill/nodes.yaml" command="load"/>
    <rosparam file="$(find drill_launch_pack)/params/drill/drilling_profiles.yaml" command="load"/>

    <!-- Set params_dump_file parameter -->
    <param name="/load_params_with_create/params_dump_file" value="$(find drill_launch_pack)/params/drill/params_dump.yaml"/>
    <!-- Node loads params if sets params from dump file if exists or create empty dump file -->
    <node name="load_params_with_create" pkg="drill_launch_pack" type="load_params_with_create.py" output="screen"/>
</launch>