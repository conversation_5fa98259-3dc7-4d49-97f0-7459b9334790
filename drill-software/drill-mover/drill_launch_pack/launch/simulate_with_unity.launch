<launch>



  <group>

    <include file="$(find drill_launch_pack)/launch/load_params.launch"/>
    <!--     update Unity-sim specific params -->
    <rosparam file="$(find drill_launch_pack)/params/drill/unity_sim.yaml" command="load"/>

    <include file="$(find visualizer)/launch/normal.launch"/>

<!--drill-moover-->
    <include file="$(find main_state_machine)/launch/run.launch"/>
<!--     <include file="$(find tower_controller)/launch/run.launch"/> -->
    <include file="$(find arm_controller)/launch/run.launch"/>
    <include file="$(find fork_controller)/launch/run.launch"/> 
    <include file="$(find dust_flaps_controller)/launch/run.launch"/>
    <include file="$(find carousel_controller)/launch/run.launch"/>
    <include file="$(find wrench_controller)/launch/run.launch"/>
    <include file="$(find cat_regulator)/launch/run.launch"/>
    <include file="$(find cat_driver)/launch/run.launch"/>
    <include file="$(find planner)/launch/run.launch"/>
    <include file="$(find leveler)/launch/run.launch"/>
    <include file="$(find driller)/launch/run.launch"/>
    <include file="$(find drill_regulator)/launch/run.launch"/>
    <include file="$(find drill_supervisor)/launch/run.launch"/>
    <include file="$(find hal_client)/launch/run.launch"/>
    <include file="$(find rod_changer)/launch/run.launch"/>
<!--    <include file="$(find collision_preventer)/launch/ai_connector_run.launch"/>-->
    <include file="$(find vehicle_supervisor)/launch/run.launch"/>

<!--not inside drill-moover for some reason-->
    <include file="$(find remote_connector)/launch/run.launch"/>

<!-- drill-frame without low-level drivers (nmttcan, lidar_sensors) and modbus node -->
    <include file="$(find can_node)/launch/run.launch"/>
    <include file="$(find can_node)/launch/robo_mode_ctrl.launch"/>
    <include file="$(find can_node)/launch/engine_controller.launch"/>
    <include file="$(find qarl_trimble)/launch/normal.launch"/>
    <include file="$(find state_tracker2)/launch/run.launch"/>
<!--     <node name="SensorsPubNode" pkg="leveler" type="sensors_publisher2.py" respawn="true" respawn_delay="5" output="screen"/> -->

<!--robomode selector simple (topic-level) simulation-->
    <node name="RoboModeSelectorSimulatorNode" pkg="simulator" type="simulate_robomode_selector.py" respawn="true" respawn_delay="5" output="screen"/>
    <node name="EmergencySimulatorNode" pkg="simulator" type="simulate_emergency_state.py" respawn="true" respawn_delay="5" output="screen"/>


<!--   Remap python simulator topics, which are replaced with new sim configuration -->
<!--    <remap from="/state" to="/state_oldsim"/>-->
<!--    <remap from="/driver_out" to="/driver_out_oldsim"/>-->
<!--    <remap from="/SC/orientation" to="/SC/orientation_oldsim"/>-->
<!--    <remap from="/SC/trimble_nmea_gga" to="/SC/trimble_nmea_gga_oldsim"/>-->
<!--    <remap from="/st/cat_fb/SC/trimble_nmea_gga" to="/SC/trimble_nmea_gga_oldsim"/>-->
<!--    <remap from="/st/cat_fb" to="/st/cat_fb_oldsim"/>-->
<!--    <remap from="/jacks_state" to="/jacks_state_oldsim"/>-->
<!--    <remap from="/platform_level" to="/platform_level_oldsim"/>-->
<!--    <remap from="/tower_state" to="/tower_state_oldsim"/>-->
<!--    <include file="$(find simulator)/launch/run.launch"/>-->

<!--    <node pkg="tf" type="static_transform_publisher" name="lidar_front_broadcaster" args="6.08 -0.37 4.94 0 0 0 world lidar_front 100" />-->
<!--    <node pkg="tf" type="static_transform_publisher" name="lidar_back_broadcaster" args="-5.65 -0.51 5.8 180 0 0 world lidar_back 100" />-->

  </group>

</launch>
