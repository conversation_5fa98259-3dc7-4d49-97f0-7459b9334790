# Настройки параметров предприятия
enterprise:
  location:
    # Hudbay random point
    base_point:
        lon: -71.772348
        lat: -14.465727
    # Нулевая точка на карте предприятия
#    ref_point_1:
#      local_x: 0.0
#      local_y: 0.0
#      lon: -71.772274
#      lat: -14.465823
    # Точка на оси ОХ, хотя наверное это может быть любая точка
#    ref_point_2:
#      local_x: 405.0
#      local_y: 0.0
#      lon: -71.772055
#      lat: -14.465816
    # Точка на оси ОУ, хотя наверное это тоже может быть любая точка
#    ref_point_3:
#      local_x: 0.0
#      local_y: -319.0
#      lon: -71.772265
#      lat: -14.465952

    utm_zone: '19'

# Настройки топиков
topics:

  arm_action: 'arm_action'
  arm_control: 'arm_control'
  arm_control_direct: 'remote_arm_control'
  arm_controller_status: 'ArmControllerNodeStatus'
  arm_switch_state: 'arm_switch_state'

  carousel_control: 'carousel_control'
  carousel_controller_status: 'CarouselControllerNodeStatus'
  carousel_action: 'carousel_action'
  carousel_switch_state: 'carousel_switch_state'
  carousel_control_direct: 'remote_carousel_control'

  cat_control_direct: 'remote_cats_control'
  cat_control_smart: 'cat_control_smart'
  cat_control: 'cats_control'
  cat_feedback: '/st/cat_fb'

  compressor_control: 'compressor_control'
  counter_data: "counter_data"

  drill_control_direct: 'remote_driller_actuator'
  drill_control_smart: 'smart_drill_control'
  drill_state: 'drill_state'
  drill_actuator: 'drill_actuator'
  drill_angle: "drill_angle"
  drill_rpm: "drill_rpm"
  driller_action: 'driller_action'
  driller_status: 'DrillerNodeStatus'
  driller_out: 'driller_out'
  rmo_rock_type: 'rmo_rock_type'
  rmo_hole_water: 'rmo_hole_water'
  drill_depth_info: 'drill_depth_info'

  drive_drill_mode: 'drill_propel_mode'
  remote_drill_propel_mode: 'remote_drill_propel_mode'
  driver_out: 'driver_out'
  dust_flaps_control: 'dust_flaps_control'
  dust_flaps_switch_state: 'dust_flaps_switch_state'
  dust_flaps_action: 'dust_flaps_action'
  dust_flaps_status: 'DustFlapsControllerNodeStatus'
  camera_detections: 'camera_detections'

  emergency_status: 'emergency_status'
  emergency_control: 'emergency_control'
  event: 'event'
  engine_ctrl: 'engine_ctrl'

  fork_state: 'fork_state'
  fork_control: 'fork_control'
  fork_controller_status: 'ForkControlNodeStatus'
  fork_control_direct: 'remote_fork_control'
  fork_switch_state: 'fork_switch_state'
  fork_length_data: 'fork_length_data'
  fork_action: 'fork_action'

  internal_error: 'internal_error'
  internal_report: 'internal_report'

  jack_rangefinder_data: 'jack_rangefinder_data'
  jacks_control_speed: 'jacks_control_speed'
  jacks_state: 'jacks_state'
  jacks_control: 'jacks_control'
  leveler_status: 'LevelerNodeStatus'
  platform_level: 'platform_level'
  kobus_platform_level: 'platform_level'
  kobus_rotation_speed: 'kobus_rotation_speed'
  kobus_state: 'kobus_state'

  lamp_control: "lamp_control"
  laser_data: "laser_data"
  last_finished_holeid: 'last_finished_holeid'
  last_main_mode: 'last_main_mode'
  leveler_control_direct: 'remote_jacks_control'
  locker_out: 'locker_out'

  main_action: 'main_action'
  main_mode: 'main_mode'
  manual_state: 'manual_state'
  moving_error: 'moving_error'

  planned_routes: 'planned_routes'
  planned_route: 'planned_route'
  planner_action: 'planner_action'
  planner_status: 'PlannerNodeStatus'
  planner_stoppage: 'planner_stoppage'
  permission: 'permission'
  pressure_state: 'pressure_state'
  progress_topic: 'progress_topic'
  pwm_enabled: 'pwm_enabled'
  bags_save_cmd: 'bags_save_cmd'

  restrictions_mode: 'restrictions_mode'
#  restrictions_mode_rmo: 'restrictions_mode_rmo'

  state: 'state'
  speed_ctrl_stopped: 'speed_ctrl_stopped'
  shaft_action: 'shaft_action'
  shaft_status: 'shaft_status'
  system_status: 'system_status'

  tower_action: 'tower_action'
  tower_control: 'tower_control'
  tower_state: 'tower_state'
  tower_controller_status: 'TowerControllerNodeStatus'
  tower_control_direct: 'remote_tower_control'
  tower_switch_state: 'tower_switch_state'

  vibration_state: 'vibration_state'

  relative_depth: 'relative_depth'
  remote_drilling: 'remote_driller_actuator'
  remote_moving: 'remote_moving'
  restr_off_mode_remaining_time: 'restr_off_mode_remaining_time'
  rod_changer_out: 'rod_changer_out'

  compressor_control_direct: 'remote_compressor_control'

  dust_collector_control_direct: 'remote_dust_collector_control'
  dust_collector_control: 'dust_collector_control'

  main_sm_drill_out: 'main_sm_drill_out'
  main_state_machine_status: 'MainStateMachineNodeStatus'

  hal_permission: 'hal_permission'

  remote_type: 'remote_type'
  rod_changer_status: "RodChangerNodeStatus"
  rod_changer_action: "rod_changer_action"

  wrench_state: "wrench_switch_state"
  wrench_control: "wrench_control"
  wrench_control_direct: "remote_wrench_control"
  wrench_controller_status: "WrenchControllerNodeStatus"
  wrench_controller_action: "wrench_controller_action"

  water_injection_auto: "water_injection_auto"

  lights_control_direct: "lights_control_direct"

  rcs_faults: "rcs_faults"

  trimble_position: '/SC/position'
  trimble_orientation: '/SC/orientation'
  trimble_quality: '/SC/quality'
  trimble_speed: '/SC/speed'
  trimble_nmea_gga: '/SC/trimble_nmea_gga'

  obst_shortest_dist: 'obst_shortest_dist'

  rmo_robomode_sp: '/rmo_robomode_sp'
  current_robomode: '/current_robomode'
  selector_robomode_sp: '/selector_robomode_sp'
  selector_robomode_fb: '/selector_robomode_fb'
  shaft_counter: 'shaft_counter'
  need_shaft_reset: 'need_shaft_reset'
  water_injection_control: 'water_injection_control'


# Общая погрешность
EPS: 0.000001
# Допустимая задержка входных данных нод секунды
MSG_TTL: 0.5

# Допустимое ожидание нового стейта для возвращения из failure секунды
new_state_TTL: 3

# Настройки отчетов состояния нод и системы
Reports:
  Not_working:
    Initialization_exception: 100
    Runtime_exception: 101
  Info:
    Working: 200
    Initializing: 201
    Shutting down: 202
    Shut_down: 203
    Starting: 204
    Started: 205
  Warnings:
    Internal_warning: 401
    Connecting: 402
    Reconnecting: 402
    No_incoming_data: 403
  Errors:
    Internal_error: 500
    Initialization_failed: 501
    Connection_failed: 502
    Incoming_data_delay: 503
    Report_delay: 504
  Critical:
    Action_failed: 600
    Hardware_malfunction: 601
    Bad_conditions: 602
    Tilt_too_high: 603
  Emergency:
    pwmbox_failed: 700


Timeouts:
  timeout_obsolescence: 0.5
  timeout_not_shoving_up: 2


SYSTEM_STATUS:
  WARNING: 400
  ERROR: 401
  CRITICAL: 402


system_flags:
  arm_present: True
  arm_grip_sensor_present: False  # set False according to bad sensor
  fork_present: True
  use_rotation_speed_sensor: True
  can_drill_inclined: False
  dust_flaps_present: True

# Классы событий, к которым будет принадлежать каждое конкретное лог-сообщение.
# В коментариях уточняется, в каких случаях используется каждое событие.
events:
  # Пришла команда, которая уже была исполнена ранее
  # (открыть уже открытый люнет; перейти в ДУ когда уже в нем и т.п.)
  already_done: "ALREADY_DONE"
  # Задание/команда не принято т.к. машина еще выполняет предыдущее.
  wait_finish: "WAIT_FINISH"
  # Потеря связи с HAL-сервером
  lost_hal_conn: "LOST_HAL_CONN"
  # Робот не смог перейти в режим ДУ
  # (т.к. еще не остановился или не получил данные о состоянии машины)
  can_not_switch_to_rc: "CAN_NOT_SWITCH_TO_RC"
  depth_correction_failed: 'DEPTH_CORRECTION_FAILED'
  uneven_terrain: "UNEVEN_TERRAIN"

  # Следующие события отправляются, когда робот не смог выполнить действие сам и
  # нужно сделать это за него в режиме ДУ:

# Нужно повернуть карусель в указанном направлении
  rc_rotate_carousel_idx1: "RC_ROTATE_CAROUSEL_IDX1"
  rc_rotate_carousel_idx2: "RC_ROTATE_CAROUSEL_IDX2"
  # Нужно закрыть карусель
  rc_close_carousel: "RC_CLOSE_CAROUSEL"
  # Нужно открыть карусель
  rc_open_carousel: "RC_OPEN_CAROUSEL"
  # Нужно открыть ключ
  rc_open_arm: "RC_OPEN_ARM"
  # Нужно закрыть ключ
  rc_close_arm: "RC_CLOSE_ARM"
  # Нужно открыть вилку
  rc_open_fork: "RC_OPEN_FORK"
  # Нужно закрыть вилку
  rc_close_fork: "RC_CLOSE_FORK"
  # нужно открыть фартук
  rc_open_dust_flaps: "RC_OPEN_DUST_FLAPS"
  # нужно закрыть фартук
  rc_close_dust_flaps: "RC_CLOSE_DUST_FLAPS"
  # Нужно доехать до целевой точки
  rc_move: "RC_MOVE"
  # Нужно освободить бур из застревания
  rc_unstuck: "RC_UNSTUCK"
  # Нужно отдомкратиться
  rc_leveling: "RC_LEVELING"
  # Нужно отдомкратиться выше
  rc_level_too_low: "RC_LEVEL_TOO_LOW"
  # Нужно поднять (просевший) став
  rc_lift_string: "RC_LIFT_STRING"
  rc_string_too_high: "RC_STRING_TOO_HIGH"
  # Нужно нарастить штангу
  rc_rode_change: "RC_RODE_CHANGE"
  # Нужно заблокировать мачту
  rc_lock_tower: "RC_LOCK_TOWER"
  # Нужно разблокировать мачту
  rc_unlock_tower: "RC_UNLOCK_TOWER"
  # Нужно отрегулировать мачту
  rc_tower_tilt: "RC_TOWER_TILT"
  # Нужно выполнить выравнивание штанги по лыскам
  rc_align_fork: "RC_ALIGN_FORK"
  # Нужно повернуть вилку по часовой стрелке
  rc_turn_cw: "RC_TURN_CW"
  # Нужно повернуть вилку против часовой стрелки
  rc_turn_ccw: "RC_TURN_CCW"
  # Нужно закрыть ключ
  rc_close_wrench: "RC_CLOSE_WRENCH"
  # Нужно повернуть ключ
  rc_turn_wrench: "RC_TURN_WRENCH"
  # Нужно открыть ключ
  rc_open_wrench: "RC_OPEN_WRENCH"
  # Нужно отвернуть ключ
  rc_release_wrench: "RC_RELEASE_WRENCH"
  # Нужно выполнить откручивание
  rc_detach: "RC_DETACH"
  # Нужно поднять вращатель выше карусели
  rc_lift_to_carousel: "RC_LIFT_TO_CAROUSEL"
  # Нужно выполнить накручивание вращателя на штангу
  rc_screwing: "RC_SCREWING"
  # Нужно вытащить штангу из стакана
  rc_pull_out: "RC_PULL_OUT"
  #Нужно выровнять штангу после разборки
  rc_final_position: "RC_FINAL_POSITION"
  # Нужно выполнить страгивание резьбы
  rc_brakeout: "RC_BRAKEOUT"
  # Нужно подвести штангу к стакану карусели
  rc_approach_carousel: "RC_APPROACH_CAROUSEL"
  # Нужно выровнять штангу к стакану
  rc_align_cup: "RC_ALIGN_CUP"
  # Нужно вставить штангу в стакан
  rc_insert: "RC_INSERT"
  # Нужно повернуть штангу в стакане по часовой стрелке
  rc_turn_cw_cup: "RC_TURN_CW_CUP"
  # Нужно подвести вращатель к вилке
  rc_approach_fork: "RC_APPROACH_FORK"
  # Нужно выполнить накручивание новой штанги
  rc_new_rod_screwing: "RC_NEW_ROD_SCREWING"
  # Нужно открыть вилку и люнет
  rc_open_forkarm: "RC_OPEN_FORKARM"
  # Нужно открутить вращатель
  rc_detach_cup: "RC_DETACH_CUP"


  # События сообщающие о неисправности железа:

  # Неисправность компрессора
  compressor_failure: "COMPRESSOR_FAILURE"
  # невалидные показания датчика наклона
  level_sensor_failure: "LEVEL_SENSOR_FAILURE"
  # Неисправность концевика ключа
  wrench_switch_failure: "WRENCH_SWITCH_FAILURE"
  # Неисправность лазера
  laser_failure: "LASER_FAILURE"
  # Отсутсвие соединения с iBox
  ibox_no_conn: "IBOX_NO_CONN"
  # Отсутсвие соединения с РМО
  iplace_no_conn: "IPLACE_NO_CONN"
  # концевик не подтвердил начало движения
  left_jack_limit_switch_failed: "LEFT_JACK_LIMIT_SWITCH_FAILED"
  right_jack_limit_switch_failed: "RIGHT_JACK_LIMIT_SWITCH_FAILED"
  rear_jack_limit_switch_failed: "REAR_JACK_LIMIT_SWITCH_FAILED"

  # События сообщающие о текущем состоянии машины:

  # машина готовится к следующему заданию, закрывая люнет и втягивая домкраты
  closing_arm_pulling_jacks: "CLOSING_ARM_PULLING_JACKS"
  # движение бура заблокировано, люнет не в том состоянии, в каком должен быть
  wrong_arm_state: "WRONG_ARM_STATE"
  # нельзя закрыть люнет - вращатель слишком низко
  head_too_low: "HEAD_TOO_LOW"
  # Втягивание просевших домкратов
  restoring_pulled_jacks: "RESTORING_PULLED_JACKS"
  # нет диффпоправки
  no_rtk: 'NO_RTK'
  # превышены допустимые углы наклона
  angles_above_limits: 'ANGLES_ABOVE_LIMITS'


  # Став востановлен из проседшего положения
  restored_string: "RESTORED_STRING"
  # Робот начинает новое задание
  starting_new_act: "STARTING_NEW_ACT"
  # Робот готов к получению нового задания
  ready_for_new_act: "READY_FOR_NEW_ACT"
  # Робот не смог начать бурить текущую скважину и поехал на следующую
  can_not_drill_cur_hole: "CAN_NOT_DRILL_CUR_HOLE"
  # Робот не может принять/начать новое задание
  can_not_accept_action: "CAN_NOT_ACCEPT_ACTION"
  # Запрет движения по препятствию
  collision_preventer_stoppage: "COLLISION_PREVENTER_STOPPAGE"
  no_front_lidar: 'NO_FRONT_LIDAR'
  no_rear_lidar: 'NO_REAR_LIDAR'

  # VehicleSupervisor events
  sensor_timeout: 'SENSOR_TIMEOUT'
  sensor_out_of_range: 'SENSOR_OOR'
  sensor_stall: 'SENSOR_STALL'
  roll_critical: 'ROLL_CRITICAL'
  pitch_critical: 'PITCH_CRITICAL'
  wrench_sensor1_failure: 'WRENCH_SENSOR1_FAILURE'
  wrench_sensor2_failure: 'WRENCH_SENSOR2_FAILURE'
  wrench_sensor3_failure: 'WRENCH_SENSOR3_FAILURE'
  rpm_sensor_failure: 'RPM_SENSOR_FAILURE'
  fork_linear_sensor_failure: 'FORK_LINEAR_SENSOR_FAILURE'
  left_jack_linear_sensor_failure: 'LJACK_LINEAR_SENSOR_FAILURE'
  right_jack_linear_sensor_failure: 'RJACK_LINEAR_SENSOR_FAILURE'
  rear_left_jack_linear_sensor_failure: 'RLJACK_LINEAR_SENSOR_FAILURE'
  rear_right_jack_linear_sensor_failure: 'RRJACK_LINEAR_SENSOR_FAILURE'
  rotation_pressure_sensor_failure: 'ROT_PRESS_SENSOR_FAILURE'
  feed_pressure_sensor_failure: 'FEED_PRESS_SENSOR_FAILURE'
  air_pressure_sensor_failure: 'AIR_PRESS_SENSOR_FAILURE'
  carousel_linear_sensor_failure: 'CAROUSEL_LINEAR_SENSOR_FAILURE'
  arm_linear_sensor1_failure: 'ARM_LINEAR_SENSOR1_FAILURE'
  arm_linear_sensor2_failure: 'ARM_LINEAR_SENSOR2_FAILURE'

