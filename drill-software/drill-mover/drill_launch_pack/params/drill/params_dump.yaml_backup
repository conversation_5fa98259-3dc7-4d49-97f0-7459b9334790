AdamAdapterNode:
  analog:
    counter: {address: 2, count: 2, is_signed: true, unit: 16}
    fork_length: {address: 4, count: 1, is_signed: false, unit: 17}
    jack_rangefinder_left: {address: 1, count: 1, is_signed: false, unit: 17}
    jack_rangefinder_rear: {address: 2, count: 1, is_signed: false, unit: 17}
    jack_rangefinder_right: {address: 3, count: 1, is_signed: false, unit: 17}
    laser: {address: 0, count: 1, is_signed: false, unit: 17}
  check_msg: false
  discrete:
    d_1:
      address: 0
      count: 7
      device_map: {'2': tower_switch_vert, '3': tower_switch_incl, '4': jacks_switch_rear,
        '5': jacks_switch_left, '6': jacks_switch_right}
      unit: 5
    d_2:
      address: 0
      count: 7
      device_map: {'0': cup_high, '2': arm_open, '3': arm_closed, '4': cup_low, '5': fork_switch_front,
        '6': fork_switch_rear}
      unit: 1
    d_3:
      address: 0
      count: 7
      device_map: {'0': fork_cw, '1': fork_ccw, '2': tower_calib_low, '3': tower_calib_high,
        '4': carousel_open, '5': carousel_closed}
      unit: 2
    d_4:
      address: 0
      count: 7
      device_map: {'0': wrench_stage_2_open, '1': wrench_stage_1_closed, '2': wrench_stage_1_open,
        '3': wrench_stage_2_closed, '4': index_1, '5': above_carousel}
      unit: 7
  modbus_client: {baudrate: 115200, method: rtu, port: /dev/ttyUSB0, timeout: 0.1}
  rate: 30
  relay:
    r_1:
      address: 16
      count: 8
      device_map: {'0': tower_vert_pin_pull, '1': tower_vert_pin_push, '2': tower_incl_pin_pull,
        '3': tower_incl_pin_push, '4': emergency_stop, '5': air_compressor, '6': dust_collector,
        '7': drive_drill_mode}
      unit: 3
    r_2:
      address: 16
      count: 8
      device_map: {'2': wrench_grip_close, '3': wrench_grip_open, '4': fork_push,
        '5': fork_pull}
      unit: 4
    r_3:
      address: 16
      count: 8
      device_map: {'0': red_lamp, '2': yellow_lamp, '3': blue_lamp}
      unit: 8
ArmControllerNode:
  allowed_modes: [drilling, tower_tilt, shaft_buildup, shaft_stow]
  close_push_time: 3.0
  closing_time: 30
  no_reaction_time: 4.0
  open_push_time: 1.0
  opening_time: 30
  rate: 30
CarouselControllerNode: {close_speed: 1.0, max_oc_time: 10, max_turn_time: 10, open_speed: 1.0,
  rate: 5, rotate_speed: 1.0}
CatDriverNode:
  GOAL_ACHIVED_ZONE: 0.06
  approach_max_turn_rate: 0.008
  approach_yawrate_pid: {d: 0.0, i: 0.0, i_saturation: 0.01, p: 0.052}
  correction_pid: {d: 0.02, i: 0.0, i_saturation: 0.1, p: 0.5}
  debug: true
  dist_thr_k: 0
  future_yaw_len: 0.5
  max_allowed_target_dist: 0.15
  max_approach_ang_err: 8
  max_approach_speed: 0.15
  max_turn_rate: 0.09
  max_uncut_len: 0.5
  rate: 50
  slowdown_k: 5.5
  yaw_p: 0.15
  yaw_thr_k: 0
  yawrate_pid: {d: 0.01, i: 0.0, i_saturation: 0.2, p: 0.3}
CatRegulatorNode:
  disable_mode_check: true
  disable_msg_check: true
  left_reg: {d: 0.2, d_term_tc: 0.3, ff: 2.5, force_zero: true, i: 0.75, i_saturation: 0.3,
    max: 0.4, min: -0.4, out_max: 1, out_min: -1, out_tc: 0.3, p: 1.1}
  rate: 10
  right_reg: {d: 0.2, d_term_tc: 0.3, ff: 2.5, force_zero: true, i: 0.75, i_saturation: 0.3,
    max: 0.4, min: -0.4, out_max: 1, out_min: -1, out_tc: 0.3, p: 1.1}
DrillRegulatorNode:
  feed_reg: {d: 0.35, d_term_tc: 0.6, ff: 0.6, i: 0.75, i_saturation: 1.0, max: 0.56,
    min: -0.75, out_max: 1, out_min: -1, out_tc: 0.1, p: 0.5}
  press_reg: {d: 0.0, d_term_tc: 0.2, ff: 0.0012, i: 0.0001, i_saturation: 0.15, max: 222,
    min: 0, out_max: 0.36, out_min: 0, out_tc: 1.3, p: 0.00011}
  rate: 30
  rot_reg: {d: 0, d_term_tc: 0.2, ff: 0.0068, i: 0, i_saturation: 0.3, max: 100, min: -100,
    out_max: 1, out_min: -1, out_tc: 0.1, p: 0}
DrillSupervisorNode:
  error_map:
    DriverNode: {'5': 4}
    PlannerNode: {'5': 4}
  error_rate_check_period: 1
  max_errors_rate: 15
  max_report_time_delay: 1
  nodes: []
  rate: 30
  restart_lock_time: 10
  special_timeouts: {HalClientNode: 10, RemoteConnectorNode: 4}
  topic_paramkeys_to_monitor: []
  topics_to_monitor: []
  topics_to_restart_nodes: {segmented_obstacles: tailing_detector_driller}
DrillerNode:
  after_pullup_rotaion: 60
  air_nominal_long_enough: 30
  air_transient_responce_time: 7.0
  arm_close_depth: 7
  arm_close_min_depth: 5
  arm_open_depth: 8
  arm_open_max_depth: 11
  clay_drilling_feed_pressure: 60
  clay_feed_pressure_limit: 125
  clay_mode: false
  clay_mode_air_cutoff: 7.8
  clay_mode_air_feed_drop: 7.0
  clay_pass_soft_time: 30
  clay_redused_drilling_feed_pressure: 50
  clay_slow_press_increase_rate: 1
  clay_speed_low_too_long: 40
  clay_start_feed_pressure: 50
  clay_upper_drill_i_sat: 20
  close_arm: false
  close_arm_depth: 5.4
  debug: true
  default_air_power: 1
  depth_error2feed_speed_k: 0.3
  depth_step_to_drop_clay: 1.0
  drill_speed_i: 800
  drilling_feed_pressure: 160.00780031201248
  drop_from_clay_time_step: 100
  free_rotation_pressure: 40
  ground_detect_feed_speed_thr: 0.05
  ground_detect_rot_pres: 100
  ground_detect_time_thr: 1.5
  hard_rot_rotation_speed: 80
  height_delta_hard_rot: 0.5
  high_rp_threshold: 220
  high_rp_time_window: 10
  ignore_stuck_time: 3
  low_drill_speed_thr: 0.015
  lower_drill_i_sat: -130
  max_air_pressure: 5.7
  max_air_pressure_excess: 0.4
  max_depth_to_drop_clay: 7.0
  max_feed_speed: 1
  max_invalid_depth_time: 0.6
  max_invalid_depth_time_drill: 8.0
  max_ob_pass_time: 60
  max_raise_feed_speed: 0.6
  max_rotation_speed: 94.24336973478924
  min_raise_feed_speed: 0.06
  min_state_time_to_stuck: 0
  min_time_alignment_fixed: 3
  no_rotation_too_long: 1.0
  overburden_layer_thickness: 0.5
  pass_soft_press: 55
  pass_soft_time: 10
  pullup_feed_speed: 0.2
  pullup_rotation_speed: 100
  raise_fix_takes_too_long: 15
  raise_rotation_speed: 40
  raise_stop_rotation_depth: 1.0
  raise_takes_too_long: 150
  rate: 30
  recover_feed_speed: 0.05
  reduced_rotation: 50
  reduced_speed_zone: 1.5
  redused_drilling_feed_pressure: 130
  send_hole_to_kobus: false
  short_pullup_height: 3.0
  slow_feed_speed: 0.2
  slow_press_increase_rate: 5
  soft_touchdown_feed_pressure: 60
  start_feed_pressure: 90
  start_feed_speed: 1
  start_rotation_speed: 80
  stuck_thr_speed: 0.05
  stuck_timeout: 3.0
  target_drill_speed: 0.06
  too_high_rp_threshold: 270
  too_high_rp_time_thr: 3
  too_low_rotation_speed: 3
  touchdown_depth_delta: 0.25
  touchdown_feed_pressure: 80
  unstuck_push_down_time: 3
  unstuck_spin_time: 3
  upper_drill_i_sat: 41
  vibration_limits: {'15': 65, '3': 20, '5': 18, '7': 13, '9': 14, inf: Infinity}
  vibration_reduce_rot_time: 1.5
  wait_after_drill: 5
DrillerSimNode: {feed_press_k: 25, feed_speed_k: 1, landfall_probability: 0.0, layer_thickness_lower_bound: 1,
  layer_thickness_mu: 2, layer_thickness_sigma: 2, layer_thickness_upper_bound: 6,
  lf2rot_press_k: 6000, low_pass_k: 0.05, max_feed_pressure: 25, max_rotation_speed: 2,
  min_drilling_speed: 0.00028, min_time_delta_landfall: 20, min_time_delta_slowdown_change: 1,
  overall_softness_k: 0.01, rate: 100, rot_dec2press_k: 500, rot_speed2press_k: 100,
  rot_speed_k: 1, slowdown_change_prob: 0.5, slowdown_layer_probability: 0.0, upper2lower_k: 1}
Global:
  EPS: 1.0e-06
  MSG_TTL: 0.5
  Reports:
    Critical: {Action_failed: 600, Bad_conditions: 602, Hardware_malfunction: 601}
    Emergency: {pwmbox_failed: 700}
    Errors: {Connection_failed: 502, Incoming_data_delay: 503, Initialization_failed: 501,
      Internal_error: 500, Report_delay: 504}
    Info: {Initializing: 201, Shut_down: 203, Shutting down: 202, Started: 205, Starting: 204,
      Working: 200}
    Not_working: {Initialization_exception: 100, Runtime_exception: 101}
    Warnings: {Connecting: 402, Internal_warning: 401, No_incoming_data: 403, Reconnecting: 402}
  SYSTEM_STATUS: {CRITICAL: 402, ERROR: 401, WARNING: 400}
  Timeouts: {timeout_not_shoving_up: 2, timeout_obsolescence: 0.5}
  new_state_TTL: 3
  system_flags: {arm_present: true, use_rotation_speed_sensor: false}
  topics: {arm_action: arm_action, arm_control: arm_control, arm_control_direct: arm_control_direct,
    arm_controller_status: ArmControllerNodeStatus, arm_switch_state: arm_switch_state,
    calib_tower_switch: calib_tower_switch, carousel_control: carousel_control, carousel_control_direct: carousel_control_direct,
    carousel_controller_action: carousel_controller_action, carousel_controller_status: carousel_controller_status,
    carousel_switch_state: carousel_switch_state, cat_control: cat_control, cat_control_direct: cat_control_direct,
    cat_control_smart: cat_control_smart, cat_feedback: /st/cat_fb, compressor_control: compressor_control,
    compressor_control_direct: compressor_control_direct, counter_data: counter_data,
    drill_actuator: drill_actuator, drill_angle: drill_angle, drill_control_direct: drill_control_direct,
    drill_control_smart: smart_drill_control, drill_rpm: drill_rpm, drill_state: drill_state,
    driller_action: driller_action, driller_out: driller_out, driller_status: driller_status,
    drive_drill_mode: drive_drill_mode, drive_drill_mode_remote: drive_drill_mode_remote,
    driver_out: driver_out, dust_collector_control: dust_collector_control, dust_collector_control_direct: dc_control_direct,
    emergency_stop: emergency_stop, fork_control: fork_control, fork_control_direct: fork_control_direct,
    fork_length_data: fork_length_data, fork_state: fork_state, fork_switch_state: fork_switch_state,
    internal_error: internal_error, internal_report: internal_report, jack_rangefinder_data: jack_rangefinder_data,
    jacks_control_speed: jacks_control_speed, jacks_state: jacks_state, kobus_platform_level: platform_level,
    kobus_rotation_speed: kobus_rotation_speed, kobus_state: kobus_state, lamp_control: lamp_control,
    laser_data: laser_data, last_finished_holeid: last_finished_holeid, leveler_control_direct: leveler_control_direct,
    leveler_status: leveler_status, locker_out: locker_out, main_action: main_action,
    main_mode: main_mode, main_sm_drill_out: main_sm_drill_out, manual_state: manual_state,
    moving_error: moving_error, permission: permission, planned_route: planned_route,
    planner_action: planner_action, planner_status: planner_status, planner_stoppage: planner_stoppage,
    platform_level: platform_level, pressure_state: pressure_state, progress_topic: progress_topic,
    pwm_enabled: pwm_enabled, relative_depth: relative_depth, remote_drilling: remote_drilling,
    remote_moving: remote_moving, remote_type: remote_type, rod_changer_action: rod_changer_action,
    rod_changer_out: rod_changer_out, rod_changer_status: rod_changer_status, shaft_action: shaft_action,
    shaft_status: shaft_status, speed_ctrl_stopped: speed_ctrl_stopped, state: state,
    system_status: system_status, tower_action: tower_action, tower_control: tower_control,
    tower_control_direct: tower_control_direct, tower_controller_status: tower_controller_status,
    tower_state: tower_state, tower_switch_state: tower_switch_state, vibration_state: vibration_state,
    wrench_control: wrench_control, wrench_control_direct: wrench_control_direct,
    wrench_controller_action: wrench_controller_action, wrench_controller_status: wrench_controller_status,
    wrench_state: wrench_state}
HalClientNode: {IP: 127.0.0.1, PORT: 5555, PROTOCOL: tcp, rate: 20}
KobusAdapterNode:
  allowed_reset_depth_error: 40
  depth_change_ttl: 5
  depth_start: 3.637
  force_invalid_tower: false
  kobus_address: ************
  kobus_company: vist
  kobus_password: HiKobus
  kobus_port: 9753
  max_package_size: 1400
  max_pos_change: 0.2
  max_yaw_change: 5
  rate: 30
  reset_depth_max_delta: 300
  reset_depth_min_delta: 50
  robot_address: 0.0.0.0
  robot_port: 9755
  sensor_pack_len: 16
  sensors: {SidPlatformAngleCross: 104, SidPlatformAngleLong: 103, SidTemperatureOut: 512,
    SidTowerAngleCross: 102, SidTowerAngleLong: 101, air_press: 502, feed_press: 500,
    position_mode: 307, position_x: 300, position_y: 301, position_yaw: 303, position_z: 302,
    realtime: 10010, relative_depth: 525, rot_press: 501, rotation_speed: 508}
  stream_interval: 100
  top_to_calib: 3.748
LevelerNode:
  allowed_modes:
    final_leveling: [leveling, restore_string]
    holding: [calib, post_calib, drilling, tower_tilt, grounding, leveling, shaft_buildup,
      shaft_stow, wait_after_level, rod_lock/unlock, idle, restore_string]
    leveling: [leveling, grounding, restore_string]
    pulled: [grounding, moving, leveling, idle, restore_string, wait_before_level]
    pulling: [idle, grounding]
    restore_pulled: [moving, idle, wait_before_level, leveling, restore_string]
  allowed_pitch_error: 0.01
  allowed_roll_error: 0.01
  angles_time_thr: 0.6
  crt_angle_thr: 1
  debug: true
  enable_brk: false
  flat_roll_thr: 1.2
  holding_dead_zone: 0.6
  holding_dead_zone_hist_low: 0.35
  holding_speed_k: 2.5
  jack_max_len: 2
  jack_start_push_max_time: 3.0
  level_fix_time: 3.5
  max_allowed_pitch: 22
  max_allowed_pitch_move: 22
  max_allowed_roll: 8
  max_allowed_roll_move: 10
  max_final_level_time: 60
  max_hold_jack_ctrl: 0.05
  max_pitch_after_pushing_rear: -0.012
  max_pulling_time: 70
  max_push_all_front_time: 100
  max_push_front_first_time: 100
  max_push_front_time: 100
  max_push_rear_final_time: 100
  max_push_rear_time: 100
  max_roll_after_pushing_front: 0.012
  max_speed: 0.1
  min_allowed_pitch: -22
  min_allowed_pitch_move: -13
  min_allowed_roll: -13
  min_allowed_roll_move: -15
  min_pitch_delta: 0.0025
  min_roll_delta: 0.002
  push_front_allowed_pitch_error: 0.01
  rate: 30
  wait_before_hold: 1.0
MainStateMachineNode: {allowed_hole_depth_error: 0.1, allowed_string_drawdown: 0.3,
  allowed_string_drawdown_for_tower_tilt: 0.5, debug: true, extra_depth: 0.2, ignore_buildup_diff: 1.0,
  max_post_calib_time: 30, max_remote_prepare_waite_time: 3, max_string_speed: 0.2,
  min_string_speed: 0.1, no_move_allowed_err: 0.2, rate: 50, reduced_drill_speed_zone: 0.3,
  remote_prepare_max_speed: 0.05, skip_drilling: false, skip_leveling: false, skip_moving: false,
  skip_tower_tilt: false, string_fix_dz: 0.05, string_fix_time: 1.0, string_move_k: 0.3,
  top_to_initial: 1.0, wal_delay: 1.0, wbl_delay: 4.0}
ModbusNode:
  IOs:
    DI_Ad1: {address: 0, count: 7, type: di, unit: 1}
    DO_Ad1: {address: 16, count: 8, type: co, unit: 1}
  baudrate: 115200
  input_topics:
    ass_control:
      fields:
        blue: {default: false, io: DO_Ad1, latch: false, word: 7}
        red: {default: false, io: DO_Ad1, latch: true, word: 1}
        yellow: {default: false, io: DO_Ad1, latch: false, word: 2}
      msg_type: drill_msgs/LampCtrl
      timeout: 10
    poop_control:
      fields:
        closed: {default: false, io: DO_Ad1, latch: true, word: 0}
        cup_1_top: {default: false, io: DO_Ad1, latch: false, word: 6}
        index_1: {default: true, io: DO_Ad1, latch: false, word: 4}
        index_2: {default: true, io: DO_Ad1, latch: false, word: 5}
        open: {default: false, io: DO_Ad1, latch: false, word: 3}
      msg_type: drill_msgs/CarouselSwitchState
      timeout: 10
  method: rtu
  output_topics:
    ass_status:
      fields:
        blue: {io: DI_Ad1, word: 3}
        red: {io: DI_Ad1, word: 1}
        yellow: {io: DI_Ad1, word: 2}
      msg_type: drill_msgs/LampCtrl
    poop_status:
      fields:
        closed: {io: DI_Ad1, word: 0}
        cup_1_top: {io: DI_Ad1, word: 7}
        index_1: {io: DI_Ad1, word: 5}
        index_2: {io: DI_Ad1, word: 6}
        open: {io: DI_Ad1, word: 4}
      msg_type: drill_msgs/CarouselSwitchState
  port: /dev/ttyUSB0
  rate: 20
PWMBoxAdapterNode:
  baudrate: 115200
  channel_map:
    arm: {ch: 15, ctrl_lower_threshold: 71, ctrl_lower_threshold_rev: 71, ctrl_upper_threshold: 100,
      ctrl_upper_threshold_rev: 100, reverse: true}
    carousel_move: {ch: 18, ctrl_lower_threshold: 50, ctrl_lower_threshold_rev: 50,
      ctrl_upper_threshold: 62, ctrl_upper_threshold_rev: 75, reverse: false}
    carousel_rotate: {ch: 17, ctrl_lower_threshold: 90, ctrl_lower_threshold_rev: 90,
      ctrl_upper_threshold: 120, ctrl_upper_threshold_rev: 120, reverse: false}
    cat_left: {ch: 3, ctrl_lower_threshold: 84, ctrl_lower_threshold_rev: 84, ctrl_upper_threshold: 166,
      ctrl_upper_threshold_rev: 166, poly_0: 68.57, poly_0_rev: 77.51, poly_1: 178.015,
      poly_1_rev: 141.982, poly_2: -226.652, poly_2_rev: -149.875, poly_3: 145.168,
      poly_3_rev: 96.112, reverse: true}
    cat_right: {ch: 4, ctrl_lower_threshold: 84, ctrl_lower_threshold_rev: 75, ctrl_upper_threshold: 158,
      ctrl_upper_threshold_rev: 140, poly_0: 70.3, poly_0_rev: 72.82, poly_1: 155.927,
      poly_1_rev: 155.393, poly_2: -175.183, poly_2_rev: -163.958, poly_3: 107.054,
      poly_3_rev: 93.844, reverse: true}
    feed_press: {ch: 2, ctrl_lower_threshold: 0, ctrl_lower_threshold_rev: 0, ctrl_upper_threshold: 255,
      ctrl_upper_threshold_rev: 255, reverse: false}
    feed_speed: {ch: 5, ctrl_lower_threshold: 80, ctrl_lower_threshold_rev: 80, ctrl_upper_threshold: 255,
      ctrl_upper_threshold_rev: 255, reverse: true}
    jack_left: {ch: 13, ctrl_lower_threshold: 62, ctrl_lower_threshold_rev: 62, ctrl_upper_threshold: 120,
      ctrl_upper_threshold_rev: 120, reverse: false}
    jack_rear: {ch: 11, ctrl_lower_threshold: 75, ctrl_lower_threshold_rev: 75, ctrl_upper_threshold: 127,
      ctrl_upper_threshold_rev: 127, reverse: false}
    jack_right: {ch: 12, ctrl_lower_threshold: 62, ctrl_lower_threshold_rev: 62, ctrl_upper_threshold: 120,
      ctrl_upper_threshold_rev: 120, reverse: false}
    rotation_speed: {ch: 6, ctrl_lower_threshold: 80, ctrl_lower_threshold_rev: 77,
      ctrl_upper_threshold: 200, ctrl_upper_threshold_rev: 200, reverse: true}
    tilt_speed: {ch: 14, ctrl_lower_threshold: 49, ctrl_lower_threshold_rev: 59, ctrl_upper_threshold: 77,
      ctrl_upper_threshold_rev: 77, reverse: true}
    wrench: {ch: 16, ctrl_lower_threshold: 70, ctrl_lower_threshold_rev: 72, ctrl_upper_threshold: 90,
      ctrl_upper_threshold_rev: 90, reverse: true}
  channel_total_num: 18
  command_num: 2
  device_id: 1
  disable_msg_check: false
  max_no_resp_time: 2
  max_resp_err_count: 3
  pkg_start_flag: 160
  port: /dev/ttyXR0
  rate: 30
  read_resp_timeout: 0.3
  timeout: 0.1
PlannerNode:
  GOAL_ACHIEVED_ZONE_FINAL: 0.35
  GOAL_ACHIEVED_ZONE_INTERMEDIATE: 0.3
  MAX_SHIFT: 2
  OUTPUT_ROUTE_LEN: 15
  before_end_low_speed_len: 1.0
  curve_analysis_length: 5
  debug: true
  inclined_slowdown_len: 1.5
  maneuver_builder: {check_step: 0.8, grid_extra_len: 3, grid_half_width: 6.7, grid_step: 1.3,
    grid_yaw_step: 0.3925, max_search_radius: 4, max_steps: 5, min_search_radius: 2.47,
    min_turn_radius: 3.6, point_step: 0.1, straight_part_len: 2.0, timeout: 120}
  max_curvature_radius: 60.0
  max_pitch_to_ruch: 5
  max_roll_to_ruch: 3
  rate: 30
  smooth_start_k: 0.08
  speed_limit_when_inclined: 0.2
  target_speed: 0.32
RemoteConnectorNode:
  IP: 0.0.0.0
  PORT: 6000
  PROTOCOL: tcp
  cameras:
    '1': {IP: ***********, login: admin, password: 12345678a}
    '2': {IP: ***********, login: admin, password: 12345678a}
    '3': {IP: ***********, login: admin, password: 12345678a}
    '4': {IP: ***********, login: admin, password: 12345678a}
    '5': {IP: ***********, login: admin, password: 12345678a}
    '6': {IP: ***********, login: admin, password: 12345678a}
  direct_control_map:
    arm:
      MSG: drill_msgs/FloatStamped
      TOPIC: /arm_control_direct
      args:
        move_arm: {MAX: 1, MIN: -1, NAME: value}
    carousel:
      MSG: drill_msgs/CarouselCtrlStamped
      TOPIC: /carousel_control_direct
      args:
        move_carousel: {MAX: 1, MIN: -1, NAME: move}
        rotate_carousel: {MAX: 1, MIN: -1, NAME: rotate}
    cat:
      MSG: drill_msgs/CatsStamped
      TOPIC: /cat_control_direct
      args:
        left_track: {MAX: 1, MIN: -1, NAME: cat_left}
        right_track: {MAX: 1, MIN: -1, NAME: cat_right}
    compressor:
      MSG: drill_msgs/FloatStamped
      TOPIC: /compressor_control_direct
      args:
        value: {MAX: 1, MIN: 0, NAME: value}
    drill:
      MSG: drill_msgs/DrillCtrl
      TOPIC: /remote_drilling
      args:
        feed_pressure: {MAX: 1, MIN: -1, NAME: feed_pressure}
        feed_speed: {MAX: 1, MIN: -1, NAME: feed_speed}
        rotation_speed: {MAX: 1, MIN: -1, NAME: rotation_speed}
    drivedrill:
      MSG: drill_msgs/VehicleBehaviorMode
      TOPIC: /drive_drill_mode_remote
      args:
        mode: {MAX: 1, MIN: -1, NAME: mode}
    dust_collector:
      MSG: drill_msgs/BoolStamped
      TOPIC: /dc_control_direct
      args:
        value: {MAX: 1, MIN: 0, NAME: value}
    fork:
      MSG: drill_msgs/FloatStamped
      TOPIC: /fork_control_direct
      args:
        move_fork: {MAX: 1, MIN: -1, NAME: value}
    leveler:
      MSG: drill_msgs/JacksCtrlStamped
      TOPIC: /leveler_control_direct
      args:
        left_jack: {MAX: 1, MIN: -1, NAME: left}
        rear_jack: {MAX: 1, MIN: -1, NAME: rear}
        right_jack: {MAX: 1, MIN: -1, NAME: right}
    tower:
      MSG: drill_msgs/TowerCtrlStamped
      TOPIC: /tower_control_direct
      args:
        inclined_pin: {MAX: 1, MIN: -1, NAME: inclined_pin_movement}
        tower_tilt: {MAX: 1, MIN: -1, NAME: tilt_speed}
        vertical_pin: {MAX: 1, MIN: -1, NAME: vertical_pin_movement}
    wrench:
      MSG: drill_msgs/WrenchCtrlStamped
      TOPIC: /wrench_control_direct
      args:
        grip_wrench: {MAX: 1, MIN: -1, NAME: grip}
        move_wrench: {MAX: 1, MIN: -1, NAME: move}
  iplace_ids: [124, 123]
  max_msg_delay: 5
  max_speed_err: 0.1
  rate: 30
  smart_control_map:
    cat:
      MSG: drill_msgs/CatsStamped
      TOPIC: /cat_control_smart
      args:
        left_track: {MAX: 10, MIN: -10, NAME: cat_left}
        right_track: {MAX: 10, MIN: -10, NAME: cat_right}
    drill:
      MSG: drill_msgs/DrillCtrl
      TOPIC: /remote_drilling_smart
      args:
        feed_pressure: {MAX: 10, MIN: -10, NAME: feed_pressure}
        feed_speed: {MAX: 10, MIN: -10, NAME: feed_speed}
        rotation_speed: {MAX: 10, MIN: -10, NAME: rotation_speed}
  telemetry_map:
    arm_controller_status: {status: ArmControllerNode}
    arm_switch_state: {close_switch_on: arm_closed, open_switch_on: arm_open}
    carousel_controller_status: {status: CarouselControllerNode}
    carousel_switch_state: {closed: carousel_closed, cup_1_top: carousel_cup_1, index_1: carousel_index_1,
      index_2: carousel_index_2, open: carousel_open}
    drill_state: {angular_pose: drill_angular_pose, feed_speed: drill_feed_speed,
      rotation_speed: drill_rotation_speed, spindle_depth: spindle_depth}
    driller_status: {status: DrillerNode}
    jacks_state: {left: left_jack_pulled, rear: rear_jack_pulled, right: right_jack_pulled}
    last_finished_holeid: {data: last_holeid}
    leveler_status: {status: LevelerNode}
    main_mode: {mode: MainStateMachineNode}
    maneuver_processing: {value: maneuver_processing}
    planned_route:
      points:
        local_pose: {x: point_x, y: point_y, z: point_z}
    platform_level: {pitch: platform_pitch, roll: platform_roll}
    pressure_state: {air_pressure: air_pressure, feed_pressure: drill_feed_pressure,
      rotation_pressure: drill_rotation_pressure}
    pwm_enabled: {value: pwm_enabled}
    segmented_obstacles: {RAW: obstacles_pc}
    state:
      position: {x: position_x, y: position_y, z: position_z}
      speed: speed
      yaw: yaw
    tailing_map_polygons: {RAW: tailing_map}
    tower_angle_ready: {value: tower_ok_to_lock}
    tower_controller_status: {status: TowerControllerNode}
    tower_state: {value: tower_angle}
    tower_switch_state: {inclined_switch_on: inclined_pin, vertical_switch_on: vert_pin}
    vibrations: {vibrations: vibration}
RodChangerNode: {align_rpm: 7, attach_approach_speed: 0.08, attach_high_rot_pressure: 270,
  attach_rotation: 20, attach_speed: 0.05, brakeout_rev: 0.3, carousel_close_speed: 1.0,
  carousel_open_speed: 1.0, cw_rot_speed: 20, detach_rev: 3.5, detach_rpm: 20, fix_time: 2,
  force_rot_speed: 110, fork_start_time: 20, head_lift_speed: 0.3, max_align_time: 20,
  max_ang_err: 22, max_brakeout_attempts: 3, max_brakeout_time: 5, max_dh: 0.05, max_fork_push_time: 20,
  max_head_pos_for_wrench: 18.82, max_rot_time: 10, max_vert_err: 0.05, over_carousel_head_pos: 10.0,
  pull_out_extra_height: 0.05, pull_out_speed: 0.05, rate: 30, stop_rpm_thr: 2, time_to_start_lift: 1,
  vert_align_speed: 0.05, wrench_brakeout_rev: 0.03}
SimulatorNode:
  air_c2f: 3
  arm_c2f: 0.2
  carousel_move_c2f: 1
  carousel_rot_c2f: 0.2
  cat_c2f: 3
  fork_c2f: 0.2
  grnd_left: 0
  grnd_rear: 0
  grnd_right: 0
  head_c2f: 0.6
  jack_c2f: 0.2
  layers:
    thickness: {lower_bound: 1, mu: 2, sigma: 2, upper_bound: 6}
    total_num: 50
  left_jack_len_start: 1.4
  max_air_press: 4
  max_arm_len: 0.3
  max_carousel_move_len: 0.3
  max_carousel_rot_len: 0.3
  max_feed_pressure: 25
  max_fork_len: 0.3
  max_incl_pin_len: 0.3
  max_jack_len: 4
  max_rotation_speed: 2
  max_tilt: 90
  max_vert_pin_len: 0.3
  max_wrench_grip_len: 0.2
  max_wrench_move_len: 0.3
  min_air_press: 0
  min_arm_len: 0
  min_drilling_speed: 0.00028
  min_fork_len: 0
  min_jack_len: 1.4
  pin_c2f: 0.004
  press_c2f: 0.01
  rate: 50
  rear_jack_len_start: 1.4
  right_jack_len_start: 1.4
  rot_c2f: 100
  spindle_start: 1
  tilt_c2f: 0.2
  wrench_grip_c2f: 0.2
  wrench_move_c2f: 0.2
  x_start: 0
  y_start: 0
  yaw_start: 0
StateTracker2Node: {rate: 100}
StatsLoggerNode:
  ns_topics:
    state: [state, drill_state, pressure_state, platform_level, jacks_state, vibration_state]
  rate: 1
  seconds_per_file: 3600
  work_time: 259200
TowerControllerNode:
  allow_inclined_with_no_feedback: false
  allowed_angles: {'0': 0, '10': 9.5, '15': 14.2, '5': 4.5}
  allowed_modes: [tower_tilt]
  allowed_tilt_error: 0.3
  debug: true
  min_angle_delta: 1
  pins_move_time: 6
  pins_no_reaction_time: 2
  rate: 20
  tilt_error2speed: 0.3
  tilt_fixed_time: 3
  tilt_regulation_time: 40
Vehicle:
  VEHID: 4694
  carousel_bottom_pos: 18.03
  cup_height: 0.2
  drill_bit_len: 0.75
  extension_len: 0.24
  fork_ang_pose: 0
  geometry:
    drill_coordinates: {x: -2.8, y: 0}
    k_len: 1.0
    left_cat_polygon_points:
    - {x: 1.7, y: -2.2}
    - {x: 1.7, y: -1.3}
    - {x: -4.8, y: -1.3}
    - {x: -4.8, y: -2.2}
    platform_height: 2.4
    platform_length: 8.5
    platform_width: 3.8
    polygon_points:
    - {x: 6.11, y: -1.54}
    - {x: 2.66, y: -1.54}
    - {x: 2.06, y: -2.13}
    - {x: -5.71, y: -2.13}
    - {x: -5.71, y: 2.13}
    - {x: 1.42, y: 2.13}
    - {x: 1.42, y: 3.5}
    - {x: 6.11, y: 3.5}
    r_min: 1.5
    right_cat_polygon_points:
    - {x: 1.7, y: 1.3}
    - {x: 1.7, y: 2.2}
    - {x: -4.8, y: 2.2}
    - {x: -4.8, y: 1.3}
    tower2center: 2.8
    tower_height: 18.95
    tracks_dist: 3.3
  max_speed: 0.4
  moving_regimes:
    bit_faster: {curvature: 45, max_shift: 3, speed: 0.2}
    high_speed: {curvature: 100, max_shift: 1, speed: 0.5}
    low_speed: {curvature: 0.001, max_shift: 7, speed: 0.15}
    normal: {curvature: 80, max_shift: 1.5, speed: 0.3}
  shaft_len_params:
    '0':
      full_len: 17.42
      grooves_ang_pose:
        bot: [0]
        top: [1.98, 0.41, -1.25, -2.82]
      lower_len: 0.11
      screw_len: 0.1
      upper_len: 0.11
    '1':
      full_len: 7.6
      grooves_ang_pose:
        bot: [1.04, -0.52]
        top: [1.98, 0.41, -1.25, -2.82]
      lower_len: 0.6
      screw_len: 0.1
      upper_len: 0.06
  shaft_list: ['0', '1']
WrenchControllerNode: {close_speed: 1.0, open_speed: 1.0, rate: 20}
rosdistro: 'kinetic

  '
roslaunch:
  uris: {host_submarine__33007: 'http://Submarine:33007/', host_submarine__33533: 'http://Submarine:33533/',
    host_submarine__33915: 'http://Submarine:33915/', host_submarine__34287: 'http://Submarine:34287/',
    host_submarine__34341: 'http://Submarine:34341/', host_submarine__34425: 'http://Submarine:34425/',
    host_submarine__36997: 'http://Submarine:36997/', host_submarine__37581: 'http://Submarine:37581/',
    host_submarine__38517: 'http://Submarine:38517/', host_submarine__38519: 'http://Submarine:38519/',
    host_submarine__39819: 'http://Submarine:39819/', host_submarine__40769: 'http://Submarine:40769/',
    host_submarine__41721: 'http://Submarine:41721/', host_submarine__41879: 'http://Submarine:41879/',
    host_submarine__41965: 'http://Submarine:41965/', host_submarine__42113: 'http://Submarine:42113/',
    host_submarine__42981: 'http://Submarine:42981/', host_submarine__43357: 'http://Submarine:43357/',
    host_submarine__43591: 'http://Submarine:43591/', host_submarine__43635: 'http://Submarine:43635/',
    host_submarine__43973: 'http://Submarine:43973/', host_submarine__46053: 'http://Submarine:46053/',
    host_submarine__46071: 'http://Submarine:46071/', host_submarine__46397: 'http://Submarine:46397/'}
rosversion: '1.12.17

  '
run_id: 412874fc-a2a4-11eb-84b6-e470b8b29017
