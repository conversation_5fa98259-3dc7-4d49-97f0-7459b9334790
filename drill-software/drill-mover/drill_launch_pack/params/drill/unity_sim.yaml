#Global:
#  topics:
#    platform_level: 'platform_level_epiroc'


HalClientNode:
  IP: '127.0.0.1' # localhost

CanNode:
  IOs:
    feed:
      deadband_value_neg: -345
      deadband_value_pos: 333
      max_value: 635
      min_value: -585
    rot:
      deadband_value_neg: -408
      deadband_value_pos: 405
      max_value: 646
      min_value: -665

#CanNode:
#  IOs:
#    cat_left:
#      min_value: -615
#      max_value: 645
##      polynomial_pos: [0.0, 1.76651, -3.34973, 2.58322]
##      polynomial_neg: [0.0, 1.34466, -1.76995, 1.42529]
#      deadband_value_pos: 344
#      deadband_value_neg: -314
#
#    cat_right:
#      min_value: -615
#      max_value: 645
##      polynomial_pos: [0.0, 1.76651, -3.34973, 2.58322]
##      polynomial_neg: [0.0, 1.34466, -1.76995, 1.42529]
#      deadband_value_pos: 344
#      deadband_value_neg: -314

#  output_topics: # выходы из ноды, приём от устройства, паблишеры
#    jacks_state_raw:
#      fields:
#        rear_left_len:
#          io: 'jack_rear_left_len'
#        rear_right_len:
#          io: 'jack_rear_right_len'
#        left_len:
#          io: 'jack_left_len'
#        right_len:
#          io: 'jack_right_len'

RoboModeSelectorSimulatorNode:
  rate: 10

EmergencySimulatorNode:
  rate: 10

Vehicle:
  geometry:
    platform_height: 1.8
    # ход вращателя метры
    tower_height: 19.88

  restrictions:

      max_allowed_roll: 6.5
      max_allowed_pitch: 6.5
      min_allowed_roll: -6.5
      min_allowed_pitch: -6.5
      max_allowed_roll_move: 10.5
      max_allowed_pitch_move: 2.0
      min_allowed_roll_move: -2.0
      min_allowed_pitch_move: -1.5

      crt_angle_thr_1: 1
      crt_angle_thr_2: 5


  shaft_len_params:
    '0':
      # Растояние от нижнего среза штанги до нижнего паза метры
      lower_len: 0.11
      # Растояние от нижнего паза до верхнего паза метры
      #full_len: 18.5 # с 3м пацаном
      full_len: 18.22
      #full_len: 16.34 # с 1м пацаном
      # Растояние от верхнего паза до верхнего среза штанги метры
      upper_len: 0.11
      # Угловое положение пазов на штанге (радианы)
      screw_len: 0.1

      grooves_ang_pose:
        top:
          - 1.98
          - 0.41
          - -1.25
          - -2.82
        bot:
          - 0

  # Длина шарошки со стабилизатором
  drill_bit_len: 0.485

  # Длина дополнительной секции
  extension_len: 0.34


  devices:
    # Антенны позиционирования (относительно стейта)
    position_antenna:
      local_x: -7.22
      local_y: -1.23
      local_z: 2.84

    vector_antenna:
      local_x: 4.57
      local_y: 1.46
      local_z: 2.48

    trimble:
        host: '127.0.0.1'
#      host: '**************'
#       host: '**************'

#    lidar_front:  # Translation: [5.055, -0.204, 1.450]
#      local_x: 5.055
#      local_y: -0.204
#      local_z: 0.75
#      frame_id: "velodyne_front"
#
#    lidar_back:   # Translation: [-6.729, -0.100, 1.315]
#      local_x: -6.729
#      local_y: -0.100
#      local_z: 0.75
#      frame_id: "velodyne_rear"

#  sensors:
#    lidar:
#      frame_id: "velodyne_front"

MainStateMachineNode:
  top_to_initial: 1.839


DrillerNode:
#  arm_open_depth: 9.0 # положение вращателя, ниже которого будет открыт люнет
#  arm_close_depth: 8.0 # положение вращателя выше которого будет закрыт люнет
#
#  arm_close_min_depth: 7.5 # самое высокое положение вращателя выше которого запрещено движение при открытом люнете
#  arm_open_max_depth: 9.5 # самое низкое положение вращателя ниже которого запрещено движение при закрытом люнете

  default:
    common:
      stuck_move_duration: 5.0
      stuck_thr_speed: 0.05
      min_state_duration_to_stuck: 3

BagsRecorderNode:
  record_folder: "/root/nolidar"
  save_path: "/root/bags_saved"

RemoteConnectorNode:
  cameras:
    '2':
      IP: 'localhost:8554/drill'
    '3':
      IP: 'localhost:8554/right'
    '4':
      IP: 'localhost:8554/left'
    '5':
      IP: 'localhost:8554/rear'
    '6':
      IP: 'localhost:8554/front'
    '7':
      IP: 'localhost:8554/tower'

StateTracker2Node:

#  jack_rl_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.268 #0.15
#    result_2: 0.926 #6.1
#
#  jack_rr_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.268 #0.22
#    result_2: 0.926 #6.4
#
#  jack_fl_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.268 #0.19
#    result_2: 0.926 #5.31
#
#  jack_fr_len_pred:
#    measure_1: 0.268
#    measure_2: 0.926
#    result_1: 0.268 #0.15
#    result_2: 0.926 #6.20

  fork_len_rear_check:
    max_val: 6.6
    min_val: 0

  fork_len_front_check:
    max_val: 15
    min_val: 10.15
    
  wrench_st1_extend_len_check:
    max_val: 24
    min_val: 21.35
    
  wrench_st1_retract_len_check:
    max_val: 15.85
    min_val: 0
    
  wrench_st2_extend_len_check:
    max_val: 24
    min_val: 18.45
    
  wrench_st2_retract_len_check:
    max_val: 12.9
    min_val: 0
    
  wrench_st3_extend_len_check:
    max_val: 24
    min_val: 14.4
    
  wrench_st3_retract_len_check:
    max_val: 12.7
    min_val: 0

  head_rangefinder_pred:
    measure_1: 1533
    result_1: 0
    measure_2: 28969
    result_2: 19.9

  #  inc_roll_pitch_align:
#    pitch_offset: 0 #0.7
#    roll_offset: 0 #0.3
#
#  tower_imu_roll_pitch_align:
#    pitch_offset: 0 #0.7
#    roll_offset: 0 #0.3
#
#  front_imu_roll_pitch_align:
#    pitch_offset: 0 #0.7
#    roll_offset: 0 #0.3
#
#  back_imu_roll_pitch_align:
#    pitch_offset: 0 #0.7
#    roll_offset: 0 #0.3


  inc_roll_pitch_align:
    pitch_offset: 0.0
    roll_offset: 0.0

  tower_imu_roll_pitch_align:
    pitch_offset: 0.0
    roll_offset: 0.0

  tower_inc_roll_pitch_align:
    pitch_offset: -90.0
    roll_offset: -90.0

  front_imu_roll_pitch_align:
    pitch_offset: 15
    roll_offset: 180

  back_imu_roll_pitch_align:
    pitch_offset: 3.65
    roll_offset: -180
    
  head_rangefinder_len_check:
    max_val: 20.0
    min_val: -0.08


CatRegulatorNode:
  rate: 10
  disable_mode_check: True
  disable_msg_check: True
  left_reg:
    i_saturation: 0.5
    p: 1.5
    i: 0.8
    d: 0.15
    ff: 0.125
    min: -0.4
    max: 0.4
    out_min: -1
    out_max: 1
    d_term_tc: 0.3
    out_tc: 0.3
    force_zero: True

  right_reg:
    i_saturation: 0.5
    p: 1.5
    i: 0.8
    d: 0.15
    ff: 0.125
    # Макс скорость вперед - 0.4 м/с
    # Макс скорость назад - 0.4 м/c
    min: -0.4
    max: 0.4
    out_min: -1
    out_max: 1
    d_term_tc: 0.3
    out_tc: 0.3
    force_zero: True


CatDriverNode:
  max_approach_speed: 0.05
  max_approach_ang_err: 10 #deg
  GOAL_ACHIVED_ZONE: 0.11
  max_allowed_target_dist: 0.19


PlannerNode:
  debug: true
  rate: 30
  # Длина максимального допустимого отклонения от траектории метры
  MAX_SHIFT: 3.5
  # Длина траектории в драйвер метры
  OUTPUT_ROUTE_LEN: 15
   # радиус зоны достижения цели в конечной точке маршрута метры
  GOAL_ACHIEVED_ZONE_INTERMEDIATE: 1.85
  GOAL_ACHIEVED_ZONE_FINAL: 1.15
  # Расстояние до конца маршрута, на котором скорость искуственно занижается для эффективной остановки метры
  before_end_low_speed_len: 1.5
  # Длина маршрута для анализа кривизны метры
  curve_analysis_length: 5
  # Максимальный радиус кривизны
  max_curvature_radius: 60.0
  # целевая скорость движения
  target_speed: 0.5


LevelerNode:
  #touchdown_max_err: 0.05

#  max_allowed_roll: 7
#  max_allowed_pitch: 7
#  min_allowed_roll: -7
#  min_allowed_pitch: -7
#  max_allowed_roll_move: 7
#  max_allowed_pitch_move: 7
#  min_allowed_roll_move: -7
#  min_allowed_pitch_move: -7

  max_roll_above_start: 7.0
  max_pitch_above_start: 7.0

  holding_dead_zone_hist_low: 0.1 #deg
  holding_dead_zone_hist_high: 0.21 #deg
  required_stab_time: 3.0


#DrillRegulatorNode:
#  rot_reg:
#    i_saturation: 0.6
#    p: 0.01
#    i: 0.0 #0.0022
#    d: 0.0
#    ff: 0.006
#    min: -120
#    max: 120
#    out_min: -1
#    out_max: 1
#    d_term_tc: 0.2
#    out_tc: 0.1
