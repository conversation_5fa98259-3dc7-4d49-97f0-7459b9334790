Header header

float32 spindle_depth                               # spindle depth from zero position (drill at fork height)
float32 wellhead_altitude                           # z coord (altitude above MSL) of wellhead (ground), calculating on transition to OVERBURDEN_PASS
float32 ground_spindle_depth                        # from fork to well head    =   spindle_depth on transition to OVERBURDEN_PASS
float32 robot_estimated_target_wellhead_depth       # from well head            =   full target_drill_depth - ground_spindle_depth
float32 wellhead_drilling_depth                     # depth from wellhead 		= 	spindle_depth - ground_spindle_depth
float32 max_wellhead_drilling_depth
