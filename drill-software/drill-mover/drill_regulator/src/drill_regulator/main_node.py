#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import json
import math

from common.geometry import norm_angle_minusPI_plusPI
from common.base_node import BaseNode
from std_msgs.msg import String
from common_msgs.msg import MovePermission, MovingError
from common.PID_controller import PID
from drill_msgs.msg import DrillCtrl, VehicleBehaviorMode, DrillState, PressureState, InternalError, RemoteType, FloatStamped

# Режимы из ГА
IDLE = 'idle'
CALIB = 'calib'
DRILLING = 'drilling'
REMOTE = 'remote'
MOVING = "moving"
RESTORE_STRING = 'restore_string'
POST_CALIB = 'post_calib'
SHAFT_BUILDUP = "shaft_buildup"
SHAFT_STOW = "shaft_stow"

# Типы управления в ДУ
SMART = 'smart'


class DrillRegulatorNode(BaseNode):

    def __init__(self, name):
        super(DrillRegulatorNode, self).__init__(name)

        self.move_permission = True

        self.rotation_speed = 0
        self.feed = 0
        self.feed_pressure = 0

        self.rotation_speed_sp = 0
        self.feed_sp = 0
        self.feed_pressure_sp = 0
        self.raw_fp = False

        self.rotation_speed_fb = 0
        self.feed_fb = 0
        self.feed_pressure_fb = 0

        self.last_state_ts = None
        self.last_pressure_ts = None

        self.last_sp_ts = None

        self.state_updated = False
        self.press_updated = False

        self.rotation_torque = 0

        self.main_mode = IDLE

        self.remote_type = None

        self.drill_act_pub = rospy.Publisher(self.global_params['topics']['drill_actuator'],
                                             DrillCtrl, queue_size=10)

        self.feed_pid = PID(
            rospy.get_time,
            self.node_params["feed_reg"]["i_saturation"],
            self.node_params["feed_reg"]['p'],
            self.node_params["feed_reg"]["i"],
            self.node_params["feed_reg"]['d'],
            self.node_params["feed_reg"]['ff'],
            self.node_params["feed_reg"]['min'],
            self.node_params["feed_reg"]['max'],
            self.node_params["feed_reg"]['out_min'],
            self.node_params["feed_reg"]['out_max'],
            self.node_params["feed_reg"]['d_term_tc'],
            self.node_params["feed_reg"]['out_tc'],
            is_angular=False,
            force_zero=True,
            one_side_i=True
        )

        self.press_pid = PID(
            rospy.get_time,
            self.node_params["press_reg"]["i_saturation"],
            self.node_params["press_reg"]['p'],
            self.node_params["press_reg"]["i"],
            self.node_params["press_reg"]['d'],
            self.node_params["press_reg"]['ff'],
            self.node_params["press_reg"]['min'],
            self.node_params["press_reg"]['max'],
            self.node_params["press_reg"]['out_min'],
            self.node_params["press_reg"]['out_max'],
            self.node_params["press_reg"]['d_term_tc'],
            self.node_params["press_reg"]['out_tc'],
            is_angular=False,
            force_zero=True,
            one_side_i=self.node_params["press_reg"]['one_side_i']
        )

        self.rot_pid = PID(
             rospy.get_time,
             self.node_params["rot_reg"]["i_saturation"],
             self.node_params["rot_reg"]['p'],
             self.node_params["rot_reg"]["i"],
             self.node_params["rot_reg"]['d'],
             self.node_params["rot_reg"]['ff'],
             self.node_params["rot_reg"]['min'],
             self.node_params["rot_reg"]['max'],
             self.node_params["rot_reg"]['out_min'],
             self.node_params["rot_reg"]['out_max'],
             self.node_params["rot_reg"]['d_term_tc'],
             self.node_params["rot_reg"]['out_tc'],
             is_angular=False,
             force_zero=True
        )

    def initialize_params(self):
        self.rot_pid.i_saturation = self.node_params["rot_reg"]["i_saturation"]
        self.rot_pid.p = self.node_params["rot_reg"]['p']
        self.rot_pid.i = self.node_params["rot_reg"]['i']
        self.rot_pid.d = self.node_params["rot_reg"]['d']
        self.rot_pid.ff = self.node_params["rot_reg"]['ff']
        self.rot_pid.min = self.node_params["rot_reg"]['min']
        self.rot_pid.max = self.node_params["rot_reg"]['max']
        self.rot_pid.out_min = self.node_params["rot_reg"]['out_min']
        self.rot_pid.out_max = self.node_params["rot_reg"]['out_max']
        self.rot_pid.d_term_tc = self.node_params["rot_reg"]['d_term_tc']
        self.rot_pid.out_tc = self.node_params["rot_reg"]['out_tc']

        self.press_pid.i_saturation = self.node_params["press_reg"]["i_saturation"]
        self.press_pid.p = self.node_params["press_reg"]['p']
        self.press_pid.i = self.node_params["press_reg"]['i']
        self.press_pid.d = self.node_params["press_reg"]['d']
        self.press_pid.ff = self.node_params["press_reg"]['ff']
        self.press_pid.min = self.node_params["press_reg"]['min']
        self.press_pid.max = self.node_params["press_reg"]['max']
        self.press_pid.out_min = self.node_params["press_reg"]['out_min']
        self.press_pid.out_max = self.node_params["press_reg"]['out_max']
        self.press_pid.d_term_tc = self.node_params["press_reg"]['d_term_tc']
        self.press_pid.out_tc = self.node_params["press_reg"]['out_tc']

        self.feed_pid.i_saturation = self.node_params["feed_reg"]["i_saturation"]
        self.feed_pid.p = self.node_params["feed_reg"]['p']
        self.feed_pid.i = self.node_params["feed_reg"]['i']
        self.feed_pid.d = self.node_params["feed_reg"]['d']
        self.feed_pid.ff = self.node_params["feed_reg"]['ff']
        self.feed_pid.min = self.node_params["feed_reg"]['min']
        self.feed_pid.max = self.node_params["feed_reg"]['max']
        self.feed_pid.out_min = self.node_params["feed_reg"]['out_min']
        self.feed_pid.out_max = self.node_params["feed_reg"]['out_max']
        self.feed_pid.d_term_tc = self.node_params["feed_reg"]['d_term_tc']
        self.feed_pid.out_tc = self.node_params["feed_reg"]['out_tc']

    def on_start(self):
        rospy.Subscriber(self.global_params['topics']['driller_out'],
                         DrillCtrl, self.driller_callback)
        rospy.Subscriber(self.global_params['topics']['main_sm_drill_out'],
                         DrillCtrl, self.main_sm_drill_callback)

        rospy.Subscriber(self.global_params['topics']['rod_changer_out'],
                         DrillCtrl, self.rod_changer_callback)

        rospy.Subscriber(self.global_params['topics']['drill_state'],
                         DrillState, self.drill_state_callback)

        rospy.Subscriber(self.global_params['topics']['pressure_state'],
                         PressureState, self.pressure_callback)

        rospy.Subscriber(self.global_params['topics']['main_mode'],
                         VehicleBehaviorMode, self.main_mode_callback)

        rospy.Subscriber(self.global_params['topics']['remote_type'],
                         RemoteType, self.remote_type_callback)

        rospy.Subscriber(self.global_params['topics']['remote_drilling'],
                         DrillCtrl, self.smart_drilling_callback)

        rospy.Subscriber(self.global_params['topics']['permission'],
                         MovePermission, self.permission_callback)

        self.initialize_params()

    def do_work(self):

        # Если нет режима - не работаем
        if self.main_mode is None or self.last_state_ts is None or self.last_pressure_ts is None:
            return

        stop = False

        # Проверяем, что текущий режим позвляет работать, если нет - останавливаем работу
        allowed_modes = (
            DRILLING,
            SHAFT_STOW,
            SHAFT_BUILDUP,
            CALIB,
            POST_CALIB,
            RESTORE_STRING
        )
        if self.main_mode not in allowed_modes or not self.move_permission:
            self.feed_sp = 0
            self.feed_pressure_sp = 0
            self.rotation_speed_sp = 0
            stop = True

        else:
            # Проверяем что последний полученый стейт не слишком старый
            if rospy.Time.now().to_sec() - self.last_pressure_ts > self.global_params['MSG_TTL']:
                error_message = "Outdated pressure state"
                self.handle_internal_error(error_message)
                stop = True

            # Проверяем что последний полученый стейт не слишком старый
            if rospy.Time.now().to_sec() - self.last_state_ts > self.global_params['MSG_TTL']:
                error_message = "Outdated state"
                self.handle_internal_error(error_message)
                stop = True

            if self.last_sp_ts is None or rospy.get_time() - self.last_sp_ts > self.global_params['MSG_TTL']:
                error_message = "Outdated control"
                self.handle_internal_error(error_message)
                stop = True

        # Если ошибок нет - работаем
        if not stop:
            if self.press_updated:
                # print "pres", self.feed_pressure_sp, self.feed_pressure_fb, rospy.get_time()
                if not self.raw_fp:
                    self.feed_pressure = self.press_pid.update(
                        self.feed_pressure_sp,
                        self.feed_pressure_fb
                    )
                else:
                    self.press_pid.integral = 0
                    self.press_pid.d_old = 0
                    self.press_pid.out_old = 0
                    self.feed_pressure = self.feed_pressure_sp

                self.press_updated = False

            if self.state_updated:
                self.feed = self.feed_pid.update(
                    self.feed_sp,
                    self.feed_fb
                )

                self.rotation_speed = self.rot_pid.update(
                    self.rotation_speed_sp,
                    self.rotation_speed_fb if self.rotation_speed >=0 else -self.rotation_speed_fb # remove when have better tacho
                )
                self.state_updated = False

            if self.feed_pressure_sp < 0.001 and not self.raw_fp:  # free moving downwards
                self.feed_pressure = self.node_params['pdf_free_moving']
                self.press_pid.integral = 0
                self.press_pid.d_old = 0
                self.press_pid.out_old = 0

        else:
            self.press_pid.integral = 0
            self.press_pid.d_old = 0
            self.press_pid.out_old = 0
            self.press_pid.previous_time = rospy.get_time()
            self.rot_pid.integral = 0
            self.rot_pid.d_old = 0
            self.rot_pid.out_old = 0
            self.rot_pid.previous_time = rospy.get_time()
            self.feed_pid.integral = 0
            self.feed_pid.d_old = 0
            self.feed_pid.out_old = 0
            self.feed_pid.previous_time = rospy.get_time()
            self.stop_control()

        self.publish_control()

    def publish_control(self):
        message = DrillCtrl()
        message.header.stamp = rospy.get_rostime()

        message.feed_pressure = self.feed_pressure
        message.feed_speed = self.feed
        message.rotation_speed = self.rotation_speed
        message.rotation_torque = self.rotation_torque if self.rotation_torque > 0.001 else self.node_params['default_rot_torque']
        self.drill_act_pub.publish(message)

    def handle_internal_error(self, error_message=None, warning=False, err_type=None):
        # Останавливаем работу, обнуляя все скорости, если ошибка
        if not warning:
            self.stop_control()

        # Логгируем ошибку, если нужно
        if error_message is not None:
            loglevel = 2 if warning else 3

            if err_type is None:
                warning_num = self.global_params['Reports']['Warnings']['Internal_warning']
                error_num = self.global_params['Reports']['Errors']['Internal_error']
                err_type = warning_num if warning else error_num

            self.publish_alert_once(err_type, error_message, loglevel=loglevel)

    def stop_control(self):
        self.feed_pressure = 0
        self.feed = 0
        self.rotation_speed = 0

    def drill_state_callback(self, message):
        self.last_state_ts = message.header.stamp.to_sec()
        self.rotation_speed_fb = message.rotation_speed
        self.feed_fb = message.feed_speed
        self.state_updated = True

    def main_sm_drill_callback(self, message):
        if self.main_mode == CALIB or self.main_mode == POST_CALIB or self.main_mode == RESTORE_STRING:
            self.feed_pressure_sp = message.feed_pressure
            self.feed_sp = message.feed_speed
            self.rotation_speed_sp = message.rotation_speed
            self.rotation_torque = message.rotation_torque
            self.raw_fp = message.raw_fp
            self.last_sp_ts = rospy.get_time()

    def driller_callback(self, message):
        if self.main_mode == DRILLING:
            self.feed_pressure_sp = message.feed_pressure
            self.feed_sp = message.feed_speed
            self.rotation_speed_sp = message.rotation_speed
            self.rotation_torque = message.rotation_torque
            self.raw_fp = message.raw_fp
            self.last_sp_ts = rospy.get_time()


    def smart_drilling_callback(self, message):
        """
        Обрабатываем управление полученное в режиме ДУ.
        """
        if self.main_mode == REMOTE:
            if self.remote_type == SMART:
                self.feed_pressure_sp = message.feed_pressure
                self.feed_sp = message.feed_speed
                self.rotation_speed_sp = message.rotation_speed
                self.raw_fp = message.raw_fp
                self.last_sp_ts = rospy.get_time()
            else:
                self.stop_control()

    def rod_changer_callback(self, message):
        if self.main_mode == SHAFT_BUILDUP or self.main_mode == SHAFT_STOW:
            self.feed_pressure_sp = message.feed_pressure
            self.feed_sp = message.feed_speed
            self.rotation_speed_sp = message.rotation_speed
            self.raw_fp = message.raw_fp
            self.last_sp_ts = rospy.get_time()


    def pressure_callback(self, message):
        self.last_pressure_ts = message.header.stamp.to_sec()
        self.feed_pressure_fb = message.feed_pressure
        self.press_updated = True

    def main_mode_callback(self, message):
        self.main_mode = message.mode

    def permission_callback(self, message):
        if not self.move_permission or not message.permission:
            self.press_pid.integral = 0
            self.press_pid.previous_error = 0
            self.rot_pid.integral = 0
            self.rot_pid.previous_error = 0
            self.feed_pid.integral = 0
            self.feed_pid.previous_error = 0

        self.move_permission = message.permission

    def remote_type_callback(self, message):
        self.remote_type = message.type
