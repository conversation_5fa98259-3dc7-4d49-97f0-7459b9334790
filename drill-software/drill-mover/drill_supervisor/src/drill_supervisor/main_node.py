#!/usr/bin/env python
# -*- coding: utf-8 -*-

from collections import defaultdict
from threading import RLock

import rospy
import rosnode
from std_msgs.msg import String
from common.base_node import BaseNode
from common_msgs.msg import (
    InternalReport,
    MovingError,
    SystemStatus,
    MovePermission
)

from drill_msgs.msg import RemoteType, BoolStamped, EmergencyCtrl
from drill_msgs.srv import Bool

INITIALIZING_STATUS = 'initializing'
ERROR_STATUS = 'error'
WORKING_STATUS = 'working'


class DrillSupervisorNode(BaseNode):
    def initialize(self):
        # Начальный статус системы
        self.system_status = INITIALIZING_STATUS
        # Список топиков для отслеживания из параметров
        check_topics_params = self.node_params['topic_paramkeys_to_monitor']
        # Топики из глобального настроечного файла
        global_topics = rospy.get_param('Global/topics')
        # Словарь топиков, поставленных в соответствие топикам из настроек
        self._topics = [global_topics[topicparam] for topicparam in check_topics_params]
        # Паблишер состояния системы
        self._system_status_pub = rospy.Publisher(self.global_params['topics']['system_status'],
                                                  SystemStatus, queue_size=10)
        # Палблишер разрешения движения
        self._move_permission_pub = rospy.Publisher(self.global_params['topics']['permission'],
                                                    MovePermission, queue_size=10)

        # emergency pub
        self.emg_pub = rospy.Publisher(self.global_params['topics']['emergency_control'],
                                       EmergencyCtrl, queue_size=10)

        # Клиент для вызова сервиса ДУ у Главного Автомата
        self._remote_control_client = rospy.ServiceProxy('remote_control_super', Bool)

        # Список для типов репортов
        self.report_types_list = self.report_tolist()
        # Отправка статуса системы
        self.publish_system_status(self.system_status)
        # Лок для отчетов
        self.reports_lock = RLock()
        # Лок для отчетов-ошибок
        self.errors_lock = RLock()
        # Списки рабочих и нерабочих нод
        self.bad_nodes = []
        # Метка для отсчета времени поднятия системы
        self.init_time = rospy.Time.now()
        # Словарь для отслеживания состояния нод
        self.nodes_reports = {}
        self.nodes_error_reports = {}
        self.moving_error = None
        # Словарь для отслеживания состояния топиков
        self._topics = [global_topics[topicparam] for topicparam in check_topics_params]
        for topic_name in self.node_params['topics_to_monitor']:
            self._topics.append(topic_name)
        # self.log("topics: %s" % str(self._topics), loglevel=0)

        # Очередь полученных ошибок для отслеживания их частоты
        self.nodes_error_ts = defaultdict(list)
        # Флаг работы в ДУ
        self.in_remote = False
        self.pwm_enabled = False
        # Флаг ожидания ДУ
        self.waiting_remote = False
        self.monitor_to_restart_ts = {}
        self.last_restart_ts = {}

    def on_start(self):
        # Подписка на топик о типе ДУ
        rospy.Subscriber(self.global_params['topics']['remote_type'],
                         RemoteType, self.remote_callback)
        # Подписка на топик отчетов нод
        rospy.Subscriber(self.global_params['topics']['internal_report'],
                         InternalReport, self.report_callback)

        rospy.Subscriber(self.global_params['topics']['moving_error'],
                         MovingError, self.moving_error_callback)

        rospy.Subscriber(self.global_params['topics']['pwm_enabled'],
                         BoolStamped, self.pwm_status_cb)

        # Подписка но топики по списку отслеживаемых
        for topic in self._topics:
            rospy.Subscriber(topic, rospy.AnyMsg,
                             lambda msg, topic_name=topic: self._any_topic_callback(msg, topic_name))
            self.topics_init(topic)

        for topic in self.node_params['topics_to_restart_nodes'].keys():
            rospy.Subscriber(topic, rospy.AnyMsg,
                             lambda msg, topic_name=topic: self.monitor_to_restart_cb(msg, topic_name))
            self.topics_init(topic)

    def monitor_to_restart_cb(self, msg, topic_name):
        self.monitor_to_restart_ts[topic_name] = rospy.get_time()

    def topics_init(self, topic):
        report = InternalReport()
        report.source = topic
        report.header.stamp = rospy.get_rostime()
        report.type = self._report_types["Info"]["Initializing"]
        self.nodes_reports[topic] = report

    def report_tolist(self):
        """
        Перевод словаря типов репортов в список для удобства перебора в рабочем цикле
        """
        reports_list = []
        for key, value in self._report_types.items():
            for k, val in value.items():
                reports_list.append(val)

        return reports_list

    def _any_topic_callback(self, message, topic=''):
        """
        Коллбэк на любое сообщение в отслеживаемых топиках
        """

        report = InternalReport()
        report.source = topic
        report.header.stamp = rospy.get_rostime()
        report.type = self._report_types["Info"]["Working"]

        with self.reports_lock:
            self.nodes_reports[topic] = report

    def do_work(self):
        for topic in self.monitor_to_restart_ts.keys():
            if rospy.get_time() - self.monitor_to_restart_ts[topic] > self.node_params['max_report_time_delay']:
                node = self.node_params['topics_to_restart_nodes'][topic]
                if node in self.last_restart_ts.keys() and rospy.get_time() - self.last_restart_ts[node] < self.node_params['restart_lock_time']:
                    continue
                self.log("Node %s is dead or undead! Restarting.."%node, loglevel=2)
                self.last_restart_ts[node] = rospy.get_time()
                killed, _ = rosnode.kill_nodes([node])
                if node not in killed:
                    self.log("Can't kill node %s! Kill it with console"%node, loglevel=3)
                    self.publish_move_permission(False)


        """
        Разделение нод и топиков на работающие и неработающие, присвоение
        статуса системе на основе диагностики нод
        """
        # Обрабатываем ошибку движения если она есть
        if self.moving_error is not None:
            if self.moving_error.err_num != MovingError.BLOCKED_BY_PERMISSION:
                # self.log("Got moving error: stop")
                self.handle_critical()
            self.moving_error = None

        # Копирование отчетов нод через лок
        with self.reports_lock:
            items = self.nodes_reports.items()
        # Проверка отчетов нод и заполнение списков рабочих и нерабочих нод

        for node_name, report in items:
            if node_name in self.bad_nodes:
                self.bad_nodes.remove(node_name)

            if node_name not in self.node_params['special_timeouts'].keys():
                timeout  = self.node_params['max_report_time_delay']
            else:
                timeout = self.node_params['special_timeouts'][node_name]

            if report is None or report.type in self._report_types["Not_working"].values():
                self.bad_nodes.append(node_name)
                continue

            # Проверка типов сообщений по всем ключам в параметрах
            elif report.type not in self.report_types_list:
                self.alarm(node_name, report)
            elif report.type is not self._report_types["Info"]["Initializing"] and \
                    rospy.Time.now().to_sec() - report.header.stamp.to_sec() >= timeout:
                report.message = 'Time delay'
                self.alarm_time_delay(node_name, report)


            if len(self.bad_nodes) > 0:
                self.system_status = ERROR_STATUS
            else:
                self.system_status = WORKING_STATUS

        with self.errors_lock:
            for reports in self.nodes_error_reports.values():
                for report in reports:
                    self.handle_internal_error(report)

            self.nodes_error_reports = {}

        # Вывод нерабочих нод
        if len(self.bad_nodes) > 0:
            self.log("bad nodes: %s" % str(self.bad_nodes), loglevel=3)
            self.publish_move_permission(False)

        # Вывод рабочих нод
        self.publish_system_status(self.system_status)

    def handle_internal_error(self, message):
        """
        Вызывает дефолтный обработчик для переданного типа ошибки,
        если в конфиге не указано иначе.
        """
        node_name = message.source
        err_type = message.type // 100

        err_map = self.node_params['error_map']
        node_map = err_map.get(node_name)
        if node_map is not None:
            handle_as_type = node_map.get(str(err_type))
            if handle_as_type is not None:
                # self.log('Got type {} error from {}. Handling it as type {}'.format(err_type, node_name, handle_as_type), loglevel=2)
                err_type = handle_as_type

        def handle_warning():
            """
            Обработывает ошибки типа WARNING:
            Ничего не делает, ждет перевода машины в ДУ.
            """
            pass

        def handle_error():
            """
            Обрабатывает ошибки типа ERROR:
            Следит за частотой получения ошибки.
            В случае превышения заданного частотного порога
            останавливает машину.
            """
            if not self.waiting_remote and not self.in_remote:
                # print(len(self.nodes_error_ts[node_name]))
                if len(self.nodes_error_ts[node_name]) > self.node_params['max_errors_rate']:
                    # self.log('Error rate exceeded the limit')
                    self.handle_critical(ask_remote=False)

                for ts in self.nodes_error_ts[node_name]:
                    if rospy.Time.now().to_sec() - ts > self.node_params['error_rate_check_period']:
                        with self.errors_lock:
                            self.nodes_error_ts[node_name].remove(ts)
                    else:
                        break

        handler_map = {
            InternalReport.WARNING: handle_warning,
            InternalReport.ERROR: handle_error,
            InternalReport.CRITICAL: self.handle_critical,
            InternalReport.EMERGENCY: self.handle_emergency,
        }
        handler = handler_map.get(err_type)
        if handler is None:
            self.log('No handler for error of type {}'.format(err_type), 2)
            self.handle_critical()
            return
        handler()

    def handle_emergency(self):
        """
        Обрабатывает ошибки при которых надо жать кнопку экстренной остановки.
        """
        if self.pwm_enabled:
            self.log("EMERGENCY STOP!", loglevel=3)
            self.publish_move_permission(False)

            # msg = EmergencyCtrl()
            # msg.header.stamp = rospy.get_rostime()
            # msg.set = True
            # msg.reset = False
            # self.emg_pub.publish(msg)

    def handle_critical(self, ask_remote=True):
        """
        Обрабатывает критические ошибки и ошибки движения:
        Останавливает машину, запрашивает ДУ и ждет перехода в него.
        Если уже ждем ДУ или перешли в него, то ничего не делаем.
        """
        self.erase_stacked_error_ts()
        self.publish_move_permission(perm=False)
        if ask_remote and not self.waiting_remote and not self.in_remote:
            self.make_remote_request()

    def erase_stacked_error_ts(self):
        """
        Сбрасывает все накопленные ранее таймстемпы ошибок.
        """
        for node in self.nodes_error_ts.keys():
            with self.errors_lock:
                del self.nodes_error_ts[node][:]

    def moving_error_callback(self, message):
        """
        Обрабатывает ошибки типа MOVING:
        Те же действия, что и для CRITICAL.
        """
        # print('got moving err')
        self.moving_error = message

    def report_callback(self, message):
        """
        Сбор сообщений о состоянии нод.

        Если в сообщение передана ошибка типа ERROR -
        сохраняем его временную метку для последующего
        определения частоты прихода ошибки.
        """

        if self.is_internal_error(message):
            with self.errors_lock:
                if message.type in self._report_types["Errors"].values():
                    self.nodes_error_ts[message.source].append(message.header.stamp.to_sec())

                if message.source not in self.nodes_error_reports.keys():
                    self.nodes_error_reports[message.source] = [message]
                else:
                    self.nodes_error_reports[message.source].append(message)

        else:
            with self.reports_lock:
                self.nodes_reports[message.source] = message


    def is_internal_error(self, report):
        """
        Определяет является ли сообщение о состоянии ноды
        сообщением о системной ошибки.
        """
        if (
                report.type not in self._report_types["Info"].values() and
                report.type not in self._report_types["Not_working"].values()
        ):
            return True
        return False

    def pwm_status_cb(self, msg):
        self.pwm_enabled = msg.value

    def remote_callback(self, message):
        """
        Определяет находится ли машина в ДУ
        по сообщению о типе ДУ (или его отсутствие)
        """
        self.in_remote = message.type != message.NOT_REMOTE

        if self.waiting_remote and self.in_remote:
            self.waiting_remote = False

    def alarm(self, node, report):
        """
        Вызывается при обнаружении нерабочей ноды. Ставит статус системы в ошибку
        """
        # self.log_report(node, report, loglevel=3)
        self.bad_nodes.append(node)
        self.system_status = ERROR_STATUS
        # self.log("%s ALARM ERROR" % node, 3)

    def alarm_time_delay(self, node, report):
        """
        Вызывается при определении, что нода давно не отчитывалась, так же роняет систему
        """
        self.log_report(node, report, loglevel=3)
        self.bad_nodes.append(node)
        self.system_status = ERROR_STATUS
        self.log("%s ALARM TIME DELAY ERROR" % node, 3)

    def log_report(self, node, report, loglevel=1):
        """
        Предназначено для вывода причины смерти ноды
        """
        # self.log('%s node reports: "%s"' % (node, report.message), loglevel=loglevel)

    def publish_system_status(self, status):
        """
        Публикует статус системы
        """
        msg = SystemStatus()
        msg.header.stamp = rospy.Time.now()
        msg.status = str(status)
        if status == WORKING_STATUS:
            msg.type = self._report_types["Info"]["Working"]
        elif status == INITIALIZING_STATUS:
            msg.type = self._report_types["Info"]["Initializing"]
        elif status == ERROR_STATUS:
            msg.type = self._report_types["Errors"]["Internal_error"]
        self._system_status_pub.publish(msg)

    def publish_move_permission(self, perm=False):
        """
        Публикует разрешение движения машины
        """
        msg = MovePermission()
        msg.header.stamp = rospy.Time.now()
        msg.permission = perm
        self._move_permission_pub.publish(msg)

    def make_remote_request(self):
        """
        Запрашивает ДУ у Главного Автомата.
        """
        # self.log('Calling remote_control_super service', 2)

        rospy.wait_for_service('remote_control_super', timeout=None)
        try:
            res = self._remote_control_client()

            self.waiting_remote = True
            return res
        except rospy.ServiceException as e:
            self.log('Service call failed: {}'.format(e), 2)
