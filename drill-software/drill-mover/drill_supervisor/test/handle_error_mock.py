#!/usr/bin/env python
# -*- coding: utf-8 -*-

import threading
import time

RATE_CHECK_PERIOD = 7
ERRORS_RATE_MAX = 3

rate = 1  # errors per second

errors_ts_lock = threading.RLock()

errors_ts = []


def err_publisher():
	while True:
		with errors_ts_lock:
			errors_ts.append(time.time())

		if len(errors_ts) > ERRORS_RATE_MAX:
			print('CRITICAL: got {} errors'.format(len(errors_ts)))

		time.sleep(1/rate)


def remove_old_ts():
	while True:
		for ts in errors_ts:
			if time.time() - ts > RATE_CHECK_PERIOD:
				with errors_ts_lock:
					errors_ts.remove(ts)
			else:
				break


def main():
	pub_thread = threading.Thread(target=err_publisher, args=())
	rate_check_thread = threading.Thread(target=remove_old_ts, args=())

	rate_check_thread.start()
	pub_thread.start()


if __name__ == '__main__':
	main()

