#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
from common_msgs.msg import InternalReport, MovingError

rospy.init_node('test')

report_pub = rospy.Publisher(rospy.get_param('Global/topics/internal_report'),
                                            InternalReport, queue_size=10)

mov_err_pub = rospy.Publisher(self.global_params['topics']['moving_error'],
                                                MovingError, queue_size=10)

# Частота паблишинга
r = 1
rate = rospy.Rate(r)


def pub_internal_err():
    msg = InternalReport()
    msg.header.stamp = rospy.get_rostime()
    msg.source = 'TestNode'
    # msg.type = rospy.get_param('Global/Reports')['Errors']['Internal_error']
    msg.type = 9
    report_pub.publish(msg)


def pub_mov_err():
    msg = MovingError()
    msg.header.stamp = rospy.get_rostime()
    msg.err_num = MovingError.MANEUVER_CALC_ERROR

    mov_err_pub.publish(msg)


def main():
    while not rospy.is_shutdown():
        # pub_internal_err()
        pub_mov_err()

        rate.sleep()


if __name__ == '__main__':
    main()
