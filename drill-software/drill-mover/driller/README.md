# driller

Driller – конечный автомат, реализующий алгоритм бурения. 
Отслеживает скорость вращения, скорость подачи, давление в гидромоторе вращения, давление на забой. 
Управляет целевой скоростью вращения и подачи. 
Может задействовать люнет, следит за его своевременным открытием.


Подробная документация https://gitlab.com/vistmt/drill-docs-dev/-/blob/main/docs/onboard-system/drilling-and-mechanisms/driller.md?ref_type=heads