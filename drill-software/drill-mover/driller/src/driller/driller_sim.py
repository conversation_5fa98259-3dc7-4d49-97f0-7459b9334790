#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import rosparam
import time
import numpy as np
import scipy.stats as stats

from common.base_node import BaseNode
from common.geometry.geometry import norm_angle_0_2PI
from drill_msgs.msg import DrillState, PressureState, DrillCtrl


class DrillerSimNode(BaseNode):

    def __init__(self, name):
        super(DrillerSimNode, self).__init__(name)

        # Параметры машины
        # self.vehicle_params = rosparam.get_param('Vehicle')

        #self.random_seed = self.node_params['random_seed']
        self.random_seed = None

        self.hole_depth = 0

        self.tool_depth = (
                self.vehicle_params['shaft_len_params']['0']['lower_len'] +
                self.vehicle_params['drill_bit_len'] -
                self.vehicle_params['geometry']['platform_height']
        )

        self.spindle_depth = (
                self.vehicle_params['geometry']['tower_height'] -
                (
                        self.vehicle_params['shaft_len_params']['0']['middle_len'] +
                        self.vehicle_params['shaft_len_params']['0']['upper_len'] +
                        self.vehicle_params['extension_len']
                )
        )

        self.string_depth = -self.vehicle_params['geometry']['platform_height']

        np.random.seed(self.random_seed)
        self.ang_pose = np.random.uniform(0, 2 * np.pi)
        self.is_detached = False

        self.layers = {}

        self.passed_layers_depth = 0
        self.cur_layer_idx = 0

        # Число генерируемых слоев
        layers_num = 300

        # Генерируем толщину для каждого слоя из нормального распределения с
        # границами [upper, lower]
        mu = self.node_params['layer_thickness_mu']
        sigma = self.node_params['layer_thickness_sigma']
        lower = self.node_params['layer_thickness_lower_bound']
        upper = self.node_params['layer_thickness_upper_bound']
        np.random.seed(self.random_seed)
        layers_thickness = stats.truncnorm.rvs(
                            (lower - mu) / sigma,
                            (upper - mu) / sigma,
                            loc=mu,
                            scale=sigma,
                            size=layers_num
        )
        # Мин. значение коэфф. мягкости. Считается для мин. скорости бурния 1 м/час,
        # макс. давления подачи 25т. и скорости вращения 2 оборота/с
        min_drilling_speed = self.node_params['min_drilling_speed']
        max_feed_press = self.node_params['max_feed_pressure']
        max_rotation_speed = self.node_params['max_rotation_speed']
        min_softness_k = min_drilling_speed / (max_feed_press * max_rotation_speed)
        # Генерируем коэффициенты мягкости для всех слоев в пределах (0, 1]
        np.random.seed(self.random_seed)
        layers_softness = np.random.uniform(min_softness_k, np.nextafter(1, 2), size=layers_num)

        # Вероятность слоя замедления
        slowdown_prob = self.node_params['slowdown_layer_probability']
        # Флаги слоев замедления
        np.random.seed(self.random_seed)
        slowdown_flags = [
            1 if np.random.uniform(1, 0) < slowdown_prob else 0 for _ in range(layers_num)
        ]
        np.random.seed(self.random_seed)
        # Мин период времени между изменениями коэфф замедления на слое замедления
        self.min_time_delta_slowdown_change = self.node_params['min_time_delta_slowdown_change']
        self.last_slowdown_change = rospy.Time.now().to_sec()
        self.slowdown_change_prob = self.node_params['slowdown_change_prob']
        # Начальный коэффициентй замедления, будет обновляться в главном цикле
        self.slowdown_k = 1

        # Заполняем словарь с данными о слоях
        for idx, layer_data in enumerate(
                zip(layers_thickness, layers_softness, slowdown_flags)
        ):
            self.layers[idx] = {
                           'thickness': layer_data[0],
                           'softness': layer_data[1],
                           'is_slowdown': layer_data[2],
                           }

        # Мин период времени между возможными обвалами
        self.min_time_delta_landfall = self.node_params['min_time_delta_landfall']
        self.last_landfall = rospy.Time.now().to_sec()
        # Вероятность обвала
        self.landfall_prob = self.node_params['landfall_probability']
        # Мин. глубина на которой может генерироваться событие "обвал", строго больше длины инструмента
        self.min_landfall_depth = self.vehicle_params["drill_bit_len"] * 3
        self.min_landfall_thickness = 0.1
        self.max_landfall_thickness = self.min_landfall_depth - self.vehicle_params["drill_bit_len"]
        np.random.seed(self.random_seed)
        self.landfall_layer_softness = np.random.uniform(min_softness_k, np.nextafter(1, 2))
        # Толщина слоя обвала над инструментом и внизу скважины.
        # Будут обновляться в главном цикле при генерации обвала
        self.upper_landfall_layer_thickness = 0
        self.lower_landfall_layer_thickness = 0
        self.upper2lower_k = self.node_params['upper2lower_k']

        # Входы
        self.ctrl_feed_press = 0
        self.ctrl_rotation_speed = 0
        self.ctrl_feed_speed = 0

        # Выходы
        self.feed_speed = 0
        self.feed_press = 0
        self.rotation_speed = 0
        self.rotation_press = 0

        self.feed_press_k = self.node_params['feed_press_k']
        self.feed_speed_k = self.node_params['feed_speed_k']
        self.rot_speed_k = self.node_params['rot_speed_k']
        self.rot_speed2press_k = self.node_params['rot_speed2press_k']
        self.lf2rot_press_k = self.node_params['lf2rot_press_k']
        self.low_pass_k = self.node_params['low_pass_k']

        self.time_start = rospy.Time.now().to_sec()

        self.time_start_global = rospy.Time.now().to_sec()
        self.been_stopped = True

        self.state_pub = rospy.Publisher("/drill_state", DrillState, queue_size=1)
        self.pressure_pub = rospy.Publisher("/pressure_state", PressureState, queue_size=1)

        rospy.Subscriber("/drill_actuator", DrillCtrl, self.drill_callback)

    def drill_callback(self, message):
        self.ctrl_feed_press = message.feed_pressure
        self.ctrl_rotation_speed = message.rotation_speed
        self.ctrl_feed_speed = message.feed_speed

    def do_work(self):
        # Время одной иттерации
        delta_time = rospy.Time.now().to_sec() - self.time_start

        # Если инструмент (на предыдущей итерации) опустился ниже глубины скважины,
        # увеличиваем глубину скважины
        hole_depth_delta = max(0, self.tool_depth - self.hole_depth)
        self.hole_depth += hole_depth_delta
        if self.lower_landfall_layer_thickness > 0:
            self.lower_landfall_layer_thickness -= min(
                self.lower_landfall_layer_thickness, hole_depth_delta
            )

        depth_delta = self.feed_speed * delta_time
        self.spindle_depth += depth_delta
        if not self.is_detached:
            self.tool_depth += depth_delta

        # Для расчета углового положения штанги, переводим дельту из поворотов/секунду в радианы
        ang_delta = self.rotation_speed * delta_time * 2*np.pi
        self.ang_pose += ang_delta
        self.ang_pose = norm_angle_0_2PI(self.ang_pose)

        cur_layer = self.layers[self.cur_layer_idx]
        is_slowdown = bool(cur_layer['is_slowdown'])
        # Если находимся на слое замедления и прошло достаточно времени с прошлого изменения,
        # c заданой вероятностью может обновиться сила (коэффициент) замедления
        np.random.seed(self.random_seed)
        if is_slowdown:
            print("!! SLOWDOWN !!")
            if (rospy.Time.now().to_sec() - self.last_slowdown_change >=
                    self.min_time_delta_slowdown_change and
                    np.random.uniform(1, 0) < self.slowdown_change_prob):
                self.slowdown_k = np.random.uniform(0, np.nextafter(1, 2))
                self.last_slowdown_change = rospy.Time.now().to_sec()

        # Если глубина достаточно большая и прошло достаточно времени с предыдущего обвала,
        # с заданой вероятностью может генерироваться очередной обвал
        np.random.seed(self.random_seed)
        if (self.tool_depth >= self.hole_depth > self.min_landfall_depth and
                rospy.Time.now().to_sec() - self.last_landfall >= self.min_time_delta_landfall and
                np.random.uniform(1, 0) < self.landfall_prob):

            self.upper_landfall_layer_thickness += np.random.uniform(self.min_landfall_thickness,
                                                                     self.max_landfall_thickness)
            self.last_landfall = rospy.Time.now().to_sec()

        # Если над иструментом есть слой обвала и после движения инструмента
        # он вышел над уровнем земли, считаем вышедшую часть удаленной
        upper_lf_depth = self.tool_depth - (self.vehicle_params['drill_bit_len'] +
                                            self.upper_landfall_layer_thickness)
        if depth_delta < 0 < self.upper_landfall_layer_thickness and \
                upper_lf_depth < 0:
            self.upper_landfall_layer_thickness -= min(
                abs(upper_lf_depth), self.upper_landfall_layer_thickness
            )
        # Если же слой не вышел над уровнем земли, то его часть,
        # пропорциональная перемещению инструмента и
        # толщине слоя, осыпается на дно скважены
        elif depth_delta < 0 < self.upper_landfall_layer_thickness:
            upper2lower = min(abs(depth_delta) * self.upper2lower_k,
                              self.upper_landfall_layer_thickness,
                              self.hole_depth - self.tool_depth)
            self.upper_landfall_layer_thickness -= upper2lower
            self.lower_landfall_layer_thickness += upper2lower
            self.hole_depth -= upper2lower
        if self.lower_landfall_layer_thickness > 0:
            softness_k = self.landfall_layer_softness
        else:
            softness_k = cur_layer['softness'] * self.node_params['overall_softness_k']

        rotation_speed = (
                self.rotation_speed +
                (self.ctrl_rotation_speed * self.rot_speed_k - self.rotation_speed) *
                self.low_pass_k
        )
        if self.tool_depth >= self.hole_depth and self.ctrl_feed_speed > 0:
            self.feed_press = self.ctrl_feed_press * self.feed_press_k
        else:
            self.feed_press = 0

        if self.tool_depth >= self.hole_depth and is_slowdown:
            rot_dec = rotation_speed - rotation_speed * self.slowdown_k
            self.rotation_speed = rotation_speed * self.slowdown_k
        else:
            rot_dec = 0
            self.rotation_speed = rotation_speed

        rotation_pressure = self.rotation_speed * self.rot_speed2press_k + rot_dec * self.node_params['rot_dec2press_k']
        if self.upper_landfall_layer_thickness > 0:
            print("!! LANDFALL !!")
            self.rotation_press = (rotation_pressure +
                                   self.upper_landfall_layer_thickness * self.lf2rot_press_k)
        else:
            self.rotation_press = rotation_pressure

        if self.tool_depth >= self.hole_depth and self.ctrl_feed_speed > 0:
            self.feed_speed = self.feed_press * self.rotation_speed * softness_k
        else:
            self.feed_speed = self.ctrl_feed_speed * self.feed_speed_k

        if self.hole_depth > self.passed_layers_depth + cur_layer['thickness']:
            self.passed_layers_depth += cur_layer['thickness']
            self.cur_layer_idx += 1

        self.time_start = rospy.Time.now().to_sec()

        print("Layer #: {}".format(self.cur_layer_idx))
        print("Layer thickness: {}".format(cur_layer['thickness']))
        print("Spindle depth (from fork): {}".format(self.spindle_depth))
        print("Tool depth (from ground): {}".format(self.tool_depth))
        print("Hole depth: {}".format(self.hole_depth))
        print("Angular pose (degrees): {}".format(np.rad2deg(self.ang_pose)))
        print(" Upper landfall: {}".format(self.upper_landfall_layer_thickness))
        print(" Lower landfall: {}".format(self.lower_landfall_layer_thickness))
        print("Feed speed: {}".format(self.feed_speed))
        print("Feed press: {}".format(self.feed_press))
        print("Rotation speed: {}".format(self.rotation_speed))
        print("Rotation press: {}\n\n".format(self.rotation_press))

        # На заданной секунде один раз останавливаем симулятор, чтобы проверить
        # работу ноды с устаревшим стейтом.
        self.stop_sim(after_sec=5)

        # Паблишим
        d = DrillState()
        d.header.stamp = rospy.get_rostime()
        d.spindle_depth = self.spindle_depth
        d.rotation_speed = self.rotation_speed
        d.feed_speed = self.feed_speed
        d.angular_pose = self.ang_pose

        p = PressureState()
        p.header.stamp = rospy.get_rostime()
        p.rotation_pressure = self.rotation_press
        p.feed_pressure = self.feed_press

        self.state_pub.publish(d)
        self.pressure_pub.publish(p)

        self.time_start = time.time()

    def stop_sim(self, after_sec):
        if not self.been_stopped and rospy.Time.now().to_sec() - self.time_start_global >= after_sec:
            self.been_stopped = True
            raw_input('Press Enter to continue...')
