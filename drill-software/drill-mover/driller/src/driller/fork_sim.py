#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import time
import roslib.message as message
import yaml

from drill_msgs.msg import ForkSwitchState, FloatStamped

ts = time.time()

rospy.init_node("fork_sim")

max_len = 0.3
min_len = 0
cur_len = 0.3

ctrl2speed = 0.1
ctrl = 0
rear_switch_on = False
front_switch_on = False

# Частота паблишинга
r = 30
rate = rospy.Rate(r)


def ctrl_callback(msg):
    global ctrl
    ctrl = msg.value


state_pub = rospy.Publisher("/fork_switch_state", ForkSwitchState, queue_size=1)
rospy.Subscriber("/fork_control", FloatStamped, ctrl_callback)

while not rospy.is_shutdown():
    # Время одной иттерации
    delta_time = rospy.get_rostime().to_sec() - ts
    print('fork_len: {}'.format(cur_len))
    print('front_switch_on: {}'.format(front_switch_on))
    print('rear_switch_on: {}\n'.format(rear_switch_on))

    cur_len += ctrl * ctrl2speed * delta_time
    if cur_len >= max_len:
        cur_len = max_len
        front_switch_on = True
    elif cur_len < max_len:
        front_switch_on = False

    if cur_len > min_len:
        rear_switch_on = False
    elif cur_len <= min_len:
        cur_len = min_len
        rear_switch_on = True

    s = ForkSwitchState()
    s.header.stamp = rospy.get_rostime()
    s.rear_switch_on = rear_switch_on
    s.front_switch_on = front_switch_on
    state_pub.publish(s)

    s = message.strify_message(s)
    s = yaml.load(s)
    print(s.keys()[0] == 'header')

    ts = time.time()
    rate.sleep()
