#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import json
import rosparam

from .models import *

from .constants import *

from std_msgs.msg import String
from state_machine_node import AbstractNodeStateMachine
from common_msgs.msg import Action, MovePermission, MovingError
from drill_msgs.srv import SetStringDepth, Bool, SendNewHole
from drill_msgs.msg import (StateMachineStatus, InternalError, VehicleBehaviorMode,
                            ManualState, DrillState, ForkSwitchState, PressureState, VibrationArray,
                            DrillCtrl, FloatStamped, BoolStamped, CompressorCtrl)

from main_state_machine.constants import REMOTE, REMOTE_WAIT, REMOTE_PREPARE, REMOTE_WAIT
from copy import deepcopy


class Dict2Obj(object):
    def __init__(self, dictionary):
        for key, value in dictionary.items():
            if isinstance(value, (list, tuple)):
                setattr(self, key, [Dict2Obj(x) if isinstance(x, dict) else x for x in value])
            else:
                setattr(self, key, Dict2Obj(value) if isinstance(value, dict) else value)

    def to_dict(self):
        d = {}
        for a, b in self.__dict__.items():
            if isinstance(b, Dict2Obj):
                d[a] = b.to_dict()
            elif isinstance(b, list):
                d[a] = [x.to_dict() if isinstance(x, Dict2Obj) else x for x in b]
            else:
                d[a] = b
        return d

    def __repr__(self):
        return str(self.to_dict())  # for readable output

def deep_update(original, update):
    """
    Recursively update dictionary original with entries from update.
    """
    for key, value in update.items():
        if isinstance(value, dict):
            original[key] = deep_update(original.get(key, {}), value)
        else:
            original[key] = value
    return original


def dict_diff(d1, d2, context=''):
    """
    Prints messages about keys that differ between two configurations.

    Args:
        d1: First dictionary to compare.
        d2: Second dictionary to compare.
        context: Keeps track of the nested keys. Used for recursive calls.
    """
    for key in d1.keys():
        # If key is not found in d2, then it's removed in d2
        if key not in d2:
            print("Key removed: %s" % (context + key))
        elif isinstance(d1[key], dict) and isinstance(d2[key], dict):
            # Recursive call to check nested dictionaries
            dict_diff(d1[key], d2[key], context + key + '.')
        # If value of the key has changed
        elif d1[key] != d2[key]:
            print("Key changed: %s" % (context + key))

    for key in d2.keys():
        # If key is not found in d1, then it's added in d2
        if key not in d1:
            print("Key added: %s" % (context + key))


class DrillControls:
    def __init__(self):
        self.feed_speed = 0
        self.rotation_speed = 0
        self.feed_pressure = 0
        self.air_power = 0
        self.dust_collector_on = False

    def reset(self):
        self.feed_speed = 0
        self.rotation_speed = 0
        self.feed_pressure = 0
        self.air_power = 0
        self.dust_collector_on = False


class DrillerNode(AbstractNodeStateMachine):

    def drill_initialize(self, *args, **kwargs):

        self.set_current_state(IdleState(self))

        self.add_states([TouchDownState(self), OverburdenPassState(self), DrillingState(self),
                         HardRotState(self), PullUpState(self), AfterPullUpState(self),
                         RaiseState(self), WaitAfterDrillState(self), PassSoftState(self), UnstuckState(self)])

        # Hole and action info
        self.hole_id = None
        self.hole_angle = 0
        self.hole_target_depth = None

        self.action = None
        self.new_action_flag = False
        self.first_shaft_flag = False
        self.last_shaft_flag = False
        self.target_drill_spindle_depth = None
        self.target_raise_spindle_depth = None

        # Timers
        self.last_high_vibration_time = 0

        self.last_normal_rotation_time = None
        self.last_nostuck_move_time = rospy.get_time()
        self.last_normal_rp_time = rospy.get_time()
        self.last_unstuck_time = 0

        self.first_air_is_bad_time = 0
        self.last_air_check_time = 0

        self.last_valid_depth_time = rospy.get_time()
        self.last_air_is_nominal_time = rospy.get_time()

        self.sent_arm_time = 0

        # Управление для отправки
        self.controls = DrillControls()

        # Drilling Flags
        self.is_stuck = False
        self.is_hard_rot = False

        self.sent_arm_open = False
        self.sent_arm_close = False

        # Counters
        self.short_pullup_cnt = 0

        # vars
        self.drill_speed_smoothed = None

        # ground_spindle_depth estimation based on geometry
        shaft_id = self.vehicle_params['shaft_list'][0]
        shaft_len_params = self.vehicle_params['shaft_len_params'][shaft_id]
        shaft_len = shaft_len_params['full_len']
        drill_bit_len = self.vehicle_params['drill_bit_len']
        extension_len = self.vehicle_params['extension_len']
        string_len = shaft_len + drill_bit_len + extension_len
        tower_height = self.vehicle_params['geometry']['tower_height']
        self.ground_spindle_depth = (tower_height
                                     - string_len
                                     + self.vehicle_params['geometry']['platform_height']
                                     + self.vehicle_params['geometry']['average_level_height'])

        self.action_start_spindle_depth = None
        self.nominal_air_pres = None

        # water save
        self.saved_water_ctrl = 0
        self.saved_water_restored = True
        self.last_sent_water = 0

        self.last_water_ctrl_external = FloatStamped()

        # hardware configuration flags
        self.arm_present = self.global_params['system_flags']['arm_present']
        self.use_rotation_speed_sensor = self.global_params['system_flags']['use_rotation_speed_sensor']

        # ROS control publishers
        self.drill_ctrl_pub = rospy.Publisher(self.global_params['topics']['driller_out'],
                                              DrillCtrl, queue_size=10)

        self.arm_action_pub = rospy.Publisher(self.global_params['topics']['arm_action'],
                                              Action, queue_size=10)

        self.compressor_ctrl_pub = rospy.Publisher(
            self.global_params['topics']['compressor_control'],
            CompressorCtrl, queue_size=10)

        self.dust_collector_control_pub = rospy.Publisher(
            self.global_params['topics']['dust_collector_control'],
            BoolStamped, queue_size=10)

        self.water_control_pub = rospy.Publisher(
            self.global_params['topics']['water_injection_auto'],
            FloatStamped, queue_size=10)

        rospy.Service('recalibrate_air', Bool, self.manual_recalib_air)

        # active parameters, starts with default and gets updated based on the applied profile
        self.current_params = Dict2Obj(self.node_params['default'])
        # used in drill_mode_callback to detect changes
        self.last_profiles = []

        # last depth from ground of pullup for current hole (to not repeate mandatory pullups done),
        # must be reset for new hole
        self.last_mandatory_pullup_spindle_depth = 0

    def subscribers_initialize(self):

        self.add_subscriber(self.global_params['topics']['drill_actuator'],
                            DrillCtrl, 'drill_ctrl')

        self.add_subscriber(self.global_params['topics']['water_injection_control'],
                            FloatStamped, 'water_ctrl')

        self.add_subscriber(self.global_params['topics']['arm_controller_status'],
                            StateMachineStatus, 'arm_status')

        self.add_subscriber(self.global_params['topics']['main_mode'],
                            VehicleBehaviorMode, 'main_mode')

        self.add_subscriber(self.global_params['topics']['vibration_state'],
                            VibrationArray, 'vibration_state')

        self.add_subscriber(self.global_params['topics']['pressure_state'],
                            PressureState, 'pressure_state')

        self.add_subscriber(self.global_params['topics']['drill_state'],
                            DrillState, 'drill_state')

        self.add_subscriber(self.global_params['topics']['rmo_rock_type'], String, 'rmo_rock_type')
        self.add_subscriber(self.global_params['topics']['rmo_hole_water'], String, 'rmo_hole_water')

        rospy.Subscriber(self.global_params['topics']['driller_action'],
                         Action, self.action_callback)

        rospy.Subscriber(self.global_params['topics']['water_injection_control'],
                         FloatStamped, self.water_ctrl_callback)

    def initialize_params(self):
        """
        Overridden to prevent the default setting parameters as attributes.
        Parameters remain in `self.node_params`.
        """
        if hasattr(self, 'current_params') and hasattr(self, 'last_profiles'):
            self.log("Driller: reinitialize params")
            self.current_params = Dict2Obj(self.node_params['default'])
            self.apply_profiles(self.last_profiles)
            self.print_current_params()

        pass

    def action_callback(self, message):
        # Если номер нового экшена больше предыдущего и все необходимые поля в
        # data заполнены в верном формате - сохраняем экшен
        if not self.new_action_flag:
            if self.get_current_state().get_name() == IDLE:
                if message.seq > self._last_action_seq:
                    try:
                        read_json = json.loads(message.data)

                        # Проверяем, что в Action.data заполнены все необходимые данные
                        required_keys = (
                            'drill_spindle_depth',
                            'raise_spindle_depth',
                            'first_shaft_flag',
                            'last_shaft_flag',
                            'hole_depth',
                            'tower_angle',
                            'id'
                        )
                        for k in required_keys:
                            if k not in read_json:
                                error_descr = "Action data is incomplete or invalid"
                                self.handle_internal_error(error_descr)
                                return

                        # Все ок - сохраняем задание
                        self.action = message
                        self.new_action_flag = True
                        self.target_drill_spindle_depth = read_json['drill_spindle_depth']
                        self.target_raise_spindle_depth = read_json['raise_spindle_depth']
                        self.first_shaft_flag = read_json['first_shaft_flag']
                        self.last_shaft_flag = read_json['last_shaft_flag']
                        self.hole_angle = read_json['tower_angle']
                        self.hole_target_depth = read_json['hole_depth']
                        self.hole_id = read_json['id']
                        self._cur_action_seq = message.seq
                        self._last_action_seq = message.seq
                        if self.first_shaft_flag:
                            self.ground_spindle_depth = self.target_raise_spindle_depth  # use this until we found real ground

                    except ValueError:
                        error_descr = "Action data is not in valid json format"
                        self.handle_internal_error(error_descr)
                else:
                    error_descr = "Action.seq is not set or invalid"
                    self.handle_internal_error(error_descr)
            else:
                error_descr = "Can't process new action while not in IDLE"
                self.handle_internal_error(error_descr)

        else:
            error_descr = "New action while old one was not finished"
            self.handle_internal_error(error_descr)

    def print_current_params(self):
        """Pretty-prints the current parameters."""
        formatted_params = json.dumps(self.current_params.to_dict(), indent=4)
        self.log("Current Parameters:\n" + formatted_params)

    def process_profiles(self):
        """
        Applying the necessary profile based on last messages from rmo_rock_type and rmo_hole_water
        and hole short or long type
        """
        profiles = []

        # Add profile based on RMO input (rock type and hole water)
        if self.subs.rmo_rock_type and self.subs.rmo_rock_type.data:
            profiles.append(self.subs.rmo_rock_type.data)
        if self.subs.rmo_hole_water and self.subs.rmo_hole_water.data:
            profiles.append(self.subs.rmo_hole_water.data)

        # if current hole is short
        if self.is_short_hole():
            # self.log("is short hole")

            # Apply 'default-short' profile first for short holes
            if 'default-short' not in profiles:
                profiles.insert(0, 'default-short')

            # Convert all profiles in the list to their "-short" counterparts if exist
            profiles = [profile if profile + '-short' not in self.node_params["profiles"]
                        else profile + '-short' for profile in profiles]
        # else:
        #     self.log("NOT short hole")

        # self.log("profiles '%s'" % str(profiles))
        # self.log("last_profiles '%s'" % str(self.last_profiles))

        # Check if the profiles are different from the last ones
        if profiles != self.last_profiles:
            self.log("Applying changed profiles: '%s'" % str(profiles))
            # self.print_current_params()
            if self.apply_profiles(profiles):  # Only update self.last_profiles if apply_profiles was successful
                self.last_profiles = profiles
        # else:
        #     self.log("Received same profiles as before, skipping re-application.")

    def is_short_hole(self):
        if self.hole_target_depth is not None and self.current_params.common.short_hole_max_depth is not None:
            return self.hole_target_depth < self.current_params.common.short_hole_max_depth
        else:
            return False  

    def apply_profiles(self, profiles):
        """
        Apply the specified profiles to the node's parameters.

        :param profiles: List of profile names to be applied.
        """

        profiles_to_apply = deepcopy(profiles)

        def check_new_keys(base_dict, profile_dict, prefix=""):
            """Recursively check if profile_dict contains keys not in base_dict."""
            for key, value in profile_dict.items():
                if key not in base_dict:
                    self.log("Warning: Parameter '%s' from profile '%s' does not exist in defaults." %
                             (prefix + key, profile_name), 2)
                elif isinstance(value, dict) and isinstance(base_dict[key], dict):
                    # If the value is another dict and the corresponding key in the base_dict is also a dict
                    check_new_keys(base_dict[key], value, prefix + key + ".")

        current_params = deepcopy(self.node_params['default'])
        for profile_name in profiles_to_apply:
            self.log("Applying profile '%s'" % profile_name)
            if profile_name in self.node_params["profiles"]:
                profile_params = self.node_params["profiles"][profile_name]

                # (just for debug print) Check if there are new keys in the profile that are not in default
                check_new_keys(current_params, profile_params)
                # (just for debug print) copy to calc differences
                params_before = deepcopy(current_params)

                # Merge the profile params into current_params
                current_params = deep_update(current_params, profile_params)

                # (just for debug print) Calculate the differences
                # dict_diff(params_before, current_params)
            else:
                self.log("No profile '%s' in known profiles!" % profile_name, 3)
                return False  # The profile isn't known

        # Set self.current_params with the merged params TODO: rwlock
        self.current_params = Dict2Obj(current_params)
        self.log("apply_profiles finished")
        # self.print_current_params()
        return True  # Profiles were applied successfully

    def water_ctrl_callback(self, msg):
        if abs(msg.value - self.last_sent_water) > 1e-3:
            if abs(self.last_water_ctrl_external.value - msg.value) > 1e-3:
                self.last_water_ctrl_external = msg
                self.log(f"New external water control {msg.value} detected. Last driller control was {self.last_sent_water}")


    def set_water(self, value, force_send=False, remember=True):
        if self.last_sent_water == value and not force_send:
            return

        msg = FloatStamped()
        msg.header.stamp = rospy.get_rostime()
        msg.value = value
        self.water_control_pub.publish(msg)
        if remember:
            self.last_sent_water = value

    def open_arm(self):
        self.publish_arm_action(do_open=True)
        self.sent_arm_open = True

    def close_arm_func(self):
        self.publish_arm_action(do_open=False)
        self.sent_arm_close = True

    def publish_arm_action(self, do_open=False):
        message = Action()
        message.data = json.dumps({'open': do_open})
        message.seq = 1
        self.arm_action_pub.publish(message)
        self.sent_arm_time = rospy.get_time()

    def zero_ctrl(self):
        self.controls.reset()

    def stop_control(self):
        self.zero_ctrl()
        self.send_control()

    def manual_recalib_air(self, req=None):
        if self.get_current_state().get_name() not in [IDLE, FAILURE]:
            self.nominal_air_pres = self.subs.pressure_state.air_pressure
            self.log("Nominal air pressure set to %.2f bar" % self.nominal_air_pres)
        return True

    def is_in_hole(self, max_distance = 0.0):
        spindle_depth = self.subs.drill_state.spindle_depth
        if spindle_depth < 0.1 + max_distance:
            return False
        if self.ground_spindle_depth is None:
            self.log("Ground level not initialized", loglevel=2)
            return False
        if self.first_shaft_flag and spindle_depth - self.ground_spindle_depth < max_distance:
            return False
        return True

    def check_vibration(self):
        u"""
        Обработка случая повышенной вибрации
        """
        if not self.current_params.common.check_vibrations:
            return

        too_high_vibration = False
        vibration_limits = self.current_params.common.vibration_limits
        for v in self.subs.vibration_state.vibrations:
            for i in range(len(vibration_limits) - 1):
                if (vibration_limits[i].frequency < v.frequency < vibration_limits[i + 1].frequency and
                        v.amplitude > vibration_limits[i].amplitude):
                    too_high_vibration = True
                    break

        if too_high_vibration:
            if rospy.get_time() - self.last_high_vibration_time > self.current_params.common.high_vibration_duration_threshold:
                self.log("Detected high vibrations!", loglevel=2)
            self.last_high_vibration_time = rospy.get_time()

    def check_air_is_nominal(self):
        if (self.subs.pressure_state.air_pressure > self.current_params.common.max_air_pressure) or \
                (self.nominal_air_pres is not None and self.subs.pressure_state.air_pressure > self.nominal_air_pres
                 + self.current_params.common.max_air_pressure_excess):
            return False
        return True

    def check_air(self):
        current_time = rospy.get_time()

        compressor_enabled = (self.controls.air_power > 0) and self.get_move_permission()
        air_is_ok = (compressor_enabled and self.subs.pressure_state.air_pressure > 0.7) or \
                    (not compressor_enabled and self.subs.pressure_state.air_pressure < 0.3)

        if air_is_ok:
            self.first_air_is_bad_time = 0
        else:
            # If this is the first instance of bad air pressure
            if not self.first_air_is_bad_time:
                self.first_air_is_bad_time = current_time

            # If we missed an air check cycle
            elif current_time - self.last_air_check_time > 2 * (1.0 / self.node_params['rate']):
                self.first_air_is_bad_time = min(self.first_air_is_bad_time, current_time)

            # If bad air pressure persists beyond the acceptable duration, raise a compressor malfunction error
            elif current_time - self.first_air_is_bad_time > self.current_params.common.air_transient_response_time:
                err_msg = "Compressor malfunction!"
                err_type = self.global_params['Reports']['Critical']['Hardware_malfunction']
                self.handle_internal_error(error_message=err_msg,
                                           error_type=err_type,
                                           event=self.global_params['events']['compressor_failure'])
        self.last_air_check_time = current_time

    def check_if_pullup_needed(self):
        current_time = rospy.get_time()

        if self.check_air_is_nominal():
            self.last_air_is_nominal_time = current_time
        elif current_time - self.last_air_is_nominal_time > self.current_params.common.air_not_nominal_max_time:
            self.log("Too high air pressure! Need to pullup", loglevel=2)
            return True

        return False

    def set_safe_rotation(self, value=None):
        max_speed = self.current_params.common.max_rotation_speed
        reduced_speed = self.current_params.common.reduced_rotation_speed

        # Ensure reduced rotation is less than max rotation speed
        if reduced_speed > max_speed:
            reduced_speed = max_speed
            self.log("Configuration error: Reduced rotation speed should be less than max rotation speed.",
                     loglevel=2)

        if value is None or value > max_speed:
            value = max_speed

        self.check_vibration()

        time_since_high_vibration = rospy.get_time() - self.last_high_vibration_time
        is_low_vibration = time_since_high_vibration > self.current_params.common.high_vibration_duration_threshold

        self.controls.rotation_speed = value if is_low_vibration else reduced_speed

    def check_and_update_depth_data(self):
        """
        Check the validity of the depth data and update the smoothed drill speed.
        """
        # If depth data is valid
        if self.subs.drill_state.is_valid:
            self.last_valid_depth_time = rospy.get_time()

            # Update smoothed drill speed
            if self.drill_speed_smoothed is None:
                self.drill_speed_smoothed = self.subs.drill_state.feed_speed
            else:
                self.drill_speed_smoothed += (self.subs.drill_state.feed_speed - self.drill_speed_smoothed) * 0.1
            return True  # Data is valid and successfully updated

        # If depth data is not valid
        else:
            elapsed_since_valid = rospy.get_time() - self.last_valid_depth_time
            cur_state_name = self.get_current_state().get_name()

            # Determine the max invalid time based on drilling state
            if cur_state_name in [IDLE, DRILLING, AFTER_PULLUP, PASS_SOFT]:
                max_invalid_time = self.current_params.common.invalid_depth_duration_drill
            else:
                max_invalid_time = self.current_params.common.invalid_depth_duration

            if elapsed_since_valid < max_invalid_time:
                # Data is invalid but within acceptable threshold
                self.log("INVALID DEPTH DATA", loglevel=2, event=self.global_params['events']['laser_failure'])
                return True
            else:
                self.drill_speed_smoothed = None
                self.log("INVALID DEPTH DATA", loglevel=3, event=self.global_params['events']['laser_failure'])
                return False  # Data is invalid and drill speed reset

    def check_for_stuck(self):
        common_params = self.current_params.common
        current_time = rospy.get_time()

        # Reset criteria
        if not self.is_in_hole(0.1) \
                or self.subs.main_mode.mode != DRILLING:
            self.is_stuck = False
            self.is_hard_rot = False
            self.last_normal_rotation_time = rospy.get_time()
            self.last_nostuck_move_time = rospy.get_time()
            return

        # Check rotation stuck
        stuck_criteria_rot = (self.use_rotation_speed_sensor
                              and abs(self.subs.drill_ctrl.rotation_speed) > 0.95
                              and abs(self.subs.drill_state.rotation_speed) < common_params.too_low_rotation_speed)
        if not stuck_criteria_rot:
            self.last_normal_rotation_time = current_time

        # Check movement stuck
        stuck_criteria_move = (self.subs.drill_ctrl.feed_speed < -0.95
                               and self.subs.drill_state.feed_speed > -common_params.stuck_speed_threshold)
        if not stuck_criteria_move:
            self.last_nostuck_move_time = current_time

        # Check for high rotation pressure
        high_rp = self.subs.pressure_state.rotation_pressure > common_params.too_high_rp_threshold
        if not high_rp:
            self.last_normal_rp_time = current_time

        # Detect hard rotation
        has_long_no_rotation = current_time - self.last_normal_rotation_time >= common_params.no_rotation_duration
        has_long_high_rotation_pressure = current_time - self.last_normal_rp_time > common_params.high_rp_duration
        self.is_hard_rot = has_long_no_rotation or has_long_high_rotation_pressure

        # Detect long no movement
        has_long_no_move = current_time - self.last_nostuck_move_time > common_params.stuck_move_duration

        # Detect stuck
        allow_stuck_detection = self.get_current_state_duration() > common_params.min_state_duration_to_stuck and \
                                current_time - self.last_unstuck_time > common_params.ignore_stuck_duration
        if allow_stuck_detection and (has_long_no_move or self.is_hard_rot):
            self.is_stuck = True
        else:
            self.is_stuck = False

    def manage_arm(self):
        common_params = self.current_params.common
        current_state_name = self.get_current_state().get_name()

        # Return value which tells if string movement should be denied
        deny_movement = False

        # Reset arm state flags based on arm status
        if self.subs.arm_status is not None:
            if self.subs.arm_status.status == CLOSED:
                self.sent_arm_close = False
            elif self.subs.arm_status.status == OPEN:
                self.sent_arm_open = False
        else:
            self.logwarn("arm_status is None")

        # arm open/close delays
        time_since_last_arm_action = rospy.get_time() - self.sent_arm_time
        if self.sent_arm_open and time_since_last_arm_action > 5.0:
            self.sent_arm_open = False
        if self.sent_arm_close and time_since_last_arm_action > 5.0:
            self.sent_arm_close = False

        # Check if not in IDLE or remote
        if current_state_name != IDLE and 'remote' not in self.subs.main_mode.mode:
            need_to_close_arm = (common_params.close_arm and current_state_name == RAISE) or self.hole_angle != 0

            # Decide on arm action based on depth and other conditions
            if self.subs.drill_state.spindle_depth < common_params.arm_close_depth and not self.sent_arm_close:
                if need_to_close_arm and self.arm_present and self.subs.arm_status.status != CLOSED:
                    self.close_arm_func()
            elif self.subs.drill_state.spindle_depth > common_params.arm_open_depth and not self.sent_arm_open:
                if self.subs.arm_status.status != OPEN:
                    self.open_arm()

            # Conditions to deny string movement
            if ((
                    self.subs.drill_state.spindle_depth < common_params.arm_close_min_depth and self.subs.arm_status.status != CLOSED and need_to_close_arm) or
                    (
                            self.subs.drill_state.spindle_depth > common_params.arm_open_max_depth and self.subs.arm_status.status != OPEN)):
                deny_movement = True
                self.logwarn("Feed locked while rod support in wrong state", period=2,
                             event=self.global_params['events']['wrong_arm_state'])

        return deny_movement

    def check_data(self):
        current_state_name = self.get_current_state().get_name()

        # Check if need to update profile
        if current_state_name != IDLE:
            self.process_profiles()

        # check depth data
        if not self.check_and_update_depth_data():
            return False

        self.check_for_stuck()

        if self.subs.main_mode is None:
            return False

        # Check if we're in IDLE state or remote modes
        if current_state_name == IDLE \
                or self.subs.main_mode.mode in (REMOTE, REMOTE_WAIT, REMOTE_PREPARE, REMOTE_WAIT):
            return True

        # arm management
        self.manage_arm()

        return True

    def send_control(self, deny_string_movement=True):
        message = DrillCtrl()
        message.header.stamp = rospy.Time.now()
        message.feed_speed = self.controls.feed_speed if not deny_string_movement else 0
        message.feed_pressure = self.controls.feed_pressure
        message.rotation_speed = self.controls.rotation_speed
        message.raw_fp = 0 < self.controls.feed_pressure <= 1

        self.drill_ctrl_pub.publish(message)

        message = CompressorCtrl()
        message.header.stamp = rospy.Time.now()
        message.turned_on = self.controls.air_power > 0
        message.power = self.controls.air_power
        self.compressor_ctrl_pub.publish(message)

        message = BoolStamped()
        message.header.stamp = rospy.Time.now()
        message.value = self.controls.dust_collector_on
        self.dust_collector_control_pub.publish(message)

    def do_work_after(self):
        if self.get_current_state().get_name() != IDLE and 'remote' not in self.subs.main_mode.mode and self.get_robomode():
            self.check_air()
        else:
            self.first_air_is_bad_time = rospy.get_time()

        # check arm
        deny_string_movement = self.manage_arm()

        # send control
        self.send_control(deny_string_movement)

    def do_work_failed(self):
        self.stop_control()
