#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
from state_machine_node import AbstractState
from .constants import ID<PERSON>, TOUCHDOWN, OVERBURDEN_PASS, DRILLING, HARD_ROT, PULLUP, AFTER_PULLUP, \
    RAISE, WAIT_AFTER_DRILL, PASS_SOFT, UNSTUCK


class IdleState(AbstractState):
    def __init__(self, node):
        super(IdleState, self).__init__(node, IDLE)

    def on_transition_to(self):
        self.node._cur_action_seq = -1
        self.node.new_action_flag = False
        self.node.set_water(0, force_send=True)
        self.node.last_mandatory_pullup_spindle_depth = None

    def do_work(self):
        self.node.zero_ctrl()
        self.node.short_pullup_cnt = 0

        # state transitions on new action
        if self.node.new_action_flag:
            if not self.node.subs.drill_state.is_valid:
                self.node.log("Drill state is not valid in IDLE after new action received, waiting", loglevel=2)
                return
            else:
                self.node.action_start_spindle_depth = self.node.subs.drill_state.spindle_depth
                self.node.log("Set action start spindle depth to %.2f" % self.node.action_start_spindle_depth)

            if self.node.first_shaft_flag:
                self.node.set_state(TOUCHDOWN)
            else:

                self.node.set_state(DRILLING)


class TouchDownState(AbstractState):
    def __init__(self, node):
        super(TouchDownState, self).__init__(node, TOUCHDOWN)
        self.ground_detect_time_started = rospy.get_time()
        self.entry_spindle_depth = 0

    def on_transition_to(self):
        if self.node.get_last_state().get_name() == IDLE:
            self.entry_spindle_depth = self.node.subs.drill_state.spindle_depth

        self.ground_detect_time_started = rospy.get_time()

    def do_work(self):
        # references to the params from node's current_params; might be updated based on the current profile
        touchdown_params = self.node.current_params.touchdown
        common_params = self.node.current_params.common

        # controls
        self.node.controls.feed_speed = touchdown_params.feed_speed_ctrl
        self.node.controls.rotation_speed = touchdown_params.rotation_speed_ctrl
        self.node.controls.air_power = touchdown_params.air_power_ctrl
        self.node.controls.dust_collector_on = touchdown_params.dust_ctrl
        self.node.controls.feed_pressure = touchdown_params.feed_pressure_ctrl_raw

        # ground level detection
        if self.node.subs.drill_state.feed_speed > common_params.ground_detect_feed_speed_thr:
            self.ground_detect_time_started = rospy.get_time()

        # state transition
        elif rospy.get_time() - self.ground_detect_time_started > common_params.ground_detect_duration and \
                self.node.subs.drill_state.spindle_depth - self.entry_spindle_depth >= touchdown_params.depth_delta and \
                self.node.subs.drill_ctrl.feed_speed > 0.3:

            if not self.node.subs.drill_state.is_valid:
                self.node.log("Drill state is not valid in TOUCHDOWN "
                              "while trying to set ground_spindle_depth, waiting", loglevel=2)
            else:
                self.node.ground_spindle_depth = self.node.subs.drill_state.spindle_depth
                self.node.log("Set ground spindle depth to %.2f" % self.node.ground_spindle_depth)
                self.node.set_state(OVERBURDEN_PASS)


class OverburdenPassState(AbstractState):
    def __init__(self, node):
        super(OverburdenPassState, self).__init__(node, OVERBURDEN_PASS)
        self.entry_spindle_depth = 0

    def on_transition_to(self):
        if self.node.get_last_state().get_name() == TOUCHDOWN:
            self.entry_spindle_depth = self.node.subs.drill_state.spindle_depth
            self.node.nominal_air_pres = None
            self.node.set_water(self.node.current_params.overburden_pass.water_ctrl)

    def do_work(self):
        # reference to the params from node's current_params; might be updated based on the current profile
        overburden_params = self.node.current_params.overburden_pass

        # nominal air pressure is set to maximum observed during the overburden pass
        if self.node.nominal_air_pres is None or \
                self.node.nominal_air_pres < self.node.subs.pressure_state.air_pressure:
            self.node.nominal_air_pres = self.node.subs.pressure_state.air_pressure

        # controls
        self.node.set_water(overburden_params.water_ctrl)
        self.node.controls.feed_speed = overburden_params.feed_speed_ctrl

        # smooth spin-up based on elapsed time
        srs = overburden_params.rotation_speed_ctrl
        elapsed_time_fraction = self.node.get_current_state_duration() / overburden_params.spinup_time
        self.node.set_safe_rotation(min(srs, elapsed_time_fraction * srs))

        self.node.controls.feed_pressure = overburden_params.feed_pressure_ctrl
        self.node.controls.air_power = overburden_params.air_power_ctrl
        self.node.controls.dust_collector_on = overburden_params.dust_ctrl

        # state transitions
        if self.node.is_hard_rot:
            self.node.set_state(HARD_ROT)

        depth_delta = self.node.subs.drill_state.spindle_depth - self.entry_spindle_depth
        if self.node.get_current_state_duration() > overburden_params.max_duration or \
                depth_delta >= overburden_params.overburden_layer_thickness:
            self.node.log("Nominal air pressure set to %.2f bar" % self.node.nominal_air_pres)
            self.node.set_state(DRILLING)


class DrillingState(AbstractState):
    def __init__(self, node):
        super(DrillingState, self).__init__(node, DRILLING)
        self.last_fast_drill_ts = 0
        self.high_rp_last_time = 0
        self.drilling_speed_error_i = 0
        self.prev_drill_ts = rospy.get_time()
        self.last_air_not_nominal_time = 0
        self.entry_spindle_depth = None
        self.increase_press_slowly = True

    def on_transition_to(self):
        self.last_fast_drill_ts = 0
        self.high_rp_last_time = 0
        self.drilling_speed_error_i = 0
        self.prev_drill_ts = rospy.get_time()
        self.last_air_not_nominal_time = 0
        self.increase_press_slowly = True

        self.entry_spindle_depth = self.node.subs.drill_state.spindle_depth

    def get_value_for_current_depth(self, depth_dict, default=None):
        """
        Retrieve the corresponding value for current spindle depth from the dictionary
        (doing geometry conversions to spindle depth according origin of each depth entry in depth_dict
        and taking into account first_shaft_flag and last_shaft_flag).

        Parameters:
            depth_dict (list of dict): List containing depth thresholds and corresponding values.

        Returns:
            float: The value corresponding to the current spindle depth.
        """

        # Adjust the depths to a one reference point (ground level)
        # and sort the list by threshold
        adjusted_depth_dict = []
        for entry in depth_dict:
            depth_entry = entry.depth

            # Adjust the threshold depending on the type of reference (from ground or finish, etc).
            if hasattr(depth_entry, 'from_ground'):
                # Just for debug message on profiles wrong value for from_ground
                shaft_id = self.node.vehicle_params['shaft_list'][0]
                shaft_len_params = self.node.vehicle_params['shaft_len_params'][shaft_id]
                shaft_len = shaft_len_params['full_len']
                drill_bit_len = self.node.vehicle_params['drill_bit_len']
                extension_len = self.node.vehicle_params['extension_len']
                string_len = shaft_len + drill_bit_len + extension_len
                platform_height = self.node.vehicle_params['geometry']['platform_height']
                average_level_height = self.node.vehicle_params['geometry']['average_level_height']
                max_depth_from_ground = string_len - platform_height - average_level_height
                if depth_entry.from_ground > max_depth_from_ground:
                    self.node.log(
                        "From_ground entry would be skipped, as %f exceeds max depth from ground %f. "
                        "Check drilling profiles. Take into account, that from_ground is applied "
                        "to the first shaft." % (depth_entry.from_ground,
                                                 max_depth_from_ground))
                if self.node.first_shaft_flag:
                    threshold_spindle_depth = self.node.ground_spindle_depth + depth_entry.from_ground  # Accessing as attribute
                else:
                    # we need to leave it (not skip), but move up (in such a way last value would be used,
                    # if no other entries exist)
                    # tower_height is used here just as some reasonably big length
                    threshold_spindle_depth = depth_entry.from_ground - self.node.vehicle_params['geometry']['tower_height']

            elif hasattr(depth_entry, 'from_added_shaft_start'):
                if not self.node.first_shaft_flag:
                    threshold_spindle_depth = self.node.action_start_spindle_depth + depth_entry.from_added_shaft_start
                    if depth_entry.from_added_shaft_start >= self.node.vehicle_params['geometry']['tower_height']:
                        self.node.log("Check from_added_shaft_start entry in drilling profiles, "
                                      "as its depth %f is too big." % depth_entry.from_added_shaft_start)
                else:
                    continue  # Skip this entry if conditions are not met

            elif hasattr(depth_entry, 'from_shaft_finish'):
                if not self.node.last_shaft_flag:
                    threshold_spindle_depth = self.node.target_drill_spindle_depth - depth_entry.from_shaft_finish
                else:
                    continue  # Skip this entry

            elif hasattr(depth_entry, 'from_hole_finish'):
                if self.node.last_shaft_flag:
                    threshold_spindle_depth = self.node.target_drill_spindle_depth - depth_entry.from_hole_finish
                else:
                    continue  # Skip this entry

            else:
                self.node.log(f"Unsupported depth reference in the depth specification {depth_entry}.", loglevel=2)
                # raise ValueError("Unsupported depth reference in the depth_entry.")

            adjusted_depth_dict.append({'threshold_spindle_depth': threshold_spindle_depth, 'value': entry.value})

        # Sort the adjusted depth dictionary based on the threshold_spindle_depth.
        adjusted_depth_dict.sort(key=lambda x: x['threshold_spindle_depth'])

        # Find the appropriate value based on the sorted and adjusted thresholds
        if default is None:
            last_value = adjusted_depth_dict[0]['value']
            if self.node.subs.drill_state.spindle_depth < adjusted_depth_dict[0]['threshold_spindle_depth']:
                self.node.logwarn("The current spindle depth is less than the lowest spindle depth in the depth_dict. "
                                  "Verify settings in the profile. Use the first value from dict for now.", period = 3)
        else:
            last_value = default
        for entry in adjusted_depth_dict:
            if self.node.subs.drill_state.spindle_depth < entry['threshold_spindle_depth']:
                break
            last_value = entry['value']

        return last_value

    def get_pullup_action_for_depth(self, entry_spindle_depth, pullup_dict,
                                    last_mandatory_pullup_spindle_depth):
        """
        Determine the pull-up action required for the current spindle depth based on the provided pull-up dictionary
        (doing geometry conversions to spindle depth according origin of each depth entry in pullup_dict
        and taking into account first_shaft_flag and last_shaft_flag).

        Parameters:
            entry_spindle_depth (float): The spindle depth at entry into the drilling state.
            pullup_dict (list of dict): List containing depth thresholds and corresponding pull-up actions.
            last_mandatory_pullup_spindle_depth (float): The spindle depth of the last mandatory pull-up.

        Returns:
            str or None: The pull-up action corresponding to the given depth, or None if no action is required.
        """

        # self.node.log("get_pullup_action_for_depth called with:")
        # self.node.log("  current spindle_depth: %f" % self.node.subs.drill_state.spindle_depth)
        # self.node.log("  entry_spindle_depth: %f" % entry_spindle_depth)
        # self.node.log("  last_mandatory_pullup_spindle_depth: %f" % last_mandatory_pullup_spindle_depth)
        # self.node.log("  hole_target_depth: %f" % self.node.hole_target_depth)
        # self.node.log("  target_drill_spindle_depth: %f" % self.node.target_drill_spindle_depth)

        # Adjust the depth thresholds to a common reference point and sort the list by threshold
        adjusted_pullup_dict = []
        for item in pullup_dict:
            depth_data = item.depth

            # Adjust the threshold depending on the type of reference.
            if hasattr(depth_data, 'from_ground'):
                if self.node.first_shaft_flag:
                    threshold_spindle_depth = self.node.ground_spindle_depth + depth_data.from_ground

                    # Just for debug message on profiles wrong value for from_ground
                    shaft_id = self.node.vehicle_params['shaft_list'][0]
                    shaft_len_params = self.node.vehicle_params['shaft_len_params'][shaft_id]
                    shaft_len = shaft_len_params['full_len']
                    drill_bit_len = self.node.vehicle_params['drill_bit_len']
                    extension_len = self.node.vehicle_params['extension_len']
                    string_len = shaft_len + drill_bit_len + extension_len
                    platform_height = self.node.vehicle_params['geometry']['platform_height']
                    average_level_height = self.node.vehicle_params['geometry']['average_level_height']
                    max_depth_from_ground = string_len - platform_height - average_level_height
                    if depth_data.from_ground > max_depth_from_ground:
                        self.node.log(
                            "Pullup from_ground entry would be skipped, as %f exceeds max depth from ground %f. "
                            "Check drilling profiles. Take into account, that from_ground is applied "
                            "to the first shaft." % (depth_data.from_ground,
                                                     max_depth_from_ground))
                else:
                    continue  # Skip this entry
            elif hasattr(depth_data, 'from_added_shaft_start'):
                if not self.node.first_shaft_flag:
                    threshold_spindle_depth = self.node.action_start_spindle_depth + depth_data.from_added_shaft_start
                    if depth_data.from_added_shaft_start >= self.node.vehicle_params['geometry']['tower_height']:
                        self.node.log("Pullup from_added_shaft_start entry would be skipped, as its depth %f "
                                      "is too big. Check drilling profiles" % depth_data.from_added_shaft_start)
                else:
                    continue  # Skip this entry if conditions are not met

            elif hasattr(depth_data, 'from_shaft_finish'):
                if not self.node.last_shaft_flag:
                    threshold_spindle_depth = self.node.target_drill_spindle_depth - depth_data.from_shaft_finish
                else:
                    continue  # Skip

            elif hasattr(depth_data, 'from_hole_finish'):
                if self.node.last_shaft_flag:
                    threshold_spindle_depth = self.node.target_drill_spindle_depth - depth_data.from_hole_finish
                else:
                    continue  # Skip

            elif hasattr(depth_data, 'drilled'):
                threshold_spindle_depth = entry_spindle_depth + depth_data.drilled
            else:
                self.node.log(f"Unsupported depth reference in the depth specification {depth_data}.", loglevel=2)
                # raise ValueError("Unsupported depth reference in the depth specification.")

            adjusted_pullup_dict.append({'threshold_spindle_depth': threshold_spindle_depth, 'action': item.action})

        # Sort the adjusted pull-up dictionary based on the threshold.
        adjusted_pullup_dict.sort(key=lambda x: x['threshold_spindle_depth'])

        # Default value for last_mandatory_pullup_spindle_depth if it's None.
        if last_mandatory_pullup_spindle_depth is None:
            last_mandatory_pullup_spindle_depth = 0.0

        # self.node.log("  last_mandatory_pullup_spindle_depth: %f" % last_mandatory_pullup_spindle_depth)
        # self.node.log("  Sorted pull-up list: %s" % str(adjusted_pullup_dict))

        # Find the appropriate action based on the sorted and adjusted thresholds
        for item in adjusted_pullup_dict:
            if last_mandatory_pullup_spindle_depth < item['threshold_spindle_depth'] <= self.node.subs.drill_state.spindle_depth:
                return item['action']

        # If no action thresholds match, return None.
        return None

    def do_work(self):
    # references to the params from node's current_params; might be updated based on the current profile
        drilling_params = self.node.current_params.drilling
        common_params = self.node.current_params.common

    # Update timestamp
        dt = rospy.get_time() - self.prev_drill_ts
        self.prev_drill_ts = rospy.get_time()

    # checks if we reached the target depth
        if self.node.subs.drill_state.spindle_depth >= self.node.target_drill_spindle_depth:
            self.node.set_state(WAIT_AFTER_DRILL)
            return

    # mandatory pullups
        pullup_action = self.get_pullup_action_for_depth(
            self.entry_spindle_depth,
            drilling_params.mandatory_pullups,
            self.node.last_mandatory_pullup_spindle_depth
        )
        if pullup_action:
            self.node.log("Starting %s pullup at the spindle depth: %f" % (pullup_action,
                                                                           self.node.subs.drill_state.spindle_depth))
            # Update the last_mandatory_pullup_spindle_depth value.
            self.node.last_mandatory_pullup_spindle_depth = self.node.subs.drill_state.spindle_depth
            if pullup_action == 'long':
                self.node.set_state(PULLUP, long=True)
            elif pullup_action == 'short':
                self.node.set_state(PULLUP, short=True)
            elif pullup_action == 'cracked':    # TODO: do we start cracked pullups only after some depth?
                self.node.set_state(PULLUP, cracked=True)

    # check pullup is needed (by check_if_pullup_needed())
        if self.node.check_if_pullup_needed():
            self.node.set_state(PULLUP)
            return

    # water control
        water_ctrl = self.get_value_for_current_depth(drilling_params.water_ctrl)
        self.node.set_water(water_ctrl)

    # check drilling speed
        if self.node.drill_speed_smoothed > drilling_params.low_drill_speed_thr:
            self.last_fast_drill_ts = rospy.get_time()

    # check air pressure is nominal
        if not self.node.check_air_is_nominal():
            self.last_air_not_nominal_time = rospy.get_time()

    # feed pressure reduce
        # integral of the drill speed error
        drilling_speed_error = drilling_params.target_drill_speed - self.node.drill_speed_smoothed
        self.drilling_speed_error_i += drilling_speed_error * dt * drilling_params.drilling_speed_i_gain

        # clamp integral of the drill speed error
        if self.drilling_speed_error_i > drilling_params.drilling_speed_i_max:
            self.drilling_speed_error_i = drilling_params.drilling_speed_i_max
        elif self.drilling_speed_error_i < drilling_params.drilling_speed_i_min:
            self.drilling_speed_error_i = drilling_params.drilling_speed_i_min

        # calculate psp_regulated
        psp_regulated = self.node.node_params['user_feed_pressure'] + self.drilling_speed_error_i
        if psp_regulated < drilling_params.feed_pressure_min:
            psp_regulated = drilling_params.feed_pressure_min

        # rotation pressure check
        if self.node.subs.pressure_state.rotation_pressure > drilling_params.high_rp_threshold:
            self.high_rp_last_time = rospy.get_time()

        if rospy.get_time() - self.high_rp_last_time < drilling_params.high_rp_duration_threshold:
            psp_regulated = min(drilling_params.feed_pressure_reduced, psp_regulated)

        # limit `psp_regulated` based on the current depth
        max_pd = self.get_value_for_current_depth(drilling_params.feed_pressure_limit)
        psp_regulated = min(psp_regulated, max_pd)

        # slow pressure increase
        if self.increase_press_slowly:
            if self.node.controls.feed_pressure < drilling_params.feed_pressure_min:
                self.node.controls.feed_pressure = drilling_params.feed_pressure_min

            psp_slow = self.node.controls.feed_pressure + drilling_params.slow_press_increase_rate * dt
            if psp_slow >= psp_regulated:
                self.increase_press_slowly = False
            self.node.controls.feed_pressure = min(psp_regulated, psp_slow)
        else:
            self.node.controls.feed_pressure = psp_regulated

    # feed speed control
        self.node.controls.feed_speed = drilling_params.feed_speed_ctrl

    # rotation speed control
        max_rot = self.get_value_for_current_depth(drilling_params.rotation_speed_limit)
        self.node.set_safe_rotation(min(max_rot, self.node.node_params['user_rotation_speed']))

    # air power control
        self.node.controls.air_power = drilling_params.air_power_ctrl
    # dust control
        self.node.controls.dust_collector_on = drilling_params.dust_ctrl

    # hard rotation check
        if self.node.is_hard_rot:
            self.node.set_state(HARD_ROT)


class HardRotState(AbstractState):
    def __init__(self, node):
        super(HardRotState, self).__init__(node, HARD_ROT, prev_state_saving_flag=False)
        self.entry_spindle_depth = 0

    def on_transition_to(self):
        self.entry_spindle_depth = self.node.subs.drill_state.spindle_depth
        self.node.log("Too hard rotation! recovering..", loglevel=2)

    def do_work(self):
        # references to the params from node's current_params; might be updated based on the current profile
        hard_rot_params = self.node.current_params.hard_rot

        # controls
        self.node.controls.feed_speed = -hard_rot_params.feed_speed_ctrl

        if rospy.get_time() - self.node.last_unstuck_time > self.node.current_params.common.unstuck_recency_limit:
            self.node.controls.rotation_speed = hard_rot_params.rotation_speed_normal_ctrl
        else:
            self.node.controls.rotation_speed = hard_rot_params.rotation_speed_after_unstuck_ctrl

        self.node.controls.dust_collector_on = hard_rot_params.dust_ctrl
        self.node.controls.air_power = hard_rot_params.air_power_ctrl
        self.node.controls.feed_pressure = hard_rot_params.feed_pressure_ctrl

        # transition to previous state
        depth_delta_exceeded = self.entry_spindle_depth - self.node.subs.drill_state.spindle_depth >= hard_rot_params.depth_delta
        rotation_speed_ok = self.node.subs.drill_state.rotation_speed > hard_rot_params.min_rotation_speed
        rotation_pressure_ok = self.node.subs.pressure_state.rotation_pressure < hard_rot_params.max_rotation_pressure

        if depth_delta_exceeded or rotation_speed_ok or rotation_pressure_ok or not self.node.is_in_hole():
            prev_state_name = self.node.get_last_state().get_name()
            self.node.set_state(prev_state_name)


class PullUpState(AbstractState):
    def __init__(self, node):
        super(PullUpState, self).__init__(node, PULLUP)
        self.entry_spindle_depth = None
        self.short_pullup_distance = 0

    def on_transition_to(self):
        self.entry_spindle_depth = self.node.subs.drill_state.spindle_depth

        if self.node.saved_water_restored:
            self.node.saved_water_ctrl = self.node.subs.water_ctrl.value
            self.node.saved_water_restored = False
        self.node.set_water(0, remember=False)

    def is_clear(self):
        return self.node.subs.pressure_state.rotation_pressure < self.node.current_params.pullup.free_rotation_pressure and \
               self.node.subs.pressure_state.air_pressure < self.node.current_params.pullup.air_pressure_max

    def do_work(self):
        # references to the params from node's current_params; might be updated based on the current profile
        pullup_params = self.node.current_params.pullup

        # set short_pullup_distance according to current depth
        if self.node.first_shaft_flag:
                entry_depth_from_ground = self.entry_spindle_depth - self.node.ground_spindle_depth
                if entry_depth_from_ground < self.node.current_params.pullup.depth_threshold:
                    self.short_pullup_distance = self.node.current_params.pullup.short_pullup_height_low
                else:
                    self.short_pullup_distance = self.node.current_params.pullup.short_pullup_height_high
        else:
            self.short_pullup_distance = self.node.current_params.pullup.short_pullup_height_high


        # controls
        if rospy.get_time() - self.node.last_unstuck_time > self.node.current_params.common.unstuck_recency_limit:
            self.node.set_safe_rotation(pullup_params.rotation_speed_normal_ctrl)
        else:
            self.node.set_safe_rotation(pullup_params.rotation_speed_after_unstuck_ctrl)

        # set normal or reduced feed_speed according to current depth
        if self.node.first_shaft_flag:
            current_depth_from_ground = self.node.subs.drill_state.spindle_depth - self.node.ground_spindle_depth
            if current_depth_from_ground > pullup_params.slow_pullup_depth:
                self.node.controls.feed_speed = -pullup_params.feed_speed_normal_ctrl
            else:
                self.node.controls.feed_speed = -pullup_params.feed_speed_reduced_ctrl
        else:
            self.node.controls.feed_speed = -pullup_params.feed_speed_normal_ctrl

        self.node.controls.dust_collector_on = pullup_params.dust_ctrl

        self.node.controls.air_power = pullup_params.air_power_normal_ctrl
        # Turn off compressor, if near wellhead
        if self.node.first_shaft_flag:
            current_depth_from_ground = self.node.subs.drill_state.spindle_depth - self.node.ground_spindle_depth
            if current_depth_from_ground < pullup_params.pullup_compressor_on_to_ground_limit:
                self.node.controls.air_power = 0

        self.node.controls.feed_pressure = pullup_params.feed_pressure_ctrl

        # state transitions
        if not self.node.is_in_hole(max_distance=self.node.current_params.pullup.pullup_to_ground_limit):
            self.node.short_pullup_cnt = 0
            self.node.log("Reset short pullup counter, because reached pullup limit")
            self.node.set_state(AFTER_PULLUP)
            return

        depth_delta = self.entry_spindle_depth - self.node.subs.drill_state.spindle_depth

        do_short_pullup = False
        # explicitly set short
        if self.last_transition_kwargs.get('short'):
            do_short_pullup = True
        # normal (not explicitly set long) and short pullup counter below limit
        if not self.last_transition_kwargs.get('long') and self.node.short_pullup_cnt < pullup_params.max_short_pullup_cnt:
            do_short_pullup = True

        if do_short_pullup and self.is_clear() and depth_delta > self.short_pullup_distance:
            # reached depth delta to exit state for short pullup
            self.node.short_pullup_cnt += 1
            self.node.log("short pullups counter incremented: %d" % self.node.short_pullup_cnt)
            self.node.set_state(AFTER_PULLUP)
            return

        if self.node.is_stuck:
            self.node.set_state(UNSTUCK)


class AfterPullUpState(AbstractState):
    def __init__(self, node):
        super(AfterPullUpState, self).__init__(node, AFTER_PULLUP)
        self.ground_detect_time_started = rospy.get_time()

    def on_transition_to(self):
        self.ground_detect_time_started = rospy.get_time()
        self.node.set_water(0, remember=False)

    def do_work(self):
        # references to the params from node's current_params; might be updated based on the current profile
        after_pullup_params = self.node.current_params.after_pullup
        common_params = self.node.current_params.common

        # controls
        self.node.controls.feed_speed = after_pullup_params.feed_speed_ctrl
        self.node.controls.feed_pressure = after_pullup_params.feed_pressure_ctrl_raw

        self.node.set_safe_rotation(after_pullup_params.rotation_speed_ctrl)

        self.node.controls.dust_collector_on = after_pullup_params.dust_ctrl
        self.node.controls.air_power = after_pullup_params.air_power_ctrl

        # ground detection and nominal_air_pres update
        if self.node.subs.drill_state.feed_speed > common_params.ground_detect_feed_speed_thr or \
                self.node.subs.pressure_state.rotation_pressure < after_pullup_params.ground_detect_rot_pres:
            self.ground_detect_time_started = rospy.get_time()

            # nominal_air_pres update
            current_air_pressure = self.node.subs.pressure_state.air_pressure
            if (self.node.nominal_air_pres is None or self.node.nominal_air_pres < current_air_pressure) \
                    and current_air_pressure < common_params.max_air_pressure - 0.5: # TODO: is 0.5 ok?
                self.node.nominal_air_pres = current_air_pressure

        # state transition
        if rospy.get_time() - self.ground_detect_time_started > common_params.ground_detect_duration:
            self.node.log("Nominal air pressure set to %.2f bar" % self.node.nominal_air_pres)
            self.node.set_water(self.node.saved_water_ctrl, remember=False)
            self.node.saved_water_restored = True
            self.node.set_state(PASS_SOFT)


class RaiseState(AbstractState):
    def __init__(self, node):
        super(RaiseState, self).__init__(node, RAISE)

    def on_transition_to(self):
        self.node.set_water(0)

    def do_work(self):
        # references to the params from node's current_params; might be updated based on the current profile
        raise_params = getattr(self.node.current_params, 'raise')   # TODO: raise is reserved, rename to raise_state?

        # depths convenient variables
        raise_depth_err = self.node.target_raise_spindle_depth - self.node.subs.drill_state.spindle_depth

        stop_air = not self.node.is_in_hole(raise_params.stop_air_depth)
        stop_rotation = not self.node.is_in_hole(raise_params.stop_air_depth)
        # controls

        if stop_rotation:
            self.node.controls.rotation_speed = 0
        else:
            if rospy.get_time() - self.node.last_unstuck_time > self.node.current_params.common.unstuck_recency_limit:
                self.node.controls.rotation_speed = raise_params.rotation_speed_normal_ctrl
            else:
                self.node.controls.rotation_speed = raise_params.rotation_speed_after_unstuck_ctrl

        if stop_air:
            self.node.controls.air_power = 0
        else:
            self.node.controls.air_power = 1

        self.node.controls.feed_pressure = raise_params.feed_pressure_ctrl
        self.node.controls.dust_collector_on = raise_params.dust_ctrl

        if not self.node.subs.drill_state.is_valid:     # TODO: check in main?
            self.node.log("Drill state is not valid during raising, ignoring", loglevel=2)
            return

        # Determine feed speed control based on the error in depth to target
        if self.node.subs.drill_state.spindle_depth >= self.node.target_raise_spindle_depth:
            if abs(raise_depth_err) > raise_params.reduced_speed_zone:
                self.node.controls.feed_speed = -abs(raise_params.max_raise_feed_speed)
            else:
                self.node.controls.feed_speed = -abs(raise_params.min_raise_feed_speed)

            if self.node.is_stuck:
                self.node.set_state(UNSTUCK)
        else:
            self.node.controls.rotation_speed = 0
            self.node.controls.dust_collector_on = False
            self.node.controls.air_power = 0
            self.node.controls.feed_pressure = 0
            self.node.set_state(IDLE)


class WaitAfterDrillState(AbstractState):
    def __init__(self, node):
        super(WaitAfterDrillState, self).__init__(node, WAIT_AFTER_DRILL)

    def do_work(self):
        wait_after_drill_params = self.node.current_params.wait_after_drill

        # controls
        self.node.controls.feed_speed = wait_after_drill_params.feed_speed_ctrl
        self.node.controls.feed_pressure = wait_after_drill_params.feed_pressure_ctrl
        self.node.controls.rotation_speed = wait_after_drill_params.rotation_speed_ctrl
        self.node.controls.dust_collector_on = wait_after_drill_params.dust_ctrl
        self.node.controls.air_power = wait_after_drill_params.air_power_ctrl

        # state transition
        if self.node.get_current_state_duration() > wait_after_drill_params.max_duration:
            self.node.set_state(RAISE)


class PassSoftState(AbstractState):
    def __init__(self, node):
        super(PassSoftState, self).__init__(node, PASS_SOFT)

    def do_work(self):
        pass_soft_params = self.node.current_params.pass_soft

        self.node.controls.feed_pressure = pass_soft_params.feed_pressure_ctrl
        self.node.set_safe_rotation(pass_soft_params.rotation_speed_ctrl)
        self.node.controls.dust_collector_on = pass_soft_params.dust_ctrl
        self.node.controls.air_power = pass_soft_params.air_power_ctrl

        # state transition
        if self.node.check_if_pullup_needed():
            self.node.set_state(PULLUP)
            return

        if self.node.subs.drill_state.spindle_depth >= self.node.target_drill_spindle_depth:
            self.node.set_state(WAIT_AFTER_DRILL)
            return

        if self.node.get_current_state_duration() > pass_soft_params.max_duration:
            self.node.set_state(DRILLING)


class UnstuckState(AbstractState):
    def __init__(self, node):
        super(UnstuckState, self).__init__(node, UNSTUCK, prev_state_saving_flag=True)
        self.entry_spindle_depth = None

    def on_transition_to(self):
        self.entry_spindle_depth = self.node.subs.drill_state.spindle_depth

        if not self.node.current_params.common.enable_unstuck:
            err_msg = "Unstuck is disabled!"
            err_type = self.node.global_params['Reports']['Critical']['Action_failed']
            self.node.handle_internal_error(error_message=err_msg,
                                            error_type=err_type,
                                            event=self.node.global_params['events']['rc_unstuck'])

    def do_work(self):
        unstuck_params = self.node.current_params.unstuck
        self.node.last_unstuck_time = rospy.get_time()

        time_trying = self.node.get_current_state_duration()
        full_cycle_duration = unstuck_params.unstuck_time_up + unstuck_params.unstuck_time_down
        cycles_cnt = time_trying / full_cycle_duration
        cycle_period = time_trying % full_cycle_duration

        # controls
        self.node.controls.feed_pressure = unstuck_params.feed_pressure_ctrl
        self.node.controls.rotation_speed = unstuck_params.rotation_speed_ctrl
        self.node.controls.air_power = unstuck_params.air_power_ctrl

        if not self.node.is_in_hole() or abs(self.entry_spindle_depth - self.node.subs.drill_state.spindle_depth) >= unstuck_params.unstuck_dist: #moved enough
            prev_state_name = self.node.get_last_state().get_name()
            self.node.set_state(prev_state_name)
            self.node.log("unstucking up finished ok")
            return

        if cycle_period <= unstuck_params.unstuck_time_up:
            self.node.controls.feed_speed = -unstuck_params.unstuck_up_feed_speed_ctrl
        else:
            self.node.controls.feed_speed = unstuck_params.unstuck_down_feed_speed_ctrl
        
        if cycles_cnt >= unstuck_params.unstuck_try_cnt: #unstucking failed
            prev_state_name = self.node.get_last_state().get_name()
            self.node.set_state(prev_state_name)
            #switch to prev state and throw error
            err_msg = "Unstuck failed"
            err_type = self.node.global_params['Reports']['Errors']['Internal_error']
            rospy.logerr(err_msg)
            self.node.handle_internal_error(error_message=err_msg, error_type=err_type)
            return
